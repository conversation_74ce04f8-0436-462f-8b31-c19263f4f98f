.class public final Lcom/google/android/gms/internal/ads/zzcoi;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzgwy;


# instance fields
.field private final zza:Lcom/google/android/gms/internal/ads/zzcoh;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzcoh;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzcoi;->zza:Lcom/google/android/gms/internal/ads/zzcoh;

    return-void
.end method


# virtual methods
.method public final zza()Landroid/view/ViewGroup;
    .locals 1
    .annotation build Lo00oOo00/Class7651;
    .end annotation

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcoi;->zza:Lcom/google/android/gms/internal/ads/zzcoh;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzcoh;->zza()Landroid/view/ViewGroup;

    move-result-object v0

    return-object v0
.end method

.method public final synthetic zzb()Ljava/lang/Object;
    .locals 1
    .annotation build Lo00oOo00/Class7651;
    .end annotation

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcoi;->zza:Lcom/google/android/gms/internal/ads/zzcoh;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzcoh;->zza()Landroid/view/ViewGroup;

    move-result-object v0

    return-object v0
.end method
