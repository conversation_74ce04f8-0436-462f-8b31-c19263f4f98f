.class public final Lo0OOO0OO/o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final o00oOOo0:Lcom/github/megatronking/stringfog/xor/StringFogImpl;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/github/megatronking/stringfog/xor/StringFogImpl;

    invoke-direct {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;-><init>()V

    sput-object v0, Lo0OOO0OO/o00oOOoO;->o00oOOo0:Lcom/github/megatronking/stringfog/xor/StringFogImpl;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static o00oOOo0([B[B)Ljava/lang/String;
    .locals 1

    sget-object v0, Lo0OOO0OO/o00oOOoO;->o00oOOo0:Lcom/github/megatronking/stringfog/xor/StringFogImpl;

    invoke-virtual {v0, p0, p1}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt([B[B)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method
