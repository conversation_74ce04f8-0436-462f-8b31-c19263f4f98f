.class public final Lo0OOo0O/o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final o00oOOo0:Ljava/lang/String; = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"

.field public static final o00oOOoO:I = 0x80

.field public static final o00oOo00:I = 0x40

.field public static final o00oOo0O:I = 0x10

.field public static final o00oOo0o:I = 0xf

.field public static final o00oOoO:I = 0x80

.field public static final o00oOoO0:I = 0x8

.field public static final o00oOoOO:I = 0x7f

.field public static final o00oOoOo:I = 0x0

.field public static final o00oOoo0:I = 0x1

.field public static final o00oOooO:I = 0x20

.field public static final o00oOooo:I = 0x2

.field public static final o00oo:I = 0x3ed

.field public static final o00oo0:I = 0x9

.field public static final o00oo00O:I = 0x8

.field public static final o00oo0O:J = 0x7bL

.field public static final o00oo0O0:J = 0x7dL

.field public static final o00oo0OO:I = 0xa

.field public static final o00oo0Oo:I = 0x7e

.field public static final o00oo0o:I = 0x7f

.field public static final o00oo0o0:J = 0xffffL

.field public static final o00oo0oO:I = 0x3e9

.field public static final o00ooO00:I = 0x3ee

.field public static final o0O0o:I = 0x3ea


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/lang/AssertionError;

    const-string v1, "No instances."

    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0
.end method

.method public static o00oOOo0(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p0, "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object p0

    invoke-virtual {p0}, Lokio/o00oo00O;->sha1()Lokio/o00oo00O;

    move-result-object p0

    invoke-virtual {p0}, Lokio/o00oo00O;->base64()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static o00oOOoO(I)Ljava/lang/String;
    .locals 2

    const/16 v0, 0x3e8

    if-lt p0, v0, :cond_4

    const/16 v0, 0x1388

    if-lt p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/16 v0, 0x3ec

    if-lt p0, v0, :cond_1

    const/16 v0, 0x3ee

    if-le p0, v0, :cond_2

    :cond_1
    const/16 v0, 0x3f4

    if-lt p0, v0, :cond_3

    const/16 v0, 0xbb7

    if-gt p0, v0, :cond_3

    :cond_2
    const-string v0, "Code "

    const-string v1, " is reserved and may not be used."

    invoke-static {v0, p0, v1}, Lo00ooOO0/o0O000o0;->o00oOOo0(Ljava/lang/String;ILjava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    goto :goto_1

    :cond_3
    const/4 p0, 0x0

    return-object p0

    :cond_4
    :goto_0
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Code must be in range [1000,5000): "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-object p0, v0

    :goto_1
    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static o00oOo00([BJ[BJ)V
    .locals 4

    array-length v0, p3

    const/4 v1, 0x0

    :goto_0
    int-to-long v2, v1

    cmp-long v2, v2, p1

    if-gez v2, :cond_0

    int-to-long v2, v0

    rem-long v2, p4, v2

    long-to-int v2, v2

    aget-byte v3, p0, v1

    aget-byte v2, p3, v2

    xor-int/2addr v2, v3

    int-to-byte v2, v2

    aput-byte v2, p0, v1

    add-int/lit8 v1, v1, 0x1

    const-wide/16 v2, 0x1

    add-long/2addr p4, v2

    goto :goto_0

    :cond_0
    return-void
.end method

.method public static o00oOooO(I)V
    .locals 1

    invoke-static {p0}, Lo0OOo0O/o00oOo0O;->o00oOOoO(I)Ljava/lang/String;

    move-result-object p0

    if-nez p0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, p0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
