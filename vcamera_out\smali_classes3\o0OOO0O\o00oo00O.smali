.class public final Lo0OOo0O/o00oo00O;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo0O/o00oo00O$o00oOOo0;
    }
.end annotation


# static fields
.field public static final synthetic o00oOoOo:Z


# instance fields
.field public final o00oOOo0:Z

.field public final o00oOOoO:Ljava/util/Random;

.field public final o00oOo00:Lokio/o00oOo0O;

.field public final o00oOo0O:Lokio/o00oOo00;

.field public final o00oOo0o:Lo0OOo0O/o00oo00O$o00oOOo0;

.field public final o00oOoO:[B

.field public o00oOoO0:Z

.field public final o00oOoOO:[B

.field public o00oOooO:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(ZLokio/o00oOo0O;Ljava/util/Random;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lokio/o00oOo00;

    invoke-direct {v0}, Lokio/o00oOo00;-><init>()V

    iput-object v0, p0, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    new-instance v0, Lo0OOo0O/o00oo00O$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOo0O/o00oo00O$o00oOOo0;-><init>(Lo0OOo0O/o00oo00O;)V

    iput-object v0, p0, Lo0OOo0O/o00oo00O;->o00oOo0o:Lo0OOo0O/o00oo00O$o00oOOo0;

    if-eqz p2, :cond_3

    if-eqz p3, :cond_2

    iput-boolean p1, p0, Lo0OOo0O/o00oo00O;->o00oOOo0:Z

    iput-object p2, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    iput-object p3, p0, Lo0OOo0O/o00oo00O;->o00oOOoO:Ljava/util/Random;

    const/4 p2, 0x0

    if-eqz p1, :cond_0

    const/4 p3, 0x4

    new-array p3, p3, [B

    goto :goto_0

    :cond_0
    move-object p3, p2

    :goto_0
    iput-object p3, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    if-eqz p1, :cond_1

    const/16 p1, 0x2000

    new-array p2, p1, [B

    :cond_1
    iput-object p2, p0, Lo0OOo0O/o00oo00O;->o00oOoOO:[B

    return-void

    :cond_2
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "random == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "sink == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public o00oOOo0(IJ)Lokio/o0O00O0;
    .locals 2

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O;->o00oOoO0:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo0O/o00oo00O;->o00oOoO0:Z

    iget-object v1, p0, Lo0OOo0O/o00oo00O;->o00oOo0o:Lo0OOo0O/o00oo00O$o00oOOo0;

    iput p1, v1, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O0:I

    iput-wide p2, v1, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O:J

    iput-boolean v0, v1, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    const/4 p1, 0x0

    iput-boolean p1, v1, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o0:Z

    return-object v1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Another message writer is active. Did you call close()?"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOOoO(ILokio/o00oo00O;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    sget-object v0, Lokio/o00oo00O;->EMPTY:Lokio/o00oo00O;

    if-nez p1, :cond_0

    if-eqz p2, :cond_3

    :cond_0
    if-eqz p1, :cond_1

    invoke-static {p1}, Lo0OOo0O/o00oOo0O;->o00oOooO(I)V

    :cond_1
    new-instance v0, Lokio/o00oOo00;

    invoke-direct {v0}, Lokio/o00oOo00;-><init>()V

    invoke-virtual {v0, p1}, Lokio/o00oOo00;->o0O0ooo(I)Lokio/o00oOo00;

    if-eqz p2, :cond_2

    invoke-virtual {v0, p2}, Lokio/o00oOo00;->o0O0oOOO(Lokio/o00oo00O;)Lokio/o00oOo00;

    :cond_2
    invoke-virtual {v0}, Lokio/o00oOo00;->o0O0OooO()Lokio/o00oo00O;

    move-result-object v0

    :cond_3
    monitor-enter p0

    const/16 p1, 0x8

    const/4 p2, 0x1

    :try_start_0
    invoke-virtual {p0, p1, v0}, Lo0OOo0O/o00oo00O;->o00oOo00(ILokio/o00oo00O;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iput-boolean p2, p0, Lo0OOo0O/o00oo00O;->o00oOooO:Z

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    goto :goto_0

    :catchall_1
    move-exception p1

    iput-boolean p2, p0, Lo0OOo0O/o00oo00O;->o00oOooO:Z

    throw p1

    :goto_0
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public final o00oOo00(ILokio/o00oo00O;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O;->o00oOooO:Z

    if-nez v0, :cond_2

    invoke-virtual {p2}, Lokio/o00oo00O;->size()I

    move-result v0

    int-to-long v1, v0

    const-wide/16 v3, 0x7d

    cmp-long v1, v1, v3

    if-gtz v1, :cond_1

    or-int/lit16 p1, p1, 0x80

    iget-object v1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {v1, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-boolean p1, p0, Lo0OOo0O/o00oo00O;->o00oOOo0:Z

    if-eqz p1, :cond_0

    or-int/lit16 p1, v0, 0x80

    iget-object v0, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {v0, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOOoO:Ljava/util/Random;

    iget-object v0, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    invoke-virtual {p1, v0}, Ljava/util/Random;->nextBytes([B)V

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    iget-object v0, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    invoke-interface {p1, v0}, Lokio/o00oOo0O;->write([B)Lokio/o00oOo0O;

    invoke-virtual {p2}, Lokio/o00oo00O;->toByteArray()[B

    move-result-object p1

    array-length p2, p1

    int-to-long v2, p2

    iget-object v4, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    const-wide/16 v5, 0x0

    move-object v1, p1

    invoke-static/range {v1 .. v6}, Lo0OOo0O/o00oOo0O;->o00oOo00([BJ[BJ)V

    iget-object p2, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p2, p1}, Lokio/o00oOo0O;->write([B)Lokio/o00oOo0O;

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p1, v0}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p1, p2}, Lokio/o00oOo0O;->o0oOo0O0(Lokio/o00oo00O;)Lokio/o00oOo0O;

    :goto_0
    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p1}, Lokio/o00oOo0O;->flush()V

    return-void

    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Payload size must be less than or equal to 125"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    new-instance p1, Ljava/io/IOException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOo0O(Lokio/o00oo00O;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    const/16 v0, 0x9

    :try_start_0
    invoke-virtual {p0, v0, p1}, Lo0OOo0O/o00oo00O;->o00oOo00(ILokio/o00oo00O;)V

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public o00oOo0o(Lokio/o00oo00O;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    const/16 v0, 0xa

    :try_start_0
    invoke-virtual {p0, v0, p1}, Lo0OOo0O/o00oo00O;->o00oOo00(ILokio/o00oo00O;)V

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public o00oOooO(IJZZ)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O;->o00oOooO:Z

    if-nez v0, :cond_8

    const/4 v0, 0x0

    if-eqz p4, :cond_0

    goto :goto_0

    :cond_0
    move p1, v0

    :goto_0
    if-eqz p5, :cond_1

    or-int/lit16 p1, p1, 0x80

    :cond_1
    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p4, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-boolean p1, p0, Lo0OOo0O/o00oo00O;->o00oOOo0:Z

    if-eqz p1, :cond_2

    const/16 p1, 0x80

    goto :goto_1

    :cond_2
    move p1, v0

    :goto_1
    const-wide/16 p4, 0x7d

    cmp-long p4, p2, p4

    if-gtz p4, :cond_3

    long-to-int p4, p2

    or-int/2addr p1, p4

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p4, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    goto :goto_2

    :cond_3
    const-wide/32 p4, 0xffff

    cmp-long p4, p2, p4

    if-gtz p4, :cond_4

    or-int/lit8 p1, p1, 0x7e

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p4, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    long-to-int p4, p2

    invoke-interface {p1, p4}, Lokio/o00oOo0O;->writeShort(I)Lokio/o00oOo0O;

    goto :goto_2

    :cond_4
    or-int/lit8 p1, p1, 0x7f

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p4, p1}, Lokio/o00oOo0O;->writeByte(I)Lokio/o00oOo0O;

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p1, p2, p3}, Lokio/o00oOo0O;->writeLong(J)Lokio/o00oOo0O;

    :goto_2
    iget-boolean p1, p0, Lo0OOo0O/o00oo00O;->o00oOOo0:Z

    if-eqz p1, :cond_6

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOOoO:Ljava/util/Random;

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    invoke-virtual {p1, p4}, Ljava/util/Random;->nextBytes([B)V

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    invoke-interface {p1, p4}, Lokio/o00oOo0O;->write([B)Lokio/o00oOo0O;

    const-wide/16 p4, 0x0

    :goto_3
    cmp-long p1, p4, p2

    if-gez p1, :cond_7

    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOoOO:[B

    array-length p1, p1

    int-to-long v1, p1

    invoke-static {p2, p3, v1, v2}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v1

    long-to-int p1, v1

    iget-object v1, p0, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    iget-object v2, p0, Lo0OOo0O/o00oo00O;->o00oOoOO:[B

    invoke-virtual {v1, v2, v0, p1}, Lokio/o00oOo00;->read([BII)I

    move-result p1

    const/4 v1, -0x1

    if-eq p1, v1, :cond_5

    iget-object v1, p0, Lo0OOo0O/o00oo00O;->o00oOoOO:[B

    int-to-long v7, p1

    iget-object v4, p0, Lo0OOo0O/o00oo00O;->o00oOoO:[B

    move-wide v2, v7

    move-wide v5, p4

    invoke-static/range {v1 .. v6}, Lo0OOo0O/o00oOo0O;->o00oOo00([BJ[BJ)V

    iget-object v1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    iget-object v2, p0, Lo0OOo0O/o00oo00O;->o00oOoOO:[B

    invoke-interface {v1, v2, v0, p1}, Lokio/o00oOo0O;->write([BII)Lokio/o00oOo0O;

    add-long/2addr p4, v7

    goto :goto_3

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_6
    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    iget-object p4, p0, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    invoke-interface {p1, p4, p2, p3}, Lokio/o0O00O0;->o0O0000o(Lokio/o00oOo00;J)V

    :cond_7
    iget-object p1, p0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {p1}, Lokio/o00oOo0O;->o00oo0Oo()Lokio/o00oOo0O;

    return-void

    :cond_8
    new-instance p1, Ljava/io/IOException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
