.class public Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO$o00oo0O;->o00oOo00(ZIILjava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:Lo0OOo00/o00oo0O;

.field public final synthetic o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;Lo0OOo00/o00oo0O;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;

    iput-object p4, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0O:Lo0OOo00/o00oo0O;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 4

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0O:Lo0OOo00/o00oo0O;

    invoke-virtual {v0, v1}, Lo0OOo00/o00oo0OO$o00oo0O0;->o00oOo0o(Lo0OOo00/o00oo0O;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    invoke-static {}, Lo0OOo00o/o00oo0O0;->o00oOoO()Lo0OOo00o/o00oo0O0;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "Http2Connection.Listener failure for "

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v3, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;

    iget-object v3, v3, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v3, v3, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x4

    invoke-virtual {v1, v3, v2, v0}, Lo0OOo00o/o00oo0O0;->o00oo00O(ILjava/lang/String;Ljava/lang/Throwable;)V

    :try_start_1
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;->o00oo0O:Lo0OOo00/o00oo0O;

    sget-object v1, Lo0OOo00/o00oOo00;->PROTOCOL_ERROR:Lo0OOo00/o00oOo00;

    invoke-virtual {v0, v1}, Lo0OOo00/o00oo0O;->o00oOooO(Lo0OOo00/o00oOo00;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :goto_0
    return-void
.end method
