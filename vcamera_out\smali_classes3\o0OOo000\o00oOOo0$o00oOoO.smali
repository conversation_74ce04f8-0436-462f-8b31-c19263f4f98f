.class public final Lo0OOo000/o00oOOo0$o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0O00O0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOoO"
.end annotation


# instance fields
.field public o00oo0O:Z

.field public final o00oo0O0:Lokio/o00oo0O;

.field public o00oo0Oo:J

.field public final synthetic o00oo0o0:Lo0OOo000/o00oOOo0;


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;J)V
    .locals 1

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0o0:Lo0OOo000/o00oOOo0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lokio/o00oo0O;

    iget-object p1, p1, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {p1}, Lokio/o0O00O0;->o00oOooO()Lokio/o0O00O0o;

    move-result-object p1

    invoke-direct {v0, p1}, Lokio/o00oo0O;-><init>(Lokio/o0O00O0o;)V

    iput-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O0:Lokio/o00oo0O;

    iput-wide p2, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    return-void
.end method


# virtual methods
.method public close()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O:Z

    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-gtz v0, :cond_1

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0o0:Lo0OOo000/o00oOOo0;

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O0:Lokio/o00oo0O;

    invoke-virtual {v0, v1}, Lo0OOo000/o00oOOo0;->o00oOoO0(Lokio/o00oo0O;)V

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0o0:Lo0OOo000/o00oOOo0;

    const/4 v1, 0x3

    iput v1, v0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    return-void

    :cond_1
    new-instance v0, Ljava/net/ProtocolException;

    const-string v1, "unexpected end of stream"

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public flush()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0o0:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o00oOo0O;->flush()V

    return-void
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O0:Lokio/o00oo0O;

    return-object v0
.end method

.method public o0O0000o(Lokio/o00oOo00;J)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0O:Z

    if-nez v0, :cond_1

    iget-wide v1, p1, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v3, 0x0

    move-wide v5, p2

    invoke-static/range {v1 .. v6}, Lo0OOOoOo/o0O0000O;->o00oOOoO(JJJ)V

    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    cmp-long v0, p2, v0

    if-gtz v0, :cond_0

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0o0:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0, p1, p2, p3}, Lokio/o0O00O0;->o0O0000o(Lokio/o00oOo00;J)V

    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    sub-long/2addr v0, p2

    iput-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    return-void

    :cond_0
    new-instance p1, Ljava/net/ProtocolException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "expected "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-wide v1, p0, Lo0OOo000/o00oOOo0$o00oOoO;->o00oo0Oo:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v1, " bytes but received "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
