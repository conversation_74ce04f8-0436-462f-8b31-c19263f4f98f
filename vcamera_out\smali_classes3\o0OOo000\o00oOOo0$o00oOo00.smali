.class public final Lo0OOo000/o00oOOo0$o00oOo00;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0O00O0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOo00"
.end annotation


# instance fields
.field public o00oo0O:Z

.field public final o00oo0O0:Lokio/o00oo0O;

.field public final synthetic o00oo0Oo:Lo0OOo000/o00oOOo0;


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;)V
    .locals 1

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lokio/o00oo0O;

    iget-object p1, p1, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {p1}, Lokio/o0O00O0;->o00oOooO()Lokio/o0O00O0o;

    move-result-object p1

    invoke-direct {v0, p1}, Lokio/o00oo0O;-><init>(Lokio/o0O00O0o;)V

    iput-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O0:Lokio/o00oo0O;

    return-void
.end method


# virtual methods
.method public declared-synchronized close()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    const/4 v0, 0x1

    :try_start_1
    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O:Z

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    const-string v1, "0\r\n\r\n"

    invoke-interface {v0, v1}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O0:Lokio/o00oo0O;

    invoke-virtual {v0, v1}, Lo0OOo000/o00oOOo0;->o00oOoO0(Lokio/o00oo0O;)V

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    const/4 v1, 0x3

    iput v1, v0, Lo0OOo000/o00oOOo0;->o00oOo0o:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized flush()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o00oOo0O;->flush()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O0:Lokio/o00oo0O;

    return-object v0
.end method

.method public o0O0000o(Lokio/o00oOo00;J)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0O:Z

    if-nez v0, :cond_1

    const-wide/16 v0, 0x0

    cmp-long v0, p2, v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0, p2, p3}, Lokio/o00oOo0O;->o0O0Ooo(J)Lokio/o00oOo0O;

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    const-string v1, "\r\n"

    invoke-interface {v0, v1}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0, p1, p2, p3}, Lokio/o0O00O0;->o0O0000o(Lokio/o00oOo00;J)V

    iget-object p1, p0, Lo0OOo000/o00oOOo0$o00oOo00;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget-object p1, p1, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {p1, v1}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    return-void

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
