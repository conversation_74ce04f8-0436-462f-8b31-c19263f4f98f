.class public final Lo0OOo00/o00oo0O0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo00/o00oo0O0$o00oOOoO;,
        Lo0OOo00/o00oo0O0$o00oOOo0;
    }
.end annotation


# static fields
.field public static final o00oo0o:Ljava/util/logging/Logger;


# instance fields
.field public final o00oo0O:Lo0OOo00/o00oo0O0$o00oOOo0;

.field public final o00oo0O0:Lokio/o00oOoO;

.field public final o00oo0Oo:Z

.field public final o00oo0o0:Lo0OOo00/o00oOoO$o00oOOo0;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    const-class v0, Lo0OOo00/o00oo00O;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lo0OOo00/o00oo0O0;->o00oo0o:Ljava/util/logging/Logger;

    return-void
.end method

.method public constructor <init>(Lokio/o00oOoO;Z)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    iput-boolean p2, p0, Lo0OOo00/o00oo0O0;->o00oo0Oo:Z

    new-instance p2, Lo0OOo00/o00oo0O0$o00oOOo0;

    invoke-direct {p2, p1}, Lo0OOo00/o00oo0O0$o00oOOo0;-><init>(Lokio/o00oOoO;)V

    iput-object p2, p0, Lo0OOo00/o00oo0O0;->o00oo0O:Lo0OOo00/o00oo0O0$o00oOOo0;

    new-instance p1, Lo0OOo00/o00oOoO$o00oOOo0;

    const/16 v0, 0x1000

    invoke-direct {p1, v0, v0, p2}, Lo0OOo00/o00oOoO$o00oOOo0;-><init>(IILokio/o0OoO00O;)V

    iput-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0o0:Lo0OOo00/o00oOoO$o00oOOo0;

    return-void
.end method

.method public static o00oOOo0(IBS)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    and-int/lit8 p1, p1, 0x8

    if-eqz p1, :cond_0

    add-int/lit8 p0, p0, -0x1

    :cond_0
    if-gt p2, p0, :cond_1

    sub-int/2addr p0, p2

    int-to-short p0, p0

    return p0

    :cond_1
    const/4 p1, 0x2

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    invoke-static {p2}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object p2

    aput-object p2, p1, v0

    const/4 p2, 0x1

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, p1, p2

    const-string p0, "PROTOCOL_ERROR padding %s > remaining length %s"

    invoke-static {p0, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method public static o00oo00O(Lokio/o00oOoO;)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-interface {p0}, Lokio/o00oOoO;->readByte()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    shl-int/lit8 v0, v0, 0x10

    invoke-interface {p0}, Lokio/o00oOoO;->readByte()B

    move-result v1

    and-int/lit16 v1, v1, 0xff

    shl-int/lit8 v1, v1, 0x8

    or-int/2addr v0, v1

    invoke-interface {p0}, Lokio/o00oOoO;->readByte()B

    move-result p0

    and-int/lit16 p0, p0, 0xff

    or-int/2addr p0, v0

    return p0
.end method


# virtual methods
.method public close()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o0OoO00O;->close()V

    return-void
.end method

.method public o00oOOoO(ZLo0OOo00/o00oo0O0$o00oOOoO;)Z
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    const-wide/16 v2, 0x9

    invoke-interface {v1, v2, v3}, Lokio/o00oOoO;->o0O00o00(J)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    iget-object v1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-static {v1}, Lo0OOo00/o00oo0O0;->o00oo00O(Lokio/o00oOoO;)I

    move-result v1

    const/4 v2, 0x1

    if-ltz v1, :cond_3

    const/16 v3, 0x4000

    if-gt v1, v3, :cond_3

    iget-object v3, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v3}, Lokio/o00oOoO;->readByte()B

    move-result v3

    and-int/lit16 v3, v3, 0xff

    int-to-byte v3, v3

    if-eqz p1, :cond_1

    const/4 p1, 0x4

    if-ne v3, p1, :cond_0

    goto :goto_0

    :cond_0
    new-array p1, v2, [Ljava/lang/Object;

    invoke-static {v3}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "Expected a SETTINGS frame but was %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    :goto_0
    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p1}, Lokio/o00oOoO;->readByte()B

    move-result p1

    and-int/lit16 p1, p1, 0xff

    int-to-byte p1, p1

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readInt()I

    move-result v0

    const v4, 0x7fffffff

    and-int/2addr v0, v4

    sget-object v4, Lo0OOo00/o00oo0O0;->o00oo0o:Ljava/util/logging/Logger;

    sget-object v5, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-static {v2, v0, v1, v3, p1}, Lo0OOo00/o00oo00O;->o00oOOoO(ZIIBB)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->fine(Ljava/lang/String;)V

    :cond_2
    packed-switch v3, :pswitch_data_0

    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    int-to-long v0, v1

    invoke-interface {p1, v0, v1}, Lokio/o00oOoO;->skip(J)V

    goto :goto_1

    :pswitch_0
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oooo0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_1
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oOoO0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_2
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oo0OO(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_3
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00ooO0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_4
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oooOo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_5
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00ooOoo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_6
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00ooO00(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_7
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oOoOo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    goto :goto_1

    :pswitch_8
    invoke-virtual {p0, p2, v1, p1, v0}, Lo0OOo00/o00oo0O0;->o00oOo0o(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V

    :goto_1
    return v2

    :cond_3
    new-array p1, v2, [Ljava/lang/Object;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "FRAME_SIZE_ERROR: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :catch_0
    return v0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public o00oOo0O(Lo0OOo00/o00oo0O0$o00oOOoO;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo00/o00oo0O0;->o00oo0Oo:Z

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    invoke-virtual {p0, v2, p1}, Lo0OOo00/o00oo0O0;->o00oOOoO(ZLo0OOo00/o00oo0O0$o00oOOoO;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const-string p1, "Required SETTINGS preface not received"

    new-array v0, v1, [Ljava/lang/Object;

    invoke-static {p1, v0}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    sget-object v0, Lo0OOo00/o00oo00O;->o00oOOo0:Lokio/o00oo00O;

    invoke-virtual {v0}, Lokio/o00oo00O;->size()I

    move-result v3

    int-to-long v3, v3

    invoke-interface {p1, v3, v4}, Lokio/o00oOoO;->o0O0O0o0(J)Lokio/o00oo00O;

    move-result-object p1

    sget-object v3, Lo0OOo00/o00oo0O0;->o00oo0o:Ljava/util/logging/Logger;

    sget-object v4, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v3, v4}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v4

    if-eqz v4, :cond_2

    new-array v4, v2, [Ljava/lang/Object;

    invoke-virtual {p1}, Lokio/o00oo00O;->hex()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v4, v1

    const-string v5, "<< CONNECTION %s"

    invoke-static {v5, v4}, Lo0OOOoOo/o0O0000O;->o00oo00O(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/util/logging/Logger;->fine(Ljava/lang/String;)V

    :cond_2
    invoke-virtual {v0, p1}, Lokio/o00oo00O;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    :goto_0
    return-void

    :cond_3
    new-array v0, v2, [Ljava/lang/Object;

    invoke-virtual {p1}, Lokio/o00oo00O;->utf8()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v1

    const-string p1, "Expected a connection header but was %s"

    invoke-static {p1, v0}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oOo0o(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p4, :cond_4

    and-int/lit8 v1, p3, 0x1

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    move v1, v2

    goto :goto_0

    :cond_0
    move v1, v0

    :goto_0
    and-int/lit8 v3, p3, 0x20

    if-eqz v3, :cond_1

    goto :goto_1

    :cond_1
    move v2, v0

    :goto_1
    if-nez v2, :cond_3

    and-int/lit8 v2, p3, 0x8

    if-eqz v2, :cond_2

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readByte()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    int-to-short v0, v0

    :cond_2
    invoke-static {p2, p3, v0}, Lo0OOo00/o00oo0O0;->o00oOOo0(IBS)I

    move-result p2

    iget-object p3, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p1, v1, p4, p3, p2}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOoO(ZILokio/o00oOoO;I)V

    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    int-to-long p2, v0

    invoke-interface {p1, p2, p3}, Lokio/o00oOoO;->skip(J)V

    return-void

    :cond_3
    const-string p1, "PROTOCOL_ERROR: FLAG_COMPRESSED without SETTINGS_COMPRESS_DATA"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_4
    const-string p1, "PROTOCOL_ERROR: TYPE_DATA streamId == 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oOoO(ISBI)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ISBI)",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O:Lo0OOo00/o00oo0O0$o00oOOo0;

    iput p1, v0, Lo0OOo00/o00oo0O0$o00oOOo0;->o00oo0o:I

    iput p1, v0, Lo0OOo00/o00oo0O0$o00oOOo0;->o00oo0O:I

    iput-short p2, v0, Lo0OOo00/o00oo0O0$o00oOOo0;->o00oo0oO:S

    iput-byte p3, v0, Lo0OOo00/o00oo0O0$o00oOOo0;->o00oo0Oo:B

    iput p4, v0, Lo0OOo00/o00oo0O0$o00oOOo0;->o00oo0o0:I

    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0o0:Lo0OOo00/o00oOoO$o00oOOo0;

    invoke-virtual {p1}, Lo0OOo00/o00oOoO$o00oOOo0;->o00oOooo()V

    iget-object p1, p0, Lo0OOo00/o00oo0O0;->o00oo0o0:Lo0OOo00/o00oOoO$o00oOOo0;

    invoke-virtual {p1}, Lo0OOo00/o00oOoO$o00oOOo0;->o00oOo0O()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public final o00oOoO0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p3, 0x1

    const/4 v0, 0x0

    const/16 v1, 0x8

    if-lt p2, v1, :cond_3

    if-nez p4, :cond_2

    iget-object p4, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p4}, Lokio/o00oOoO;->readInt()I

    move-result p4

    iget-object v2, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v2}, Lokio/o00oOoO;->readInt()I

    move-result v2

    sub-int/2addr p2, v1

    invoke-static {v2}, Lo0OOo00/o00oOo00;->fromHttp2(I)Lo0OOo00/o00oOo00;

    move-result-object v1

    if-eqz v1, :cond_1

    sget-object p3, Lokio/o00oo00O;->EMPTY:Lokio/o00oo00O;

    if-lez p2, :cond_0

    iget-object p3, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    int-to-long v2, p2

    invoke-interface {p3, v2, v3}, Lokio/o00oOoO;->o0O0O0o0(J)Lokio/o00oo00O;

    move-result-object p3

    :cond_0
    invoke-interface {p1, p4, v1, p3}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOOo0(ILo0OOo00/o00oOo00;Lokio/o00oo00O;)V

    return-void

    :cond_1
    new-array p1, p3, [Ljava/lang/Object;

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "TYPE_GOAWAY unexpected error code: %d"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_2
    const-string p1, "TYPE_GOAWAY streamId != 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_3
    new-array p1, p3, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "TYPE_GOAWAY length < 8: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oOoOo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p4, :cond_3

    and-int/lit8 v1, p3, 0x1

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    move v1, v0

    :goto_0
    and-int/lit8 v2, p3, 0x8

    if-eqz v2, :cond_1

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readByte()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    int-to-short v0, v0

    :cond_1
    and-int/lit8 v2, p3, 0x20

    if-eqz v2, :cond_2

    invoke-virtual {p0, p1, p4}, Lo0OOo00/o00oo0O0;->o00oo0oO(Lo0OOo00/o00oo0O0$o00oOOoO;I)V

    add-int/lit8 p2, p2, -0x5

    :cond_2
    invoke-static {p2, p3, v0}, Lo0OOo00/o00oo0O0;->o00oOOo0(IBS)I

    move-result p2

    invoke-virtual {p0, p2, v0, p3, p4}, Lo0OOo00/o00oo0O0;->o00oOoO(ISBI)Ljava/util/List;

    move-result-object p2

    const/4 p3, -0x1

    invoke-interface {p1, v1, p4, p3, p2}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOo00(ZIILjava/util/List;)V

    return-void

    :cond_3
    const-string p1, "PROTOCOL_ERROR: TYPE_HEADERS streamId == 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oo0OO(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/16 v0, 0x8

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ne p2, v0, :cond_2

    if-nez p4, :cond_1

    iget-object p2, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p2}, Lokio/o00oOoO;->readInt()I

    move-result p2

    iget-object p4, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p4}, Lokio/o00oOoO;->readInt()I

    move-result p4

    and-int/2addr p3, v2

    if-eqz p3, :cond_0

    move v1, v2

    :cond_0
    invoke-interface {p1, v1, p2, p4}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOoOO(ZII)V

    return-void

    :cond_1
    const-string p1, "TYPE_PING streamId != 0"

    new-array p2, v1, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_2
    new-array p1, v2, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v1

    const-string p2, "TYPE_PING length != 8: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oo0oO(Lo0OOo00/o00oo0O0$o00oOOoO;I)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readInt()I

    move-result v0

    const/high16 v1, -0x80000000

    and-int/2addr v1, v0

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    move v1, v2

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    const v3, 0x7fffffff

    and-int/2addr v0, v3

    iget-object v3, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v3}, Lokio/o00oOoO;->readByte()B

    move-result v3

    and-int/lit16 v3, v3, 0xff

    add-int/2addr v3, v2

    invoke-interface {p1, p2, v0, v3, v1}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOoOo(IIIZ)V

    return-void
.end method

.method public final o00ooO0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p4, :cond_1

    and-int/lit8 v1, p3, 0x8

    if-eqz v1, :cond_0

    iget-object v0, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readByte()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    int-to-short v0, v0

    :cond_0
    iget-object v1, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v1}, Lokio/o00oOoO;->readInt()I

    move-result v1

    const v2, 0x7fffffff

    and-int/2addr v1, v2

    add-int/lit8 p2, p2, -0x4

    invoke-static {p2, p3, v0}, Lo0OOo00/o00oo0O0;->o00oOOo0(IBS)I

    move-result p2

    invoke-virtual {p0, p2, v0, p3, p4}, Lo0OOo00/o00oo0O0;->o00oOoO(ISBI)Ljava/util/List;

    move-result-object p2

    invoke-interface {p1, p4, v1, p2}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOo0o(IILjava/util/List;)V

    return-void

    :cond_1
    const-string p1, "PROTOCOL_ERROR: TYPE_PUSH_PROMISE streamId == 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00ooO00(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p3, 0x5

    const/4 v0, 0x0

    if-ne p2, p3, :cond_1

    if-eqz p4, :cond_0

    invoke-virtual {p0, p1, p4}, Lo0OOo00/o00oo0O0;->o00oo0oO(Lo0OOo00/o00oo0O0$o00oOOoO;I)V

    return-void

    :cond_0
    const-string p1, "TYPE_PRIORITY streamId == 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "TYPE_PRIORITY length: %d != 5"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00ooOoo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p3, 0x4

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-ne p2, p3, :cond_2

    if-eqz p4, :cond_1

    iget-object p2, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p2}, Lokio/o00oOoO;->readInt()I

    move-result p2

    invoke-static {p2}, Lo0OOo00/o00oOo00;->fromHttp2(I)Lo0OOo00/o00oOo00;

    move-result-object p3

    if-eqz p3, :cond_0

    invoke-interface {p1, p4, p3}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOoo0(ILo0OOo00/o00oOo00;)V

    return-void

    :cond_0
    new-array p1, v0, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v1

    const-string p2, "TYPE_RST_STREAM unexpected error code: %d"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    const-string p1, "TYPE_RST_STREAM streamId == 0"

    new-array p2, v1, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_2
    new-array p1, v0, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v1

    const-string p2, "TYPE_RST_STREAM length: %d != 4"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oooOo(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    if-nez p4, :cond_c

    const/4 p4, 0x1

    and-int/2addr p3, p4

    if-eqz p3, :cond_1

    if-nez p2, :cond_0

    invoke-interface {p1}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOoO0()V

    return-void

    :cond_0
    const-string p1, "FRAME_SIZE_ERROR ack frame should be empty!"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    rem-int/lit8 p3, p2, 0x6

    if-nez p3, :cond_b

    new-instance p3, Lo0OOo00/o0;

    invoke-direct {p3}, Lo0OOo00/o0;-><init>()V

    move v1, v0

    :goto_0
    if-ge v1, p2, :cond_a

    iget-object v2, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v2}, Lokio/o00oOoO;->readShort()S

    move-result v2

    iget-object v3, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {v3}, Lokio/o00oOoO;->readInt()I

    move-result v3

    const/4 v4, 0x2

    if-eq v2, v4, :cond_7

    const/4 v4, 0x3

    const/4 v5, 0x4

    if-eq v2, v4, :cond_6

    if-eq v2, v5, :cond_4

    const/4 v4, 0x5

    if-eq v2, v4, :cond_2

    goto :goto_1

    :cond_2
    const/16 v4, 0x4000

    if-lt v3, v4, :cond_3

    const v4, 0xffffff

    if-gt v3, v4, :cond_3

    goto :goto_1

    :cond_3
    new-array p1, p4, [Ljava/lang/Object;

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "PROTOCOL_ERROR SETTINGS_MAX_FRAME_SIZE: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_4
    if-ltz v3, :cond_5

    const/4 v2, 0x7

    goto :goto_1

    :cond_5
    const-string p1, "PROTOCOL_ERROR SETTINGS_INITIAL_WINDOW_SIZE > 2^31 - 1"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_6
    move v2, v5

    goto :goto_1

    :cond_7
    if-eqz v3, :cond_9

    if-ne v3, p4, :cond_8

    goto :goto_1

    :cond_8
    const-string p1, "PROTOCOL_ERROR SETTINGS_ENABLE_PUSH != 0 or 1"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_9
    :goto_1
    invoke-virtual {p3, v2, v3}, Lo0OOo00/o0;->o00oOoo0(II)Lo0OOo00/o0;

    add-int/lit8 v1, v1, 0x6

    goto :goto_0

    :cond_a
    invoke-interface {p1, v0, p3}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOOoO(ZLo0OOo00/o0;)V

    return-void

    :cond_b
    new-array p1, p4, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "TYPE_SETTINGS length %% 6 != 0: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_c
    const-string p1, "TYPE_SETTINGS streamId != 0"

    new-array p2, v0, [Ljava/lang/Object;

    invoke-static {p1, p2}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method

.method public final o00oooo0(Lo0OOo00/o00oo0O0$o00oOOoO;IBI)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p3, 0x4

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-ne p2, p3, :cond_1

    iget-object p2, p0, Lo0OOo00/o00oo0O0;->o00oo0O0:Lokio/o00oOoO;

    invoke-interface {p2}, Lokio/o00oOoO;->readInt()I

    move-result p2

    int-to-long p2, p2

    const-wide/32 v2, 0x7fffffff

    and-long/2addr p2, v2

    const-wide/16 v2, 0x0

    cmp-long v2, p2, v2

    if-eqz v2, :cond_0

    invoke-interface {p1, p4, p2, p3}, Lo0OOo00/o00oo0O0$o00oOOoO;->o00oOooO(IJ)V

    return-void

    :cond_0
    new-array p1, v1, [Ljava/lang/Object;

    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "windowSizeIncrement was 0"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    new-array p1, v1, [Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p1, v0

    const-string p2, "TYPE_WINDOW_UPDATE length !=4: %s"

    invoke-static {p2, p1}, Lo0OOo00/o00oo00O;->o00oOooO(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/IOException;

    move-result-object p1

    throw p1
.end method
