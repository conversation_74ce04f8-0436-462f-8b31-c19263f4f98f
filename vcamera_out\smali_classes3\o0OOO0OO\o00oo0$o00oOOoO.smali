.class public Lo0OOO0Oo/o00oo0$o00oOOoO;
.super Lcom/google/android/gms/ads/appopen/AppOpenAd$AppOpenAdLoadCallback;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oo0;->o00oOoOO(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oo0;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo0;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    invoke-direct {p0}, Lcom/google/android/gms/ads/appopen/AppOpenAd$AppOpenAdLoadCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Lcom/google/android/gms/ads/appopen/AppOpenAd;)V
    .locals 2
    .param p1    # Lcom/google/android/gms/ads/appopen/AppOpenAd;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    iget-object v0, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    invoke-static {v0, p1}, Lo0OOO0Oo/o00oo0;->o00oOooO(Lo0OOO0Oo/o00oo0;Lcom/google/android/gms/ads/appopen/AppOpenAd;)Lcom/google/android/gms/ads/appopen/AppOpenAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    iget-object p1, p1, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    new-instance v0, Lo0OOO0Oo/o00oo0$o00oOOoO$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oo0$o00oOOoO$o00oOOo0;-><init>(Lo0OOO0Oo/o00oo0$o00oOOoO;)V

    invoke-virtual {p1, v0}, Lcom/google/android/gms/ads/appopen/AppOpenAd;->setOnPaidEventListener(Lcom/google/android/gms/ads/OnPaidEventListener;)V

    const/16 p1, 0x13

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_1

    invoke-static {p1, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 p1, 0xd

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v0, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oo0;->o00oOOoO:Z

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p1, Lo0OOO0Oo/o00oo0;->o00oOo00:J

    return-void

    nop

    :array_0
    .array-data 1
        0x61t
        0x77t
        0x3bt
        -0x4bt
        0x20t
        0x2at
        -0x6at
        -0x14t
        0x52t
        0x5ct
        0x6t
        -0x4ft
        0x20t
        0x18t
        -0x6et
        -0x5t
        0x45t
        0x76t
        0x18t
    .end array-data

    :array_1
    .array-data 1
        0x20t
        0x13t
        0x76t
        -0x2ct
        0x4et
        0x4bt
        -0xft
        -0x77t
    .end array-data

    :array_2
    .array-data 1
        -0x70t
        0x3et
        -0xat
        -0x71t
        -0x11t
        -0x2et
        -0x32t
        -0x13t
        -0x66t
        0x34t
        -0x67t
        -0x3bt
        -0x73t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x1t
        0x50t
        -0x49t
        -0x15t
        -0x5dt
        -0x43t
        -0x51t
        -0x77t
    .end array-data
.end method

.method public onAdFailedToLoad(Lcom/google/android/gms/ads/LoadAdError;)V
    .locals 3
    .param p1    # Lcom/google/android/gms/ads/LoadAdError;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    const/16 v0, 0x13

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x11

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {p1}, Lcom/google/android/gms/ads/AdError;->getMessage()Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lo0OOO0Oo/o00oo0;->o00oOooO(Lo0OOO0Oo/o00oo0;Lcom/google/android/gms/ads/appopen/AppOpenAd;)Lcom/google/android/gms/ads/appopen/AppOpenAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo0;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oo0;->o00oOOoO:Z

    return-void

    nop

    :array_0
    .array-data 1
        0x4ft
        0x27t
        -0x4et
        -0x2ft
        -0x48t
        -0x6t
        -0x2ft
        -0x73t
        0x7ct
        0xct
        -0x71t
        -0x2bt
        -0x48t
        -0x38t
        -0x2bt
        -0x66t
        0x6bt
        0x26t
        -0x6ft
    .end array-data

    :array_1
    .array-data 1
        0xet
        0x43t
        -0x1t
        -0x50t
        -0x2at
        -0x65t
        -0x4at
        -0x18t
    .end array-data

    :array_2
    .array-data 1
        0x30t
        -0x43t
        0x1ct
        -0x14t
        -0x1ft
        -0x6at
        -0x75t
        -0x3t
        0x3at
        -0x49t
        0x9t
        -0x19t
        -0x15t
        -0x68t
        -0x7dt
        -0xbt
        0x65t
    .end array-data

    nop

    :array_3
    .array-data 1
        0x5ft
        -0x2dt
        0x5dt
        -0x78t
        -0x59t
        -0x9t
        -0x1et
        -0x6ft
    .end array-data
.end method

.method public bridge synthetic onAdLoaded(Ljava/lang/Object;)V
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    check-cast p1, Lcom/google/android/gms/ads/appopen/AppOpenAd;

    invoke-virtual {p0, p1}, Lo0OOO0Oo/o00oo0$o00oOOoO;->o00oOOo0(Lcom/google/android/gms/ads/appopen/AppOpenAd;)V

    return-void
.end method
