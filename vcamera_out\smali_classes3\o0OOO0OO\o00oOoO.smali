.class public Lo0OOO0Oo/o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOO0Oo/o00oOoO$o00oOo0O;
    }
.end annotation


# static fields
.field public static final o00oOo0O:Ljava/lang/String;

.field public static final o00oOo0o:J = 0x6ddd00L

.field public static o00oOoO0:Lo0OOO0Oo/o00oOoO;


# instance fields
.field public o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

.field public o00oOOoO:Z

.field public o00oOo00:J

.field public o00oOooO:Ljava/lang/Runnable;


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    const/16 v0, 0x13

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lo0OOO0Oo/o00oOoO;->o00oOo0O:Ljava/lang/String;

    const/4 v0, 0x0

    sput-object v0, Lo0OOO0Oo/o00oOoO;->o00oOoO0:Lo0OOO0Oo/o00oOoO;

    return-void

    :array_0
    .array-data 1
        -0x1bt
        -0x7dt
        -0x6ft
        0x70t
        -0x34t
        -0x37t
        -0x47t
        0x58t
        -0x2at
        -0x52t
        -0x4et
        0x62t
        -0x39t
        -0x26t
        -0x56t
        0x6dt
        -0x3bt
        -0x80t
        -0x47t
    .end array-data

    :array_1
    .array-data 1
        -0x5ct
        -0x19t
        -0x24t
        0x11t
        -0x5et
        -0x58t
        -0x22t
        0x3dt
    .end array-data
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOo00:J

    new-instance v0, Lo0OOO0Oo/o00oOoO$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oOoO$o00oOOo0;-><init>(Lo0OOO0Oo/o00oOoO;)V

    iput-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOooO:Ljava/lang/Runnable;

    return-void
.end method

.method public static synthetic o00oOOo0(Lo0OOO0Oo/o00oOoO;)J
    .locals 2

    iget-wide v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOo00:J

    return-wide v0
.end method

.method public static synthetic o00oOOoO(Lo0OOO0Oo/o00oOoO;J)J
    .locals 0

    iput-wide p1, p0, Lo0OOO0Oo/o00oOoO;->o00oOo00:J

    return-wide p1
.end method

.method public static synthetic o00oOo00(Lo0OOO0Oo/o00oOoO;)Lcom/google/android/gms/ads/interstitial/InterstitialAd;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    return-object p0
.end method

.method public static synthetic o00oOo0O(Lo0OOO0Oo/o00oOoO;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oOoO;->o00oOooO:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static o00oOo0o()Lo0OOO0Oo/o00oOoO;
    .locals 2

    sget-object v0, Lo0OOO0Oo/o00oOoO;->o00oOoO0:Lo0OOO0Oo/o00oOoO;

    if-nez v0, :cond_1

    const-class v0, Lo0OOO0Oo/o00oOoO;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lo0OOO0Oo/o00oOoO;->o00oOoO0:Lo0OOO0Oo/o00oOoO;

    if-nez v1, :cond_0

    new-instance v1, Lo0OOO0Oo/o00oOoO;

    invoke-direct {v1}, Lo0OOO0Oo/o00oOoO;-><init>()V

    sput-object v1, Lo0OOO0Oo/o00oOoO;->o00oOoO0:Lo0OOO0Oo/o00oOoO;

    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    :cond_1
    :goto_0
    sget-object v0, Lo0OOO0Oo/o00oOoO;->o00oOoO0:Lo0OOO0Oo/o00oOoO;

    return-object v0
.end method

.method public static synthetic o00oOooO(Lo0OOO0Oo/o00oOoO;Lcom/google/android/gms/ads/interstitial/InterstitialAd;)Lcom/google/android/gms/ads/interstitial/InterstitialAd;
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    return-object p1
.end method


# virtual methods
.method public declared-synchronized o00oOoO(Landroid/content/Context;)V
    .locals 3

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOoO:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOoO:Z

    new-instance v0, Lcom/google/android/gms/ads/AdRequest$Builder;

    invoke-direct {v0}, Lcom/google/android/gms/ads/AdRequest$Builder;-><init>()V

    invoke-virtual {v0}, Lcom/google/android/gms/ads/AdRequest$Builder;->build()Lcom/google/android/gms/ads/AdRequest;

    move-result-object v0

    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOOo0()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Lo0OOO0Oo/o00oOoO$o00oOOoO;

    invoke-direct {v2, p0}, Lo0OOO0Oo/o00oOoO$o00oOOoO;-><init>(Lo0OOO0Oo/o00oOoO;)V

    invoke-static {p1, v1, v0, v2}, Lcom/google/android/gms/ads/interstitial/InterstitialAd;->load(Landroid/content/Context;Ljava/lang/String;Lcom/google/android/gms/ads/AdRequest;Lcom/google/android/gms/ads/interstitial/InterstitialAdLoadCallback;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o00oOoO0()Z
    .locals 1

    iget-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOoO:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public o00oOoOO(Lcom/google/android/gms/ads/AdValue;)V
    .locals 16

    :try_start_0
    invoke-static {}, Lo0OOOo00/o00oo0O0;->o00oOooO()Lo0OOOo00/o00oo0O0;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    move-object/from16 v1, p0

    :try_start_1
    iget-object v2, v1, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    invoke-virtual {v2}, Lcom/google/android/gms/ads/interstitial/InterstitialAd;->getAdUnitId()Ljava/lang/String;

    move-result-object v2

    const-string v3, ""

    const/16 v4, 0xa

    new-array v4, v4, [B

    const/16 v5, 0x59

    const/4 v6, 0x0

    aput-byte v5, v4, v6

    const/16 v5, -0x26

    const/4 v7, 0x1

    aput-byte v5, v4, v7

    const/16 v8, -0x17

    const/4 v9, 0x2

    aput-byte v8, v4, v9

    const/16 v8, -0x1d

    const/4 v10, 0x3

    aput-byte v8, v4, v10

    const/16 v8, -0xc

    const/4 v11, 0x4

    aput-byte v8, v4, v11

    const/16 v8, 0x4b

    const/4 v12, 0x5

    aput-byte v8, v4, v12

    const/16 v8, -0x24

    const/4 v13, 0x6

    aput-byte v8, v4, v13

    const/4 v8, 0x7

    aput-byte v5, v4, v8

    const/16 v5, 0x77

    const/16 v14, 0x8

    aput-byte v5, v4, v14

    const/16 v5, 0x9

    const/16 v15, -0x2f

    aput-byte v15, v4, v5

    new-array v5, v14, [B

    const/16 v14, 0x10

    aput-byte v14, v5, v6

    const/16 v6, -0x4c

    aput-byte v6, v5, v7

    const/16 v6, -0x66

    aput-byte v6, v5, v9

    const/16 v6, -0x7a

    aput-byte v6, v5, v10

    aput-byte v6, v5, v11

    const/16 v6, 0x3f

    aput-byte v6, v5, v12

    const/16 v6, -0x74

    aput-byte v6, v5, v13

    const/16 v6, -0x45

    aput-byte v6, v5, v8

    invoke-static {v4, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    move-object/from16 v5, p1

    invoke-virtual {v0, v5, v2, v3, v4}, Lo0OOOo00/o00oo0O0;->o00oOoO0(Lcom/google/android/gms/ads/AdValue;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_0

    :catchall_1
    move-exception v0

    move-object/from16 v1, p0

    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method public o00oOoOo()V
    .locals 3

    iget-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOooO:Ljava/lang/Runnable;

    const-wide/32 v1, 0x6ddd00

    invoke-static {v0, v1, v2}, Lmultispace/multiapp/clone/util/o0O00oO0;->o00oOo0o(Ljava/lang/Runnable;J)V

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0, v0}, Lo0OOO0Oo/o00oOoO;->o00oOoO(Landroid/content/Context;)V

    return-void
.end method

.method public o00oOoo0(Landroid/app/Activity;)Z
    .locals 19

    move-object/from16 v0, p1

    const/4 v1, 0x0

    :try_start_0
    invoke-static {}, Lo0OOO0Oo/o00oOoO;->o00oOo0o()Lo0OOO0Oo/o00oOoO;

    move-result-object v2

    invoke-virtual {v2}, Lo0OOO0Oo/o00oOoO;->o00oOoO0()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-static {}, Lo0OOO0Oo/o00oOoO;->o00oOo0o()Lo0OOO0Oo/o00oOoO;

    move-result-object v2

    invoke-virtual {v2, v0}, Lo0OOO0Oo/o00oOoO;->o00oOooo(Landroid/app/Activity;)Z

    move-result v0

    return v0

    :cond_0
    const/16 v2, 0x20

    new-array v2, v2, [B

    const/4 v3, -0x5

    aput-byte v3, v2, v1

    const/16 v4, 0x50

    const/4 v5, 0x1

    aput-byte v4, v2, v5

    const/16 v4, 0x5e

    const/4 v6, 0x2

    aput-byte v4, v2, v6

    const/16 v4, 0x67

    const/4 v7, 0x3

    aput-byte v4, v2, v7

    const/16 v8, -0x7c

    const/4 v9, 0x4

    aput-byte v8, v2, v9

    const/16 v8, 0xe

    const/4 v10, 0x5

    aput-byte v8, v2, v10

    const/4 v11, 0x6

    aput-byte v3, v2, v11

    const/16 v3, 0x73

    const/4 v12, 0x7

    aput-byte v3, v2, v12

    const/16 v3, -0x23

    const/16 v13, 0x8

    aput-byte v3, v2, v13

    const/16 v14, 0x9

    const/16 v15, 0x4c

    aput-byte v15, v2, v14

    const/16 v14, 0xa

    const/16 v15, 0x1b

    aput-byte v15, v2, v14

    const/16 v14, 0xb

    const/16 v16, 0x37

    aput-byte v16, v2, v14

    const/16 v14, 0xc

    const/16 v16, -0x74

    aput-byte v16, v2, v14

    const/16 v14, 0xd

    aput-byte v12, v2, v14

    const/16 v14, -0x13

    aput-byte v14, v2, v8

    const/16 v8, 0x36

    const/16 v16, 0xf

    aput-byte v8, v2, v16

    const/16 v17, 0x10

    const/16 v18, -0x39

    aput-byte v18, v2, v17

    const/16 v17, 0x11

    const/16 v18, 0x59

    aput-byte v18, v2, v17

    const/16 v17, 0x12

    const/16 v18, 0x48

    aput-byte v18, v2, v17

    const/16 v17, 0x13

    aput-byte v4, v2, v17

    const/16 v4, 0x14

    const/16 v17, -0x7d

    aput-byte v17, v2, v4

    const/16 v4, 0x15

    aput-byte v16, v2, v4

    const/4 v4, -0x4

    const/16 v16, 0x16

    aput-byte v4, v2, v16

    const/16 v4, 0x17

    aput-byte v8, v2, v4

    const/16 v4, 0x18

    aput-byte v3, v2, v4

    const/16 v3, 0x19

    const/16 v4, 0x5d

    aput-byte v4, v2, v3

    const/16 v3, 0x1a

    const/16 v4, 0x5a

    aput-byte v4, v2, v3

    const/16 v3, 0x23

    aput-byte v3, v2, v15

    const/16 v3, 0x1c

    const/16 v4, -0x6c

    aput-byte v4, v2, v3

    const/16 v3, 0x1d

    const/16 v4, 0x4e

    aput-byte v4, v2, v3

    const/16 v3, 0x1e

    const/16 v4, -0x5a

    aput-byte v4, v2, v3

    const/16 v3, 0x1f

    const/16 v4, 0x38

    aput-byte v4, v2, v3

    new-array v3, v13, [B

    const/16 v8, -0x51

    aput-byte v8, v3, v1

    aput-byte v4, v3, v5

    const/16 v4, 0x3b

    aput-byte v4, v3, v6

    const/16 v4, 0x47

    aput-byte v4, v3, v7

    aput-byte v14, v3, v9

    const/16 v4, 0x60

    aput-byte v4, v3, v10

    const/16 v4, -0x78

    aput-byte v4, v3, v11

    aput-byte v16, v3, v12

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-static {}, Lo0OOO0Oo/o00oOoO;->o00oOo0o()Lo0OOO0Oo/o00oOoO;

    move-result-object v2

    invoke-virtual {v2, v0}, Lo0OOO0Oo/o00oOoO;->o00oOoO(Landroid/content/Context;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return v1

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    return v1
.end method

.method public final o00oOooo(Landroid/app/Activity;)Z
    .locals 3

    iget-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    const/16 v1, 0x8

    if-nez v0, :cond_0

    const/16 p1, 0x21

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    new-array v0, v1, [B

    fill-array-data v0, :array_1

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/4 p1, 0x0

    return p1

    :cond_0
    new-instance v2, Lo0OOO0Oo/o00oOoO$o00oOo00;

    invoke-direct {v2, p0, p1}, Lo0OOO0Oo/o00oOoO$o00oOo00;-><init>(Lo0OOO0Oo/o00oOoO;Landroid/app/Activity;)V

    invoke-virtual {v0, v2}, Lcom/google/android/gms/ads/interstitial/InterstitialAd;->setFullScreenContentCallback(Lcom/google/android/gms/ads/FullScreenContentCallback;)V

    iget-object v0, p0, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    invoke-virtual {v0, p1}, Lcom/google/android/gms/ads/interstitial/InterstitialAd;->show(Landroid/app/Activity;)V

    const/16 p1, 0x14

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v1, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/4 p1, 0x1

    return p1

    nop

    :array_0
    .array-data 1
        0x35t
        -0x80t
        -0x7ct
        0x3ft
        -0x33t
        0x3at
        0x51t
        -0x6dt
        0x13t
        -0x74t
        -0x7ct
        0x7bt
        -0x61t
        0x3et
        0x42t
        -0x2et
        0x16t
        -0x77t
        -0x6et
        0x71t
        -0x68t
        0x2bt
        0x6t
        -0x80t
        0x4t
        -0x77t
        -0x7bt
        0x66t
        -0x61t
        0x26t
        0x43t
        -0x7at
        0x4ft
    .end array-data

    nop

    :array_1
    .array-data 1
        0x61t
        -0x18t
        -0x1ft
        0x1ft
        -0x41t
        0x5ft
        0x26t
        -0xet
    .end array-data

    :array_2
    .array-data 1
        -0x7et
        -0x4ft
        -0x5bt
        -0x3bt
        -0x8t
        0x46t
        0x78t
        0x3ft
        -0x6ct
        -0x55t
        -0x42t
        -0x6et
        -0x58t
        0x4et
        0x71t
        0x29t
        -0x21t
        -0x9t
        -0x1ct
        -0x64t
    .end array-data

    :array_3
    .array-data 1
        -0xft
        -0x27t
        -0x36t
        -0x4et
        -0x28t
        0x2ft
        0x16t
        0x4ct
    .end array-data
.end method
