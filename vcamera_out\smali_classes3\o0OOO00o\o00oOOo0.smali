.class public final enum Lo0OOO00o/o00oOOo0;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOO00o/o00oOOo0$o00oOOo0;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lo0OOO00o/o00oOOo0;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0008\u0008\u0086\u0001\u0018\u0000 \u00042\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0005B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lo0OOO00o/o00oOOo0;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "Companion",
        "o00oOOo0",
        "SPARSE_ARRAY",
        "HASH_MAP",
        "NO_CACHE",
        "kotlin-android-extensions-runtime"
    }
    k = 0x1
    mv = {
        0x1,
        0x4,
        0x1
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lo0OOO00o/o00oOOo0;

.field public static final Companion:Lo0OOO00o/o00oOOo0$o00oOOo0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private static final DEFAULT:Lo0OOO00o/o00oOOo0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum HASH_MAP:Lo0OOO00o/o00oOOo0;

.field public static final enum NO_CACHE:Lo0OOO00o/o00oOOo0;

.field public static final enum SPARSE_ARRAY:Lo0OOO00o/o00oOOo0;


# direct methods
.method public static constructor <clinit>()V
    .locals 7

    new-instance v0, Lo0OOO00o/o00oOOo0;

    const-string v1, "SPARSE_ARRAY"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lo0OOO00o/o00oOOo0;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lo0OOO00o/o00oOOo0;->SPARSE_ARRAY:Lo0OOO00o/o00oOOo0;

    new-instance v1, Lo0OOO00o/o00oOOo0;

    const-string v3, "HASH_MAP"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lo0OOO00o/o00oOOo0;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lo0OOO00o/o00oOOo0;->HASH_MAP:Lo0OOO00o/o00oOOo0;

    new-instance v3, Lo0OOO00o/o00oOOo0;

    const-string v5, "NO_CACHE"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lo0OOO00o/o00oOOo0;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lo0OOO00o/o00oOOo0;->NO_CACHE:Lo0OOO00o/o00oOOo0;

    const/4 v5, 0x3

    new-array v5, v5, [Lo0OOO00o/o00oOOo0;

    aput-object v0, v5, v2

    aput-object v1, v5, v4

    aput-object v3, v5, v6

    sput-object v5, Lo0OOO00o/o00oOOo0;->$VALUES:[Lo0OOO00o/o00oOOo0;

    new-instance v0, Lo0OOO00o/o00oOOo0$o00oOOo0;

    const/4 v2, 0x0

    invoke-direct {v0, v2}, Lo0OOO00o/o00oOOo0$o00oOOo0;-><init>(Lkotlin/jvm/internal/o0O00;)V

    sput-object v0, Lo0OOO00o/o00oOOo0;->Companion:Lo0OOO00o/o00oOOo0$o00oOOo0;

    sput-object v1, Lo0OOO00o/o00oOOo0;->DEFAULT:Lo0OOO00o/o00oOOo0;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static final synthetic access$getDEFAULT$cp()Lo0OOO00o/o00oOOo0;
    .locals 1

    sget-object v0, Lo0OOO00o/o00oOOo0;->DEFAULT:Lo0OOO00o/o00oOOo0;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lo0OOO00o/o00oOOo0;
    .locals 1

    const-string v0, "value"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const-class v0, Lo0OOO00o/o00oOOo0;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lo0OOO00o/o00oOOo0;

    return-object p0
.end method

.method public static values()[Lo0OOO00o/o00oOOo0;
    .locals 4

    sget-object v0, Lo0OOO00o/o00oOOo0;->$VALUES:[Lo0OOO00o/o00oOOo0;

    array-length v1, v0

    new-array v1, v1, [Lo0OOO00o/o00oOOo0;

    const/4 v2, 0x0

    array-length v3, v0

    invoke-static {v0, v2, v1, v2, v3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    return-object v1
.end method
