.class public final Lo0OOo00/o00oo0OO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo00/o00oo0OO$o00oo0O0;,
        Lo0OOo00/o00oo0OO$o00oo0O;,
        Lo0OOo00/o00oo0OO$o00oo0OO;
    }
.end annotation


# static fields
.field public static final o00ooo0:I = 0x1000000

.field public static final o00ooo00:Ljava/util/concurrent/ExecutorService;

.field public static final synthetic o00ooo0O:Z


# instance fields
.field public final o00oo:Ljava/util/concurrent/ExecutorService;

.field public final o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

.field public final o00oo0O0:Z

.field public final o00oo0Oo:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lo0OOo00/o00oo0O;",
            ">;"
        }
    .end annotation
.end field

.field public o00oo0o:I

.field public final o00oo0o0:Ljava/lang/String;

.field public o00oo0oO:I

.field public o00ooO:J

.field public final o00ooO0:Lo0OOo00/o00ooO;

.field public o00ooO00:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lo0OOo00/o00ooO0;",
            ">;"
        }
    .end annotation
.end field

.field public o00ooO0O:I

.field public o00ooO0o:J

.field public final o00ooOO:Lo0OOo00/o0;

.field public o00ooOO0:Lo0OOo00/o0;

.field public o00ooOOo:Z

.field public final o00ooOo:Lo0OOo00/o0O0o;

.field public final o00ooOo0:Ljava/net/Socket;

.field public final o00ooOoO:Lo0OOo00/o00oo0OO$o00oo0O;

.field public final o00ooOoo:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public o0O0o:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 9

    new-instance v8, Ljava/util/concurrent/ThreadPoolExecutor;

    const/4 v1, 0x0

    const v2, 0x7fffffff

    const-wide/16 v3, 0x3c

    sget-object v5, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    new-instance v6, Ljava/util/concurrent/SynchronousQueue;

    invoke-direct {v6}, Ljava/util/concurrent/SynchronousQueue;-><init>()V

    const-string v0, "OkHttp Http2Connection"

    const/4 v7, 0x1

    invoke-static {v0, v7}, Lo0OOOoOo/o0O0000O;->o00ooO0(Ljava/lang/String;Z)Ljava/util/concurrent/ThreadFactory;

    move-result-object v7

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Ljava/util/concurrent/ThreadPoolExecutor;-><init>(IIJLjava/util/concurrent/TimeUnit;Ljava/util/concurrent/BlockingQueue;Ljava/util/concurrent/ThreadFactory;)V

    sput-object v8, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    return-void
.end method

.method public constructor <init>(Lo0OOo00/o00oo0OO$o00oo0OO;)V
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-direct/range {p0 .. p0}, Ljava/lang/Object;-><init>()V

    new-instance v2, Ljava/util/LinkedHashMap;

    invoke-direct {v2}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    const-wide/16 v2, 0x0

    iput-wide v2, v0, Lo0OOo00/o00oo0OO;->o00ooO0o:J

    new-instance v2, Lo0OOo00/o0;

    invoke-direct {v2}, Lo0OOo00/o0;-><init>()V

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    new-instance v2, Lo0OOo00/o0;

    invoke-direct {v2}, Lo0OOo00/o0;-><init>()V

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    const/4 v3, 0x0

    iput-boolean v3, v0, Lo0OOo00/o00oo0OO;->o00ooOOo:Z

    new-instance v4, Ljava/util/LinkedHashSet;

    invoke-direct {v4}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v4, v0, Lo0OOo00/o00oo0OO;->o00ooOoo:Ljava/util/Set;

    iget-object v4, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0o:Lo0OOo00/o00ooO;

    iput-object v4, v0, Lo0OOo00/o00oo0OO;->o00ooO0:Lo0OOo00/o00ooO;

    iget-boolean v4, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOoO0:Z

    iput-boolean v4, v0, Lo0OOo00/o00oo0OO;->o00oo0O0:Z

    iget-object v5, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    iput-object v5, v0, Lo0OOo00/o00oo0OO;->o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    const/4 v5, 0x2

    const/4 v6, 0x1

    if-eqz v4, :cond_0

    move v7, v6

    goto :goto_0

    :cond_0
    move v7, v5

    :goto_0
    iput v7, v0, Lo0OOo00/o00oo0OO;->o00oo0oO:I

    if-eqz v4, :cond_1

    add-int/2addr v7, v5

    iput v7, v0, Lo0OOo00/o00oo0OO;->o00oo0oO:I

    :cond_1
    if-eqz v4, :cond_2

    move v5, v6

    :cond_2
    iput v5, v0, Lo0OOo00/o00oo0OO;->o00ooO0O:I

    const/4 v5, 0x7

    if-eqz v4, :cond_3

    iget-object v7, v0, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    const/high16 v8, 0x1000000

    invoke-virtual {v7, v5, v8}, Lo0OOo00/o0;->o00oOoo0(II)Lo0OOo00/o0;

    :cond_3
    iget-object v7, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOOoO:Ljava/lang/String;

    iput-object v7, v0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    new-instance v15, Ljava/util/concurrent/ThreadPoolExecutor;

    const/4 v9, 0x0

    const/4 v10, 0x1

    const-wide/16 v11, 0x3c

    sget-object v13, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    new-instance v14, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct {v14}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    new-array v8, v6, [Ljava/lang/Object;

    aput-object v7, v8, v3

    const-string v3, "OkHttp %s Push Observer"

    invoke-static {v3, v8}, Lo0OOOoOo/o0O0000O;->o00oo00O(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    new-instance v7, Lo0OOOoOo/o0O0000O$o00oOOoO;

    invoke-direct {v7, v3, v6}, Lo0OOOoOo/o0O0000O$o00oOOoO;-><init>(Ljava/lang/String;Z)V

    move-object v8, v15

    move-object v3, v15

    move-object v15, v7

    invoke-direct/range {v8 .. v15}, Ljava/util/concurrent/ThreadPoolExecutor;-><init>(IIJLjava/util/concurrent/TimeUnit;Ljava/util/concurrent/BlockingQueue;Ljava/util/concurrent/ThreadFactory;)V

    iput-object v3, v0, Lo0OOo00/o00oo0OO;->o00oo:Ljava/util/concurrent/ExecutorService;

    const v3, 0xffff

    invoke-virtual {v2, v5, v3}, Lo0OOo00/o0;->o00oOoo0(II)Lo0OOo00/o0;

    const/4 v3, 0x5

    const/16 v5, 0x4000

    invoke-virtual {v2, v3, v5}, Lo0OOo00/o0;->o00oOoo0(II)Lo0OOo00/o0;

    invoke-virtual {v2}, Lo0OOo00/o0;->o00oOo0O()I

    move-result v2

    int-to-long v2, v2

    iput-wide v2, v0, Lo0OOo00/o00oo0OO;->o00ooO:J

    iget-object v2, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOOo0:Ljava/net/Socket;

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00ooOo0:Ljava/net/Socket;

    new-instance v2, Lo0OOo00/o0O0o;

    iget-object v3, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOooO:Lokio/o00oOo0O;

    invoke-direct {v2, v3, v4}, Lo0OOo00/o0O0o;-><init>(Lokio/o00oOo0O;Z)V

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    new-instance v2, Lo0OOo00/o00oo0OO$o00oo0O;

    new-instance v3, Lo0OOo00/o00oo0O0;

    iget-object v1, v1, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo00:Lokio/o00oOoO;

    invoke-direct {v3, v1, v4}, Lo0OOo00/o00oo0O0;-><init>(Lokio/o00oOoO;Z)V

    invoke-direct {v2, v0, v3}, Lo0OOo00/o00oo0OO$o00oo0O;-><init>(Lo0OOo00/o00oo0OO;Lo0OOo00/o00oo0O0;)V

    iput-object v2, v0, Lo0OOo00/o00oo0OO;->o00ooOoO:Lo0OOo00/o00oo0OO$o00oo0O;

    return-void
.end method


# virtual methods
.method public close()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    sget-object v0, Lo0OOo00/o00oOo00;->NO_ERROR:Lo0OOo00/o00oOo00;

    sget-object v1, Lo0OOo00/o00oOo00;->CANCEL:Lo0OOo00/o00oOo00;

    invoke-virtual {p0, v0, v1}, Lo0OOo00/o00oo0OO;->o00oOOoO(Lo0OOo00/o00oOo00;Lo0OOo00/o00oOo00;)V

    return-void
.end method

.method public flush()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v0}, Lo0OOo00/o0O0o;->flush()V

    return-void
.end method

.method public o00oOOo0(J)V
    .locals 2

    iget-wide v0, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    add-long/2addr v0, p1

    iput-wide v0, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    const-wide/16 v0, 0x0

    cmp-long p1, p1, v0

    if-lez p1, :cond_0

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V

    :cond_0
    return-void
.end method

.method public o00oOOoO(Lo0OOo00/o00oOo00;Lo0OOo00/o00oOo00;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    invoke-virtual {p0, p1}, Lo0OOo00/o00oo0OO;->o0O00O0(Lo0OOo00/o00oOo00;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    move-object p1, v0

    goto :goto_0

    :catch_0
    move-exception p1

    :goto_0
    monitor-enter p0

    :try_start_1
    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v1

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->size()I

    move-result v2

    new-array v2, v2, [Lo0OOo00/o00oo0O;

    invoke-interface {v1, v2}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Lo0OOo00/o00oo0O;

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->clear()V

    goto :goto_1

    :cond_0
    move-object v1, v0

    :goto_1
    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    if-eqz v2, :cond_1

    invoke-interface {v2}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v2

    iget-object v3, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    invoke-interface {v3}, Ljava/util/Map;->size()I

    move-result v3

    new-array v3, v3, [Lo0OOo00/o00ooO0;

    invoke-interface {v2, v3}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lo0OOo00/o00ooO0;

    iput-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    move-object v0, v2

    :cond_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    const/4 v2, 0x0

    if-eqz v1, :cond_3

    array-length v3, v1

    move v4, v2

    :goto_2
    if-ge v4, v3, :cond_3

    aget-object v5, v1, v4

    :try_start_2
    invoke-virtual {v5, p2}, Lo0OOo00/o00oo0O;->o00oOooO(Lo0OOo00/o00oOo00;)V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_3

    :catch_1
    move-exception v5

    if-eqz p1, :cond_2

    move-object p1, v5

    :cond_2
    :goto_3
    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    :cond_3
    if-eqz v0, :cond_4

    array-length p2, v0

    :goto_4
    if-ge v2, p2, :cond_4

    aget-object v1, v0, v2

    invoke-virtual {v1}, Lo0OOo00/o00ooO0;->o00oOOo0()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_4

    :cond_4
    :try_start_3
    iget-object p2, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {p2}, Lo0OOo00/o0O0o;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_2

    goto :goto_5

    :catch_2
    move-exception p2

    if-nez p1, :cond_5

    move-object p1, p2

    :cond_5
    :goto_5
    :try_start_4
    iget-object p2, p0, Lo0OOo00/o00oo0OO;->o00ooOo0:Ljava/net/Socket;

    invoke-virtual {p2}, Ljava/net/Socket;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_3

    goto :goto_6

    :catch_3
    move-exception p1

    :goto_6
    if-nez p1, :cond_6

    return-void

    :cond_6
    throw p1

    :catchall_0
    move-exception p1

    :try_start_5
    monitor-exit p0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    throw p1
.end method

.method public o00oOo0O()Lokhttp3/o0O00O;
    .locals 1

    sget-object v0, Lokhttp3/o0O00O;->HTTP_2:Lokhttp3/o0O00O;

    return-object v0
.end method

.method public declared-synchronized o00oOo0o(I)Lo0OOo00/o00oo0O;
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lo0OOo00/o00oo0O;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized o00oOoO()I
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    const v1, 0x7fffffff

    invoke-virtual {v0, v1}, Lo0OOo00/o0;->o00oOo0o(I)I

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized o00oOoO0()Z
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final o00oOoOo(ILjava/util/List;Z)Lo0OOo00/o00oo0O;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)",
            "Lo0OOo00/o00oo0O;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    xor-int/lit8 v6, p3, 0x1

    const/4 v4, 0x0

    iget-object v7, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    monitor-enter v7

    :try_start_0
    monitor-enter p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-boolean v0, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    if-nez v0, :cond_6

    iget v8, p0, Lo0OOo00/o00oo0OO;->o00oo0oO:I

    add-int/lit8 v0, v8, 0x2

    iput v0, p0, Lo0OOo00/o00oo0OO;->o00oo0oO:I

    new-instance v9, Lo0OOo00/o00oo0O;

    move-object v0, v9

    move v1, v8

    move-object v2, p0

    move v3, v6

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lo0OOo00/o00oo0O;-><init>(ILo0OOo00/o00oo0OO;ZZLjava/util/List;)V

    if-eqz p3, :cond_1

    iget-wide v0, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    const-wide/16 v2, 0x0

    cmp-long p3, v0, v2

    if-eqz p3, :cond_1

    iget-wide v0, v9, Lo0OOo00/o00oo0O;->o00oOOoO:J

    cmp-long p3, v0, v2

    if-nez p3, :cond_0

    goto :goto_0

    :cond_0
    const/4 p3, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p3, 0x1

    :goto_1
    invoke-virtual {v9}, Lo0OOo00/o00oo0O;->o00oo0()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1, v9}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez p1, :cond_3

    :try_start_2
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v0, v6, v8, p1, p2}, Lo0OOo00/o0O0o;->o00oooo0(ZIILjava/util/List;)V

    goto :goto_2

    :cond_3
    iget-boolean v0, p0, Lo0OOo00/o00oo0OO;->o00oo0O0:Z

    if-nez v0, :cond_5

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v0, p1, v8, p2}, Lo0OOo00/o0O0o;->o00ooO00(IILjava/util/List;)V

    :goto_2
    monitor-exit v7
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    if-eqz p3, :cond_4

    iget-object p1, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {p1}, Lo0OOo00/o0O0o;->flush()V

    :cond_4
    return-object v9

    :cond_5
    :try_start_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "client streams shouldn\'t have associated stream IDs"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :cond_6
    :try_start_4
    new-instance p1, Lo0OOo00/o00oOOoO;

    invoke-direct {p1}, Lo0OOo00/o00oOOoO;-><init>()V

    throw p1

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    :try_start_5
    throw p1

    :catchall_1
    move-exception p1

    monitor-exit v7
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    throw p1
.end method

.method public o00oo00O(Ljava/util/List;Z)Lo0OOo00/o00oo0O;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)",
            "Lo0OOo00/o00oo0O;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, v0, p1, p2}, Lo0OOo00/o00oo0OO;->o00oOoOo(ILjava/util/List;Z)Lo0OOo00/o00oo0O;

    move-result-object p1

    return-object p1
.end method

.method public declared-synchronized o00oo0OO()I
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public o00oo0oO()Lo0OOo00/o00ooO0;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lo0OOo00/o00ooO0;

    invoke-direct {v0}, Lo0OOo00/o00ooO0;-><init>()V

    monitor-enter p0

    :try_start_0
    iget-boolean v1, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    if-nez v1, :cond_1

    iget v1, p0, Lo0OOo00/o00oo0OO;->o00ooO0O:I

    add-int/lit8 v2, v1, 0x2

    iput v2, p0, Lo0OOo00/o00oo0OO;->o00ooO0O:I

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    if-nez v2, :cond_0

    new-instance v2, Ljava/util/LinkedHashMap;

    invoke-direct {v2}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v2, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    :cond_0
    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v2, 0x0

    const v3, 0x4f4b6f6b

    invoke-virtual {p0, v2, v1, v3, v0}, Lo0OOo00/o00oo0OO;->o0ooO(ZIILo0OOo00/o00ooO0;)V

    return-object v0

    :cond_1
    :try_start_1
    new-instance v0, Lo0OOo00/o00oOOoO;

    invoke-direct {v0}, Lo0OOo00/o00oOOoO;-><init>()V

    throw v0

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method

.method public o00ooO0(ILjava/util/List;Z)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)V"
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo:Ljava/util/concurrent/ExecutorService;

    new-instance v8, Lo0OOo00/o00oo0OO$o00oOoO;

    const-string v3, "OkHttp %s Push Headers[%s]"

    const/4 v1, 0x2

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v8

    move-object v2, p0

    move v5, p1

    move-object v6, p2

    move v7, p3

    invoke-direct/range {v1 .. v7}, Lo0OOo00/o00oo0OO$o00oOoO;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILjava/util/List;Z)V

    invoke-interface {v0, v8}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public o00ooO00(ILokio/o00oOoO;IZ)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v5, Lokio/o00oOo00;

    invoke-direct {v5}, Lokio/o00oOo00;-><init>()V

    int-to-long v0, p3

    invoke-interface {p2, v0, v1}, Lokio/o00oOoO;->o0O00o00(J)V

    invoke-interface {p2, v5, v0, v1}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    iget-wide v2, v5, Lokio/o00oOo00;->o00oo0O:J

    cmp-long p2, v2, v0

    if-nez p2, :cond_0

    iget-object p2, p0, Lo0OOo00/o00oo0OO;->o00oo:Ljava/util/concurrent/ExecutorService;

    new-instance v8, Lo0OOo00/o00oo0OO$o00oo00O;

    const-string v2, "OkHttp %s Push Data[%s]"

    const/4 v0, 0x2

    new-array v3, v0, [Ljava/lang/Object;

    const/4 v0, 0x0

    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v1, v3, v0

    const/4 v0, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, v3, v0

    move-object v0, v8

    move-object v1, p0

    move v4, p1

    move v6, p3

    move v7, p4

    invoke-direct/range {v0 .. v7}, Lo0OOo00/o00oo0OO$o00oo00O;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILokio/o00oOo00;IZ)V

    invoke-interface {p2, v8}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :cond_0
    new-instance p1, Ljava/io/IOException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    iget-wide v0, v5, Lokio/o00oOo00;->o00oo0O:J

    invoke-virtual {p2, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string p4, " != "

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00ooOoo(ILjava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOoo:Ljava/util/Set;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object p2, Lo0OOo00/o00oOo00;->PROTOCOL_ERROR:Lo0OOo00/o00oOo00;

    invoke-virtual {p0, p1, p2}, Lo0OOo00/o00oo0OO;->o0O0o00o(ILo0OOo00/o00oOo00;)V

    monitor-exit p0

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOoo:Ljava/util/Set;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo:Ljava/util/concurrent/ExecutorService;

    new-instance v7, Lo0OOo00/o00oo0OO$o00oOo0O;

    const-string v3, "OkHttp %s Push Request[%s]"

    const/4 v1, 0x2

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v7

    move-object v2, p0

    move v5, p1

    move-object v6, p2

    invoke-direct/range {v1 .. v6}, Lo0OOo00/o00oo0OO$o00oOo0O;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILjava/util/List;)V

    invoke-interface {v0, v7}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public o00oooOo(ILo0OOo00/o00oOo00;)V
    .locals 8

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo:Ljava/util/concurrent/ExecutorService;

    new-instance v7, Lo0OOo00/o00oo0OO$o00oo0;

    const-string v3, "OkHttp %s Push Reset[%s]"

    const/4 v1, 0x2

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v7

    move-object v2, p0

    move v5, p1

    move-object v6, p2

    invoke-direct/range {v1 .. v6}, Lo0OOo00/o00oo0OO$o00oo0;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILo0OOo00/o00oOo00;)V

    invoke-interface {v0, v7}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public o00oooo0(ILjava/util/List;Z)Lo0OOo00/o00oo0O;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)",
            "Lo0OOo00/o00oo0O;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo00/o00oo0OO;->o00oo0O0:Z

    if-nez v0, :cond_0

    invoke-virtual {p0, p1, p2, p3}, Lo0OOo00/o00oo0OO;->o00oOoOo(ILjava/util/List;Z)Lo0OOo00/o00oo0O;

    move-result-object p1

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Client cannot push requests."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public declared-synchronized o0O000o(I)Lo0OOo00/o00oo0O;
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lo0OOo00/o00oo0O;

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized o0O000o0(I)Lo0OOo00/o00ooO0;
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooO00:Ljava/util/Map;

    if-eqz v0, :cond_0

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lo0OOo00/o00ooO0;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o0O00O0(Lo0OOo00/o00oOo00;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    monitor-enter v0

    :try_start_0
    monitor-enter p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-boolean v1, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    if-eqz v1, :cond_0

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    return-void

    :cond_0
    const/4 v1, 0x1

    :try_start_3
    iput-boolean v1, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    iget v1, p0, Lo0OOo00/o00oo0OO;->o00oo0o:I

    monitor-exit p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :try_start_4
    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    sget-object v3, Lo0OOOoOo/o0O0000O;->o00oOOo0:[B

    invoke-virtual {v2, v1, p1, v3}, Lo0OOo00/o0O0o;->o00oOoO(ILo0OOo00/o00oOo00;[B)V

    monitor-exit v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    return-void

    :catchall_0
    move-exception p1

    :try_start_5
    monitor-exit p0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    :try_start_6
    throw p1

    :catchall_1
    move-exception p1

    monitor-exit v0
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_1

    throw p1
.end method

.method public o0O00O0o()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lo0OOo00/o00oo0OO;->o0O00OOO(Z)V

    return-void
.end method

.method public o0O00OOO(Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    if-eqz p1, :cond_0

    iget-object p1, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {p1}, Lo0OOo00/o0O0o;->o00oOOoO()V

    iget-object p1, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    invoke-virtual {p1, v0}, Lo0OOo00/o0O0o;->o00ooOoo(Lo0OOo00/o0;)V

    iget-object p1, p0, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    invoke-virtual {p1}, Lo0OOo00/o0;->o00oOo0O()I

    move-result p1

    const v0, 0xffff

    if-eq p1, v0, :cond_0

    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    sub-int/2addr p1, v0

    int-to-long v2, p1

    const/4 p1, 0x0

    invoke-virtual {v1, p1, v2, v3}, Lo0OOo00/o0O0o;->o0OoOoOo(IJ)V

    :cond_0
    new-instance p1, Ljava/lang/Thread;

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOoO:Lo0OOo00/o00oo0OO$o00oo0O;

    invoke-direct {p1, v0}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    return-void
.end method

.method public o0O00o0O(IZLokio/o00oOo00;J)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, 0x0

    cmp-long v2, p4, v0

    const/4 v3, 0x0

    if-nez v2, :cond_0

    iget-object p4, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {p4, p2, p1, p3, v3}, Lo0OOo00/o0O0o;->o00oOo0O(ZILokio/o00oOo00;I)V

    return-void

    :cond_0
    :goto_0
    cmp-long v2, p4, v0

    if-lez v2, :cond_4

    monitor-enter p0

    :goto_1
    :try_start_0
    iget-wide v4, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    cmp-long v2, v4, v0

    if-gtz v2, :cond_2

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v2, v4}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->wait()V

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/io/IOException;

    const-string p2, "stream closed"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_2
    :try_start_1
    invoke-static {p4, p5, v4, v5}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v4

    long-to-int v2, v4

    iget-object v4, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    iget v4, v4, Lo0OOo00/o0O0o;->o00oo0o0:I

    invoke-static {v2, v4}, Ljava/lang/Math;->min(II)I

    move-result v2

    iget-wide v4, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    int-to-long v6, v2

    sub-long/2addr v4, v6

    iput-wide v4, p0, Lo0OOo00/o00oo0OO;->o00ooO:J

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    sub-long/2addr p4, v6

    iget-object v4, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    if-eqz p2, :cond_3

    cmp-long v5, p4, v0

    if-nez v5, :cond_3

    const/4 v5, 0x1

    goto :goto_2

    :cond_3
    move v5, v3

    :goto_2
    invoke-virtual {v4, v5, p1, p3, v2}, Lo0OOo00/o0O0o;->o00oOo0O(ZILokio/o00oOo00;I)V

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_3

    :catch_0
    :try_start_2
    new-instance p1, Ljava/io/InterruptedIOException;

    invoke-direct {p1}, Ljava/io/InterruptedIOException;-><init>()V

    throw p1

    :goto_3
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw p1

    :cond_4
    return-void
.end method

.method public o0O0OO0O(ZIILo0OOo00/o00ooO0;)V
    .locals 10

    sget-object v0, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance v9, Lo0OOo00/o00oo0OO$o00oOo00;

    const-string v3, "OkHttp %s ping %08x%08x"

    const/4 v1, 0x3

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    const/4 v1, 0x2

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v9

    move-object v2, p0

    move v5, p1

    move v6, p2

    move v7, p3

    move-object v8, p4

    invoke-direct/range {v1 .. v8}, Lo0OOo00/o00oo0OO$o00oOo00;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ZIILo0OOo00/o00ooO0;)V

    invoke-interface {v0, v9}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public o0O0OOo(IZLjava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IZ",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v0, p2, p1, p3}, Lo0OOo00/o0O0o;->o00oooOo(ZILjava/util/List;)V

    return-void
.end method

.method public o0O0o00o(ILo0OOo00/o00oOo00;)V
    .locals 8

    sget-object v0, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance v7, Lo0OOo00/o00oo0OO$o00oOOo0;

    const-string v3, "OkHttp %s stream %d"

    const/4 v1, 0x2

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v7

    move-object v2, p0

    move v5, p1

    move-object v6, p2

    invoke-direct/range {v1 .. v6}, Lo0OOo00/o00oo0OO$o00oOOo0;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILo0OOo00/o00oOo00;)V

    invoke-interface {v0, v7}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public o0O0o0O(IJ)V
    .locals 9

    sget-object v0, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance v8, Lo0OOo00/o00oo0OO$o00oOOoO;

    const-string v3, "OkHttp Window Update %s stream %d"

    const/4 v1, 0x2

    new-array v4, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object v2, p0, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object v2, v4, v1

    const/4 v1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v4, v1

    move-object v1, v8

    move-object v2, p0

    move v5, p1

    move-wide v6, p2

    invoke-direct/range {v1 .. v7}, Lo0OOo00/o00oo0OO$o00oOOoO;-><init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;IJ)V

    invoke-interface {v0, v8}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method

.method public o0OoOoOo(I)Z
    .locals 1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    and-int/2addr p1, v0

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public o0ooO(ZIILo0OOo00/o00ooO0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    monitor-enter v0

    if-eqz p4, :cond_0

    :try_start_0
    invoke-virtual {p4}, Lo0OOo00/o00ooO0;->o00oOo0O()V

    :cond_0
    iget-object p4, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {p4, p1, p2, p3}, Lo0OOo00/o0O0o;->o00oo0oO(ZII)V

    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public o0ooOoOO(Lo0OOo00/o0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    monitor-enter v0

    :try_start_0
    monitor-enter p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-boolean v1, p0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    if-nez v1, :cond_0

    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    invoke-virtual {v1, p1}, Lo0OOo00/o0;->o00oOoOo(Lo0OOo00/o0;)V

    iget-object v1, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v1, p1}, Lo0OOo00/o0O0o;->o00ooOoo(Lo0OOo00/o0;)V

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    return-void

    :cond_0
    :try_start_3
    new-instance p1, Lo0OOo00/o00oOOoO;

    invoke-direct {p1}, Lo0OOo00/o00oOOoO;-><init>()V

    throw p1

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :try_start_4
    throw p1

    :catchall_1
    move-exception p1

    monitor-exit v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    throw p1
.end method

.method public oo0OOoo(ILo0OOo00/o00oOo00;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    invoke-virtual {v0, p1, p2}, Lo0OOo00/o0O0o;->o00ooO0(ILo0OOo00/o00oOo00;)V

    return-void
.end method
