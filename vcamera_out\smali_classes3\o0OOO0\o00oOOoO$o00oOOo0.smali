.class public final Lo0OOO0/o00oOOoO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0/o00oOOoO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o00oOOo0"
.end annotation

.annotation runtime Lo0O0oooo/o0OOOO;
    bv = {
        0x1,
        0x0,
        0x3
    }
    k = 0x3
    mv = {
        0x1,
        0x4,
        0x1
    }
    xi = 0x30
.end annotation


# direct methods
.method public static o00oOOo0(Lo0OOO0/o00oOOoO;I)[Ljava/lang/Object;
    .locals 0
    .param p0    # Lo0OOO0/o00oOOoO;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OOO0/o00oOOoO<",
            "TT;>;I)[TT;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string p1, "this"

    invoke-static {p0, p1}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance p0, Lo0O0oooo/o0o0000;

    const-string p1, "Generated by Android Extensions automatically"

    invoke-direct {p0, p1}, Lo0O0oooo/o0o0000;-><init>(Ljava/lang/String;)V

    throw p0
.end method
