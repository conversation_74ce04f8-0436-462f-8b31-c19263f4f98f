.class public final Lo0OOO0o0/o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0008R!\u0010\t\u001a\u00020\u00028FX\u0087\u0084\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0003\u0010\u0004\u0012\u0004\u0008\u0007\u0010\u0008\u001a\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u000b"
    }
    d2 = {
        "Lo0OOO0o0/o00oOo0O;",
        "",
        "Landroid/content/SharedPreferences;",
        "o00oOOoO",
        "Lo0O0oooo/o0OOO0;",
        "o00oOOo0",
        "()Landroid/content/SharedPreferences;",
        "getMRemarkSharedPreferences$annotations",
        "()V",
        "mRemarkSharedPreferences",
        "<init>",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# static fields
.field public static final o00oOOo0:Lo0OOO0o0/o00oOo0O;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final o00oOOoO:Lo0O0oooo/o0OOO0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOO0o0/o00oOo0O;

    invoke-direct {v0}, Lo0OOO0o0/o00oOo0O;-><init>()V

    sput-object v0, Lo0OOO0o0/o00oOo0O;->o00oOOo0:Lo0OOO0o0/o00oOo0O;

    sget-object v0, Lo0OOO0o0/o00oOo0O$o00oOOo0;->INSTANCE:Lo0OOO0o0/o00oOo0O$o00oOOo0;

    invoke-static {v0}, Lo0O0oooo/o0OOO0OO;->o00oOo00(Lo0OO0Ooo/o00oOOoO;)Lo0O0oooo/o0OOO0;

    move-result-object v0

    sput-object v0, Lo0OOO0o0/o00oOo0O;->o00oOOoO:Lo0O0oooo/o0OOO0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final o00oOOo0()Landroid/content/SharedPreferences;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lo0OOO0o0/o00oOo0O;->o00oOOoO:Lo0O0oooo/o0OOO0;

    invoke-interface {v0}, Lo0O0oooo/o0OOO0;->getValue()Ljava/lang/Object;

    move-result-object v0

    const/16 v1, 0x23

    new-array v1, v1, [B

    fill-array-data v1, :array_0

    const/16 v2, 0x8

    new-array v2, v2, [B

    fill-array-data v2, :array_1

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroid/content/SharedPreferences;

    return-object v0

    :array_0
    .array-data 1
        0x41t
        0x21t
        -0x50t
        -0x17t
        -0x6ct
        0x29t
        -0x57t
        -0x7et
        0x10t
        0x27t
        -0x59t
        -0xat
        -0x16t
        0x2ct
        -0x66t
        -0x6bt
        0x18t
        0x22t
        -0x7bt
        -0x11t
        -0x24t
        0x22t
        -0x62t
        -0x6bt
        0x18t
        0x28t
        -0x4at
        -0x8t
        -0x36t
        0x7at
        -0x2dt
        -0x37t
        0x53t
        0x68t
        -0x4t
    .end array-data

    :array_1
    .array-data 1
        0x7dt
        0x46t
        -0x2bt
        -0x63t
        -0x47t
        0x44t
        -0x5t
        -0x19t
    .end array-data
.end method

.method public static synthetic o00oOOoO()V
    .locals 0
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    return-void
.end method
