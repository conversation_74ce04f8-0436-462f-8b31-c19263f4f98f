.class public Lo0OOo00/o00oo0OO$o00oOo0O;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO;->o00ooOoo(ILjava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:I

.field public final synthetic o00oo0Oo:Ljava/util/List;

.field public final synthetic o00oo0o0:Lo0OOo00/o00oo0OO;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILjava/util/List;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iput p4, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0O:I

    iput-object p5, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0Oo:Ljava/util/List;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 3

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00ooO0:Lo0OOo00/o00ooO;

    iget v1, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0O:I

    iget-object v2, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0Oo:Ljava/util/List;

    invoke-interface {v0, v1, v2}, Lo0OOo00/o00ooO;->o00oOOoO(ILjava/util/List;)Z

    move-result v0

    if-eqz v0, :cond_0

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    iget v1, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0O:I

    sget-object v2, Lo0OOo00/o00oOo00;->CANCEL:Lo0OOo00/o00oOo00;

    invoke-virtual {v0, v1, v2}, Lo0OOo00/o0O0o;->o00ooO0(ILo0OOo00/o00oOo00;)V

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0o0:Lo0OOo00/o00oo0OO;

    monitor-enter v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :try_start_1
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v1, v1, Lo0OOo00/o00oo0OO;->o00ooOoo:Ljava/util/Set;

    iget v2, p0, Lo0OOo00/o00oo0OO$o00oOo0O;->o00oo0O:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    throw v1
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    :catch_0
    :cond_0
    :goto_0
    return-void
.end method
