.class public final Lo0OOo00/o00oo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lo0OOOooo/o0O00OO;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo00/o00oo0$o00oOOo0;
    }
.end annotation


# static fields
.field public static final o00oOo0o:Lokio/o00oo00O;

.field public static final o00oOoO:Lokio/o00oo00O;

.field public static final o00oOoO0:Lokio/o00oo00O;

.field public static final o00oOoOO:Lokio/o00oo00O;

.field public static final o00oOoOo:Lokio/o00oo00O;

.field public static final o00oOoo0:Lokio/o00oo00O;

.field public static final o00oOooo:Lokio/o00oo00O;

.field public static final o00oo0:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lokio/o00oo00O;",
            ">;"
        }
    .end annotation
.end field

.field public static final o00oo00O:Lokio/o00oo00O;

.field public static final o00oo0OO:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lokio/o00oo00O;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final o00oOOoO:Lokhttp3/o0O00O0o;

.field public final o00oOo00:Lo0OOOooO/o0OoO00O;

.field public o00oOo0O:Lo0OOo00/o00oo0O;

.field public final o00oOooO:Lo0OOo00/o00oo0OO;


# direct methods
.method public static constructor <clinit>()V
    .locals 19

    const-string v0, "connection"

    invoke-static {v0}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v0

    sput-object v0, Lo0OOo00/o00oo0;->o00oOo0o:Lokio/o00oo00O;

    const-string v1, "host"

    invoke-static {v1}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v1

    sput-object v1, Lo0OOo00/o00oo0;->o00oOoO0:Lokio/o00oo00O;

    const-string v2, "keep-alive"

    invoke-static {v2}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v2

    sput-object v2, Lo0OOo00/o00oo0;->o00oOoO:Lokio/o00oo00O;

    const-string v3, "proxy-connection"

    invoke-static {v3}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v3

    sput-object v3, Lo0OOo00/o00oo0;->o00oOoOO:Lokio/o00oo00O;

    const-string v4, "transfer-encoding"

    invoke-static {v4}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v4

    sput-object v4, Lo0OOo00/o00oo0;->o00oOoOo:Lokio/o00oo00O;

    const-string v5, "te"

    invoke-static {v5}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v5

    sput-object v5, Lo0OOo00/o00oo0;->o00oOoo0:Lokio/o00oo00O;

    const-string v6, "encoding"

    invoke-static {v6}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v6

    sput-object v6, Lo0OOo00/o00oo0;->o00oOooo:Lokio/o00oo00O;

    const-string v7, "upgrade"

    invoke-static {v7}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v7

    sput-object v7, Lo0OOo00/o00oo0;->o00oo00O:Lokio/o00oo00O;

    const/16 v8, 0xc

    new-array v8, v8, [Lokio/o00oo00O;

    const/4 v9, 0x0

    aput-object v0, v8, v9

    const/4 v10, 0x1

    aput-object v1, v8, v10

    const/4 v11, 0x2

    aput-object v2, v8, v11

    const/4 v12, 0x3

    aput-object v3, v8, v12

    const/4 v13, 0x4

    aput-object v5, v8, v13

    const/4 v14, 0x5

    aput-object v4, v8, v14

    const/4 v15, 0x6

    aput-object v6, v8, v15

    const/16 v16, 0x7

    aput-object v7, v8, v16

    sget-object v17, Lo0OOo00/o00oOo0O;->o00oOo0o:Lokio/o00oo00O;

    const/16 v15, 0x8

    aput-object v17, v8, v15

    const/16 v17, 0x9

    sget-object v18, Lo0OOo00/o00oOo0O;->o00oOoO0:Lokio/o00oo00O;

    aput-object v18, v8, v17

    const/16 v17, 0xa

    sget-object v18, Lo0OOo00/o00oOo0O;->o00oOoO:Lokio/o00oo00O;

    aput-object v18, v8, v17

    const/16 v17, 0xb

    sget-object v18, Lo0OOo00/o00oOo0O;->o00oOoOO:Lokio/o00oo00O;

    aput-object v18, v8, v17

    invoke-static {v8}, Lo0OOOoOo/o0O0000O;->o00oo0O0([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v8

    sput-object v8, Lo0OOo00/o00oo0;->o00oo0:Ljava/util/List;

    new-array v8, v15, [Lokio/o00oo00O;

    aput-object v0, v8, v9

    aput-object v1, v8, v10

    aput-object v2, v8, v11

    aput-object v3, v8, v12

    aput-object v5, v8, v13

    aput-object v4, v8, v14

    const/4 v0, 0x6

    aput-object v6, v8, v0

    aput-object v7, v8, v16

    invoke-static {v8}, Lo0OOOoOo/o0O0000O;->o00oo0O0([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lo0OOo00/o00oo0;->o00oo0OO:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lokhttp3/o0O00O0o;Lo0OOOooO/o0OoO00O;Lo0OOo00/o00oo0OO;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo0OOo00/o00oo0;->o00oOOoO:Lokhttp3/o0O00O0o;

    iput-object p2, p0, Lo0OOo00/o00oo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    iput-object p3, p0, Lo0OOo00/o00oo0;->o00oOooO:Lo0OOo00/o00oo0OO;

    return-void
.end method

.method public static o00oOoO(Ljava/util/List;)Lokhttp3/o0O00o00$o00oOOo0;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)",
            "Lokhttp3/o0O00o00$o00oOOo0;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lokhttp3/o0O000Oo$o00oOOo0;

    invoke-direct {v0}, Lokhttp3/o0O000Oo$o00oOOo0;-><init>()V

    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v4, v2

    :goto_0
    if-ge v3, v1, :cond_3

    invoke-interface {p0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lo0OOo00/o00oOo0O;

    if-nez v5, :cond_0

    if-eqz v4, :cond_2

    iget v5, v4, Lo0OOOooo/o0O0O0Oo;->o00oOOoO:I

    const/16 v6, 0x64

    if-ne v5, v6, :cond_2

    new-instance v0, Lokhttp3/o0O000Oo$o00oOOo0;

    invoke-direct {v0}, Lokhttp3/o0O000Oo$o00oOOo0;-><init>()V

    move-object v4, v2

    goto :goto_1

    :cond_0
    iget-object v6, v5, Lo0OOo00/o00oOo0O;->o00oOOo0:Lokio/o00oo00O;

    iget-object v5, v5, Lo0OOo00/o00oOo0O;->o00oOOoO:Lokio/o00oo00O;

    invoke-virtual {v5}, Lokio/o00oo00O;->utf8()Ljava/lang/String;

    move-result-object v5

    sget-object v7, Lo0OOo00/o00oOo0O;->o00oOo0O:Lokio/o00oo00O;

    invoke-virtual {v6, v7}, Lokio/o00oo00O;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_1

    new-instance v4, Ljava/lang/StringBuilder;

    const-string v6, "HTTP/1.1 "

    invoke-direct {v4, v6}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lo0OOOooo/o0O0O0Oo;->o00oOOoO(Ljava/lang/String;)Lo0OOOooo/o0O0O0Oo;

    move-result-object v4

    goto :goto_1

    :cond_1
    sget-object v7, Lo0OOo00/o00oo0;->o00oo0OO:Ljava/util/List;

    invoke-interface {v7, v6}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_2

    sget-object v7, Lo0OOOoOo/o0;->o00oOOo0:Lo0OOOoOo/o0;

    invoke-virtual {v6}, Lokio/o00oo00O;->utf8()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v7, v0, v6, v5}, Lo0OOOoOo/o0;->o00oOOoO(Lokhttp3/o0O000Oo$o00oOOo0;Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    if-eqz v4, :cond_4

    new-instance p0, Lokhttp3/o0O00o00$o00oOOo0;

    invoke-direct {p0}, Lokhttp3/o0O00o00$o00oOOo0;-><init>()V

    sget-object v1, Lokhttp3/o0O00O;->HTTP_2:Lokhttp3/o0O00O;

    iput-object v1, p0, Lokhttp3/o0O00o00$o00oOOo0;->o00oOOoO:Lokhttp3/o0O00O;

    iget v1, v4, Lo0OOOooo/o0O0O0Oo;->o00oOOoO:I

    iput v1, p0, Lokhttp3/o0O00o00$o00oOOo0;->o00oOo00:I

    iget-object v1, v4, Lo0OOOooo/o0O0O0Oo;->o00oOo00:Ljava/lang/String;

    iput-object v1, p0, Lokhttp3/o0O00o00$o00oOOo0;->o00oOooO:Ljava/lang/String;

    new-instance v1, Lokhttp3/o0O000Oo;

    invoke-direct {v1, v0}, Lokhttp3/o0O000Oo;-><init>(Lokhttp3/o0O000Oo$o00oOOo0;)V

    invoke-virtual {p0, v1}, Lokhttp3/o0O00o00$o00oOOo0;->o00oOoOo(Lokhttp3/o0O000Oo;)Lokhttp3/o0O00o00$o00oOOo0;

    move-result-object p0

    return-object p0

    :cond_4
    new-instance p0, Ljava/net/ProtocolException;

    const-string v0, "Expected \':status\' header not present"

    invoke-direct {p0, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static o00oOoO0(Lokhttp3/o0O00OOO;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lokhttp3/o0O00OOO;",
            ")",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lokhttp3/o0O00OOO;->o00oOo00:Lokhttp3/o0O000Oo;

    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, v0, Lokhttp3/o0O000Oo;->o00oOOo0:[Ljava/lang/String;

    array-length v2, v2

    div-int/lit8 v2, v2, 0x2

    add-int/lit8 v2, v2, 0x4

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    new-instance v2, Lo0OOo00/o00oOo0O;

    sget-object v3, Lo0OOo00/o00oOo0O;->o00oOo0o:Lokio/o00oo00O;

    iget-object v4, p0, Lokhttp3/o0O00OOO;->o00oOOoO:Ljava/lang/String;

    invoke-direct {v2, v3, v4}, Lo0OOo00/o00oOo0O;-><init>(Lokio/o00oo00O;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    new-instance v2, Lo0OOo00/o00oOo0O;

    sget-object v3, Lo0OOo00/o00oOo0O;->o00oOoO0:Lokio/o00oo00O;

    iget-object v4, p0, Lokhttp3/o0O00OOO;->o00oOOo0:Lokhttp3/o0O000o0;

    invoke-static {v4}, Lo0OOOooo/o0oO0O0o;->o00oOo00(Lokhttp3/o0O000o0;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v3, v4}, Lo0OOo00/o00oOo0O;-><init>(Lokio/o00oo00O;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    const-string v2, "Host"

    invoke-virtual {p0, v2}, Lokhttp3/o0O00OOO;->o00oOo00(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_0

    new-instance v3, Lo0OOo00/o00oOo0O;

    sget-object v4, Lo0OOo00/o00oOo0O;->o00oOoOO:Lokio/o00oo00O;

    invoke-direct {v3, v4, v2}, Lo0OOo00/o00oOo0O;-><init>(Lokio/o00oo00O;Ljava/lang/String;)V

    invoke-virtual {v1, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_0
    new-instance v2, Lo0OOo00/o00oOo0O;

    sget-object v3, Lo0OOo00/o00oOo0O;->o00oOoO:Lokio/o00oo00O;

    iget-object p0, p0, Lokhttp3/o0O00OOO;->o00oOOo0:Lokhttp3/o0O000o0;

    iget-object p0, p0, Lokhttp3/o0O000o0;->o00oOOo0:Ljava/lang/String;

    invoke-direct {v2, v3, p0}, Lo0OOo00/o00oOo0O;-><init>(Lokio/o00oo00O;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    iget-object p0, v0, Lokhttp3/o0O000Oo;->o00oOOo0:[Ljava/lang/String;

    array-length p0, p0

    div-int/lit8 p0, p0, 0x2

    const/4 v2, 0x0

    :goto_0
    if-ge v2, p0, :cond_2

    invoke-virtual {v0, v2}, Lokhttp3/o0O000Oo;->o00oOooO(I)Ljava/lang/String;

    move-result-object v3

    sget-object v4, Ljava/util/Locale;->US:Ljava/util/Locale;

    invoke-virtual {v3, v4}, Ljava/lang/String;->toLowerCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v3

    sget-object v4, Lo0OOo00/o00oo0;->o00oo0:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_1

    new-instance v4, Lo0OOo00/o00oOo0O;

    invoke-virtual {v0, v2}, Lokhttp3/o0O000Oo;->o00oOoo0(I)Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v3, v5}, Lo0OOo00/o00oOo0O;-><init>(Lokio/o00oo00O;Ljava/lang/String;)V

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-object v1
.end method


# virtual methods
.method public cancel()V
    .locals 2

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    if-eqz v0, :cond_0

    sget-object v1, Lo0OOo00/o00oOo00;->CANCEL:Lo0OOo00/o00oOo00;

    invoke-virtual {v0, v1}, Lo0OOo00/o00oo0O;->o00oOo0o(Lo0OOo00/o00oOo00;)V

    :cond_0
    return-void
.end method

.method public o00oOOo0()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oOoo0()Lokio/o0O00O0;

    move-result-object v0

    invoke-interface {v0}, Lokio/o0O00O0;->close()V

    return-void
.end method

.method public o00oOOoO(Lokhttp3/o0O00OOO;J)Lokio/o0O00O0;
    .locals 0

    iget-object p1, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    invoke-virtual {p1}, Lo0OOo00/o00oo0O;->o00oOoo0()Lokio/o0O00O0;

    move-result-object p1

    return-object p1
.end method

.method public o00oOo00(Lokhttp3/o0O00OOO;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p1, Lokhttp3/o0O00OOO;->o00oOooO:Lokhttp3/o0oO0Ooo;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    move v0, v1

    :goto_0
    invoke-static {p1}, Lo0OOo00/o00oo0;->o00oOoO0(Lokhttp3/o0O00OOO;)Ljava/util/List;

    move-result-object p1

    iget-object v2, p0, Lo0OOo00/o00oo0;->o00oOooO:Lo0OOo00/o00oo0OO;

    invoke-virtual {v2, v1, p1, v0}, Lo0OOo00/o00oo0OO;->o00oOoOo(ILjava/util/List;Z)Lo0OOo00/o00oo0O;

    move-result-object p1

    iput-object p1, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    iget-object p1, p1, Lo0OOo00/o00oo0O;->o00oOoOo:Lo0OOo00/o00oo0O$o00oOo00;

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOOoO:Lokhttp3/o0O00O0o;

    invoke-virtual {v0}, Lokhttp3/o0O00O0o;->o00ooOO0()I

    move-result v0

    int-to-long v0, v0

    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {p1, v0, v1, v2}, Lokio/o0O00O0o;->o00oOoO(JLjava/util/concurrent/TimeUnit;)Lokio/o0O00O0o;

    iget-object p1, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    iget-object p1, p1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOOoO:Lokhttp3/o0O00O0o;

    invoke-virtual {v0}, Lokhttp3/o0O00O0o;->o00ooOoo()I

    move-result v0

    int-to-long v0, v0

    invoke-virtual {p1, v0, v1, v2}, Lokio/o0O00O0o;->o00oOoO(JLjava/util/concurrent/TimeUnit;)Lokio/o0O00O0o;

    return-void
.end method

.method public o00oOo0O(Z)Lokhttp3/o0O00o00$o00oOOo0;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oo0oO()Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lo0OOo00/o00oo0;->o00oOoO(Ljava/util/List;)Lokhttp3/o0O00o00$o00oOOo0;

    move-result-object v0

    if-eqz p1, :cond_0

    sget-object p1, Lo0OOOoOo/o0;->o00oOOo0:Lo0OOOoOo/o0;

    invoke-virtual {p1, v0}, Lo0OOOoOo/o0;->o00oOooO(Lokhttp3/o0O00o00$o00oOOo0;)I

    move-result p1

    const/16 v1, 0x64

    if-ne p1, v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    return-object v0
.end method

.method public o00oOo0o()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0;->o00oOooO:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0}, Lo0OOo00/o00oo0OO;->flush()V

    return-void
.end method

.method public o00oOooO(Lokhttp3/o0O00o00;)Lokhttp3/o0O00oO0;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lo0OOo00/o00oo0$o00oOOo0;

    iget-object v1, p0, Lo0OOo00/o00oo0;->o00oOo0O:Lo0OOo00/o00oo0O;

    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOoO:Lo0OOo00/o00oo0O$o00oOOoO;

    invoke-direct {v0, p0, v1}, Lo0OOo00/o00oo0$o00oOOo0;-><init>(Lo0OOo00/o00oo0;Lokio/o0OoO00O;)V

    new-instance v1, Lo0OOOooo/o0O0O0O;

    iget-object p1, p1, Lokhttp3/o0O00o00;->o00oo0oO:Lokhttp3/o0O000Oo;

    invoke-static {v0}, Lokio/o0O00000;->o00oOooO(Lokio/o0OoO00O;)Lokio/o00oOoO;

    move-result-object v0

    invoke-direct {v1, p1, v0}, Lo0OOOooo/o0O0O0O;-><init>(Lokhttp3/o0O000Oo;Lokio/o00oOoO;)V

    return-object v1
.end method
