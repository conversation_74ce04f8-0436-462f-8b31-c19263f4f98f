.class public final Lo0OOo00/o00oo0O$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0O00O0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOOo0"
.end annotation


# static fields
.field public static final o00oo0o:J = 0x4000L

.field public static final synthetic o00oo0oO:Z


# instance fields
.field public o00oo0O:Z

.field public final o00oo0O0:Lokio/o00oOo00;

.field public o00oo0Oo:Z

.field public final synthetic o00oo0o0:Lo0OOo00/o00oo0O;


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lo0OOo00/o00oo0O;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Lokio/o00oOo00;

    invoke-direct {p1}, Lokio/o00oOo00;-><init>()V

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    return-void
.end method


# virtual methods
.method public close()V
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    monitor-enter v0

    :try_start_0
    iget-boolean v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O:Z

    if-eqz v1, :cond_0

    monitor-exit v0

    return-void

    :cond_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v1, v0, Lo0OOo00/o00oo0O;->o00oOoOO:Lo0OOo00/o00oo0O$o00oOOo0;

    iget-boolean v1, v1, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0Oo:Z

    const/4 v2, 0x1

    if-nez v1, :cond_2

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide v3, v1, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v5, 0x0

    cmp-long v1, v3, v5

    if-lez v1, :cond_1

    :goto_0
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide v0, v0, Lokio/o00oOo00;->o00oo0O:J

    cmp-long v0, v0, v5

    if-lez v0, :cond_2

    invoke-virtual {p0, v2}, Lo0OOo00/o00oo0O$o00oOOo0;->o00oOOo0(Z)V

    goto :goto_0

    :cond_1
    iget-object v7, v0, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget v8, v0, Lo0OOo00/o00oo0O;->o00oOo00:I

    const/4 v9, 0x1

    const/4 v10, 0x0

    const-wide/16 v11, 0x0

    invoke-virtual/range {v7 .. v12}, Lo0OOo00/o00oo0OO;->o0O00o0O(IZLokio/o00oOo00;J)V

    :cond_2
    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    monitor-enter v1

    :try_start_1
    iput-boolean v2, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O:Z

    monitor-exit v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0}, Lo0OOo00/o00oo0OO;->flush()V

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oOOoO()V

    return-void

    :catchall_0
    move-exception v0

    :try_start_2
    monitor-exit v1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw v0

    :catchall_1
    move-exception v1

    :try_start_3
    monitor-exit v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    throw v1
.end method

.method public flush()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    invoke-virtual {v1}, Lo0OOo00/o00oo0O;->o00oOo00()V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_0
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide v0, v0, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-lez v0, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lo0OOo00/o00oo0O$o00oOOo0;->o00oOOo0(Z)V

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0}, Lo0OOo00/o00oo0OO;->flush()V

    goto :goto_0

    :cond_0
    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public final o00oOOo0(Z)V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v1}, Lokio/o00oOOo0;->o00oo00O()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    :goto_0
    :try_start_1
    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-wide v2, v1, Lo0OOo00/o00oo0O;->o00oOOoO:J

    const-wide/16 v4, 0x0

    cmp-long v2, v2, v4

    if-gtz v2, :cond_0

    iget-boolean v2, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0Oo:Z

    if-nez v2, :cond_0

    iget-boolean v2, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O:Z

    if-nez v2, :cond_0

    iget-object v2, v1, Lo0OOo00/o00oo0O;->o00oOooo:Lo0OOo00/o00oOo00;

    if-nez v2, :cond_0

    invoke-virtual {v1}, Lo0OOo00/o00oo0O;->o0O0o()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_0

    :cond_0
    :try_start_2
    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v1}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    invoke-virtual {v1}, Lo0OOo00/o00oo0O;->o00oOo00()V

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-wide v1, v1, Lo0OOo00/o00oo0O;->o00oOOoO:J

    iget-object v3, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide v3, v3, Lokio/o00oOo00;->o00oo0O:J

    invoke-static {v1, v2, v3, v4}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v9

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-wide v2, v1, Lo0OOo00/o00oo0O;->o00oOOoO:J

    sub-long/2addr v2, v9

    iput-wide v2, v1, Lo0OOo00/o00oo0O;->o00oOOoO:J

    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    iget-object v0, v1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v0}, Lokio/o00oOOo0;->o00oo00O()V

    :try_start_3
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v5, v0, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget v6, v0, Lo0OOo00/o00oo0O;->o00oOo00:I

    if-eqz p1, :cond_1

    iget-object p1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide v0, p1, Lokio/o00oOo00;->o00oo0O:J

    cmp-long p1, v9, v0

    if-nez p1, :cond_1

    const/4 p1, 0x1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    move v7, p1

    iget-object v8, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    invoke-virtual/range {v5 .. v10}, Lo0OOo00/o00oo0OO;->o0O00o0O(IZLokio/o00oOo00;J)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    iget-object p1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object p1, p1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {p1}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    return-void

    :catchall_0
    move-exception p1

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    throw p1

    :catchall_1
    move-exception p1

    :try_start_4
    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v1}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    throw p1

    :catchall_2
    move-exception p1

    monitor-exit v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    throw p1
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0o0:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOoo0:Lo0OOo00/o00oo0O$o00oOo00;

    return-object v0
.end method

.method public o0O0000o(Lokio/o00oOo00;J)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    invoke-virtual {v0, p1, p2, p3}, Lokio/o00oOo00;->o0O0000o(Lokio/o00oOo00;J)V

    :goto_0
    iget-object p1, p0, Lo0OOo00/o00oo0O$o00oOOo0;->o00oo0O0:Lokio/o00oOo00;

    iget-wide p1, p1, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v0, 0x4000

    cmp-long p1, p1, v0

    if-ltz p1, :cond_0

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lo0OOo00/o00oo0O$o00oOOo0;->o00oOOo0(Z)V

    goto :goto_0

    :cond_0
    return-void
.end method
