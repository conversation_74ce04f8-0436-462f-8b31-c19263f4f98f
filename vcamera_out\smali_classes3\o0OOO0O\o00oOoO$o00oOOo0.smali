.class public interface abstract Lo0OOo0O/o00oOoO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oOoO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "o00oOOo0"
.end annotation


# virtual methods
.method public abstract o00oOo0O(Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract o00oOo0o(Lokio/o00oo00O;)V
.end method

.method public abstract o00oOoO(Lokio/o00oo00O;)V
.end method

.method public abstract o00oOoOo(ILjava/lang/String;)V
.end method

.method public abstract o00oOooO(Lokio/o00oo00O;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
