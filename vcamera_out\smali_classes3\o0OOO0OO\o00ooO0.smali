.class public final Lo0OOO0oo/o00ooO0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0015\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0008\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0004\u0012\u0006\u0010\n\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\t\u0010\u0003\u001a\u00020\u0002H\u00c6\u0003J\t\u0010\u0005\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u0007\u001a\u00020\u0006H\u00c6\u0003J\'\u0010\u000b\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00022\u0008\u0008\u0002\u0010\t\u001a\u00020\u00042\u0008\u0008\u0002\u0010\n\u001a\u00020\u0006H\u00c6\u0001J\t\u0010\u000c\u001a\u00020\u0006H\u00d6\u0001J\t\u0010\r\u001a\u00020\u0002H\u00d6\u0001J\u0013\u0010\u000f\u001a\u00020\u00042\u0008\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003R\u0017\u0010\u0008\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012R\u0017\u0010\t\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015R\u0017\u0010\n\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018\u00a8\u0006\u001b"
    }
    d2 = {
        "Lo0OOO0oo/o00ooO0;",
        "",
        "",
        "o00oOOo0",
        "",
        "o00oOOoO",
        "",
        "o00oOo00",
        "userID",
        "success",
        "msg",
        "o00oOooO",
        "toString",
        "hashCode",
        "other",
        "equals",
        "I",
        "o00oOoO",
        "()I",
        "Z",
        "o00oOoO0",
        "()Z",
        "Ljava/lang/String;",
        "o00oOo0o",
        "()Ljava/lang/String;",
        "<init>",
        "(IZLjava/lang/String;)V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# instance fields
.field public final o00oOOo0:I

.field public final o00oOOoO:Z

.field public final o00oOo00:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(IZLjava/lang/String;)V
    .locals 2
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x3

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p3, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    iput-boolean p2, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    iput-object p3, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    return-void

    :array_0
    .array-data 1
        -0x7ft
        0x6dt
        -0x4at
    .end array-data

    :array_1
    .array-data 1
        -0x14t
        0x1et
        -0x2ft
        0x68t
        -0x54t
        -0x7dt
        0xct
        -0x71t
    .end array-data
.end method

.method public static synthetic o00oOo0O(Lo0OOO0oo/o00ooO0;IZLjava/lang/String;ILjava/lang/Object;)Lo0OOO0oo/o00ooO0;
    .locals 0

    and-int/lit8 p5, p4, 0x1

    if-eqz p5, :cond_0

    iget p1, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    :cond_0
    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_1

    iget-boolean p2, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    :cond_1
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_2

    iget-object p3, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    :cond_2
    invoke-virtual {p0, p1, p2, p3}, Lo0OOO0oo/o00ooO0;->o00oOooO(IZLjava/lang/String;)Lo0OOO0oo/o00ooO0;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lo0OOO0oo/o00ooO0;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lo0OOO0oo/o00ooO0;

    iget v1, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    iget v3, p1, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-boolean v1, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    iget-boolean v3, p1, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    iget-object p1, p1, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/o0ooO;->o00oOoO0(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget v0, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    invoke-static {v0}, Ljava/lang/Integer;->hashCode(I)I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    :cond_0
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    return v1
.end method

.method public final o00oOOo0()I
    .locals 1

    iget v0, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    return v0
.end method

.method public final o00oOOoO()Z
    .locals 1

    iget-boolean v0, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    return v0
.end method

.method public final o00oOo00()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    return-object v0
.end method

.method public final o00oOo0o()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    return-object v0
.end method

.method public final o00oOoO()I
    .locals 1

    iget v0, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    return v0
.end method

.method public final o00oOoO0()Z
    .locals 1

    iget-boolean v0, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    return v0
.end method

.method public final o00oOooO(IZLjava/lang/String;)Lo0OOO0oo/o00ooO0;
    .locals 2
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const/4 v0, 0x3

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p3, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lo0OOO0oo/o00ooO0;

    invoke-direct {v0, p1, p2, p3}, Lo0OOO0oo/o00ooO0;-><init>(IZLjava/lang/String;)V

    return-object v0

    :array_0
    .array-data 1
        -0x26t
        -0x51t
        0x19t
    .end array-data

    :array_1
    .array-data 1
        -0x49t
        -0x24t
        0x7et
        0x18t
        -0x69t
        -0x67t
        0x11t
        -0x74t
    .end array-data
.end method

.method public toString()Ljava/lang/String;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v1, 0x23

    new-array v1, v1, [B

    fill-array-data v1, :array_0

    const/16 v2, 0x8

    new-array v3, v2, [B

    fill-array-data v3, :array_1

    invoke-static {v1, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lo0OOO0oo/o00ooO0;->o00oOOo0:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0xa

    new-array v1, v1, [B

    fill-array-data v1, :array_2

    new-array v3, v2, [B

    fill-array-data v3, :array_3

    invoke-static {v1, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lo0OOO0oo/o00ooO0;->o00oOOoO:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const/4 v1, 0x6

    new-array v1, v1, [B

    fill-array-data v1, :array_4

    new-array v2, v2, [B

    fill-array-data v2, :array_5

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lo0OOO0oo/o00ooO0;->o00oOo00:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :array_0
    .array-data 1
        -0x74t
        -0x35t
        -0x14t
        -0x72t
        -0x34t
        -0x2ft
        -0x2ft
        -0x6dt
        -0x42t
        -0x3dt
        -0x18t
        -0x7bt
        -0x3et
        -0x25t
        -0x2dt
        -0x64t
        -0x7dt
        -0x3ct
        -0xct
        -0x61t
        -0x1ft
        -0x2et
        -0x22t
        -0x50t
        -0x51t
        -0x35t
        -0x17t
        -0x3dt
        -0xbt
        -0x33t
        -0x29t
        -0x80t
        -0x7dt
        -0x12t
        -0x46t
    .end array-data

    :array_1
    .array-data 1
        -0x36t
        -0x56t
        -0x79t
        -0x15t
        -0x80t
        -0x42t
        -0x4et
        -0xet
    .end array-data

    :array_2
    .array-data 1
        0x22t
        -0x37t
        -0x6dt
        0x6ft
        -0x19t
        0x2ct
        -0x4et
        -0x71t
        0x7dt
        -0x2ct
    .end array-data

    nop

    :array_3
    .array-data 1
        0xet
        -0x17t
        -0x20t
        0x1at
        -0x7ct
        0x4ft
        -0x29t
        -0x4t
    .end array-data

    :array_4
    .array-data 1
        0x68t
        -0x73t
        -0x5bt
        -0x21t
        -0x5at
        0x7bt
    .end array-data

    nop

    :array_5
    .array-data 1
        0x44t
        -0x53t
        -0x38t
        -0x54t
        -0x3ft
        0x46t
        -0x7bt
        -0x61t
    .end array-data
.end method
