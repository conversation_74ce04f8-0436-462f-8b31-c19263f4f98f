.class public Lo0OOO0Oo/o00oo00O$o00oOOoO;
.super Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAdLoadCallback;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oo00O;->o00oOoO(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oo00O;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo00O;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    invoke-direct {p0}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAdLoadCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)V
    .locals 2

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    invoke-static {v0, p1}, Lo0OOO0Oo/o00oo00O;->o00oOooO(Lo0OOO0Oo/o00oo00O;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    iget-object p1, p1, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    new-instance v0, Lo0OOO0Oo/o00oo00O$o00oOOoO$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oo00O$o00oOOoO$o00oOOo0;-><init>(Lo0OOO0Oo/o00oo00O$o00oOOoO;)V

    invoke-virtual {p1, v0}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;->setOnPaidEventListener(Lcom/google/android/gms/ads/OnPaidEventListener;)V

    const/16 p1, 0x14

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_1

    invoke-static {p1, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 p1, 0xd

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v0, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oo00O;->o00oOOoO:Z

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p1, Lo0OOO0Oo/o00oo00O;->o00oOo00:J

    return-void

    nop

    :array_0
    .array-data 1
        -0xct
        -0x1t
        -0xft
        -0x36t
        -0x45t
        0x21t
        0x2t
        0x21t
        -0x39t
        -0x2et
        -0x2et
        -0x28t
        -0x50t
        0x32t
        0x11t
        0x12t
        -0x24t
        -0x1t
        -0x27t
        -0x3ct
    .end array-data

    :array_1
    .array-data 1
        -0x4bt
        -0x65t
        -0x44t
        -0x55t
        -0x2bt
        0x40t
        0x65t
        0x44t
    .end array-data

    :array_2
    .array-data 1
        -0x41t
        -0x4ct
        -0x22t
        0x2at
        0x77t
        -0xet
        0x4et
        -0x4ft
        -0x4bt
        -0x42t
        -0x4ft
        0x60t
        0x15t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x30t
        -0x26t
        -0x61t
        0x4et
        0x3bt
        -0x63t
        0x2ft
        -0x2bt
    .end array-data
.end method

.method public onAdFailedToLoad(Lcom/google/android/gms/ads/LoadAdError;)V
    .locals 3

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x11

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-static {p1}, Ljava/util/Objects;->toString(Ljava/lang/Object;)Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lo0OOO0Oo/o00oo00O;->o00oOooO(Lo0OOO0Oo/o00oo00O;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oo00O;->o00oOOoO:Z

    return-void

    nop

    :array_0
    .array-data 1
        -0x61t
        -0x37t
        -0x14t
        -0x6at
        0x5et
        0x24t
        0x3dt
        -0x19t
        -0x54t
        -0x1ct
        -0x31t
        -0x7ct
        0x55t
        0x37t
        0x2et
        -0x2ct
        -0x49t
        -0x37t
        -0x3ct
        -0x68t
    .end array-data

    :array_1
    .array-data 1
        -0x22t
        -0x53t
        -0x5ft
        -0x9t
        0x30t
        0x45t
        0x5at
        -0x7et
    .end array-data

    :array_2
    .array-data 1
        0x41t
        0xat
        -0x5et
        -0x34t
        -0x66t
        -0x77t
        -0x17t
        -0x26t
        0x4bt
        0x0t
        -0x49t
        -0x39t
        -0x70t
        -0x79t
        -0x1ft
        -0x2et
        0x14t
    .end array-data

    nop

    :array_3
    .array-data 1
        0x2et
        0x64t
        -0x1dt
        -0x58t
        -0x24t
        -0x18t
        -0x80t
        -0x4at
    .end array-data
.end method

.method public bridge synthetic onAdLoaded(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    invoke-virtual {p0, p1}, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0(Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)V

    return-void
.end method
