.class public Lo0OOO0Oo/o00oo00O$o00oOo00;
.super Lcom/google/android/gms/ads/FullScreenContentCallback;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oo00O;->o00oOoo0(Landroid/app/Activity;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Landroid/app/Activity;

.field public final synthetic o00oOOoO:Lo0OOO0Oo/o00oo00O;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo00O;Landroid/app/Activity;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOoO:Lo0OOO0Oo/o00oo00O;

    iput-object p2, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOo0:Landroid/app/Activity;

    invoke-direct {p0}, Lcom/google/android/gms/ads/FullScreenContentCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public onAdDismissedFullScreenContent()V
    .locals 3

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x21

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOoO:Lo0OOO0Oo/o00oo00O;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lo0OOO0Oo/o00oo00O;->o00oOooO(Lo0OOO0Oo/o00oo00O;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOoO:Lo0OOO0Oo/o00oo00O;

    iget-object v1, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOo0:Landroid/app/Activity;

    invoke-virtual {v0, v1}, Lo0OOO0Oo/o00oo00O;->o00oOoO(Landroid/content/Context;)V

    return-void

    :array_0
    .array-data 1
        0x30t
        -0x41t
        -0x53t
        -0x63t
        -0x52t
        0x54t
        -0x2dt
        -0x18t
        0x3t
        -0x6et
        -0x72t
        -0x71t
        -0x5bt
        0x47t
        -0x40t
        -0x25t
        0x18t
        -0x41t
        -0x7bt
        -0x6dt
    .end array-data

    :array_1
    .array-data 1
        0x71t
        -0x25t
        -0x20t
        -0x4t
        -0x40t
        0x35t
        -0x4ct
        -0x73t
    .end array-data

    :array_2
    .array-data 1
        -0x74t
        0x58t
        0x55t
        -0x3bt
        -0x75t
        -0x5ft
        -0x26t
        0x2at
        -0x76t
        0x45t
        0x67t
        -0x3ct
        -0x55t
        -0x72t
        -0x24t
        0x2bt
        -0x71t
        0x65t
        0x77t
        -0x2dt
        -0x56t
        -0x53t
        -0x39t
        0x4t
        -0x74t
        0x58t
        0x60t
        -0x3ct
        -0x5ft
        -0x44t
        -0x79t
        0x69t
        -0x33t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x1dt
        0x36t
        0x14t
        -0x5ft
        -0x31t
        -0x38t
        -0x57t
        0x47t
    .end array-data
.end method

.method public onAdFailedToShowFullScreenContent(Lcom/google/android/gms/ads/AdError;)V
    .locals 3

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x23

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {p1}, Lcom/google/android/gms/ads/AdError;->getMessage()Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOoO:Lo0OOO0Oo/o00oo00O;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lo0OOO0Oo/o00oo00O;->o00oOooO(Lo0OOO0Oo/o00oo00O;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOoO:Lo0OOO0Oo/o00oo00O;

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O$o00oOo00;->o00oOOo0:Landroid/app/Activity;

    invoke-virtual {p1, v0}, Lo0OOO0Oo/o00oo00O;->o00oOoO(Landroid/content/Context;)V

    return-void

    nop

    :array_0
    .array-data 1
        -0x7ct
        -0x12t
        0x6at
        0x72t
        0x13t
        -0x4dt
        -0x1ft
        0xft
        -0x49t
        -0x3dt
        0x49t
        0x60t
        0x18t
        -0x60t
        -0xet
        0x3ct
        -0x54t
        -0x12t
        0x42t
        0x7ct
    .end array-data

    :array_1
    .array-data 1
        -0x3bt
        -0x76t
        0x27t
        0x13t
        0x7dt
        -0x2et
        -0x7at
        0x6at
    .end array-data

    :array_2
    .array-data 1
        0x1ct
        0x2at
        -0x31t
        -0x17t
        -0x49t
        -0x15t
        -0x12t
        -0x8t
        0x16t
        0x20t
        -0x26t
        -0x1et
        -0x5et
        -0x1et
        -0x18t
        -0x1dt
        0x35t
        0x31t
        -0x1et
        -0x1ft
        -0x5et
        -0x17t
        -0xbt
        -0xft
        0x16t
        0x2at
        -0x33t
        -0x1et
        -0x61t
        -0x2t
        -0x1et
        -0x6t
        0x7t
        0x7et
        -0x52t
    .end array-data

    :array_3
    .array-data 1
        0x73t
        0x44t
        -0x72t
        -0x73t
        -0xft
        -0x76t
        -0x79t
        -0x6ct
    .end array-data
.end method

.method public onAdShowedFullScreenContent()V
    .locals 3

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x1b

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    return-void

    nop

    :array_0
    .array-data 1
        0x2et
        0x5et
        -0x69t
        -0x4ct
        -0x1dt
        -0x7bt
        -0x7et
        0x2ft
        0x1dt
        0x73t
        -0x4ct
        -0x5at
        -0x18t
        -0x6at
        -0x6ft
        0x1ct
        0x6t
        0x5et
        -0x41t
        -0x46t
    .end array-data

    :array_1
    .array-data 1
        0x6ft
        0x3at
        -0x26t
        -0x2bt
        -0x73t
        -0x1ct
        -0x1bt
        0x4at
    .end array-data

    :array_2
    .array-data 1
        -0x6bt
        -0x65t
        0x74t
        -0xdt
        0x32t
        -0x6ct
        0x21t
        -0x19t
        -0x61t
        -0x6ft
        0x73t
        -0x1et
        0xdt
        -0x70t
        0x1dt
        -0xdt
        -0x78t
        -0x70t
        0x50t
        -0x7t
        0x22t
        -0x6dt
        0x20t
        -0x1ct
        -0x61t
        -0x65t
        0x41t
    .end array-data

    :array_3
    .array-data 1
        -0x6t
        -0xbt
        0x35t
        -0x69t
        0x61t
        -0x4t
        0x4et
        -0x70t
    .end array-data
.end method
