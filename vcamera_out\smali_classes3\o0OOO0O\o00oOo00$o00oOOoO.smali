.class public Lo0OOo0O/o00oOo00$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokhttp3/o00oo00O;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo0O/o00oOo00;->o00oo0(Lokhttp3/o0O00O0o;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lokhttp3/o0O00OOO;

.field public final synthetic o00oOOoO:I

.field public final synthetic o00oOo00:Lo0OOo0O/o00oOo00;


# direct methods
.method public constructor <init>(Lo0OOo0O/o00oOo00;Lokhttp3/o0O00OOO;I)V
    .locals 0

    iput-object p1, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    iput-object p2, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOOo0:Lokhttp3/o0O00OOO;

    iput p3, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOOoO:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Lokhttp3/o00oOoO;Ljava/io/IOException;)V
    .locals 1

    iget-object p1, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    const/4 v0, 0x0

    invoke-virtual {p1, p2, v0}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    return-void
.end method

.method public o00oOOoO(Lokhttp3/o00oOoO;Lokhttp3/o0O00o00;)V
    .locals 4

    const-string v0, "OkHttp WebSocket "

    :try_start_0
    iget-object v1, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    invoke-virtual {v1, p2}, Lo0OOo0O/o00oOo00;->o00oOooo(Lokhttp3/o0O00o00;)V
    :try_end_0
    .catch Ljava/net/ProtocolException; {:try_start_0 .. :try_end_0} :catch_1

    sget-object v1, Lo0OOOoOo/o0;->o00oOOo0:Lo0OOOoOo/o0;

    invoke-virtual {v1, p1}, Lo0OOOoOo/o0;->o00oo0OO(Lokhttp3/o00oOoO;)Lo0OOOooO/o0OoO00O;

    move-result-object p1

    invoke-virtual {p1}, Lo0OOOooO/o0OoO00O;->o00oOoOo()V

    invoke-virtual {p1}, Lo0OOOooO/o0OoO00O;->o00oOooO()Lo0OOOooO/o0O000Oo;

    move-result-object v1

    invoke-virtual {v1, p1}, Lo0OOOooO/o0O000Oo;->o00oo0o0(Lo0OOOooO/o0OoO00O;)Lo0OOo0O/o00oOo00$o00oo0;

    move-result-object v1

    :try_start_1
    iget-object v2, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    iget-object v3, v2, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v3, v2, p2}, Lokhttp3/o0O0O0Oo;->o00oOo0o(Lokhttp3/o0O0oo0o;Lokhttp3/o0O00o00;)V

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v0, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOOo0:Lokhttp3/o0O00OOO;

    iget-object v0, v0, Lokhttp3/o0O00OOO;->o00oOOo0:Lokhttp3/o0O000o0;

    invoke-virtual {v0}, Lokhttp3/o0O000o0;->o00oooO()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    iget-object v0, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    iget v2, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOOoO:I

    int-to-long v2, v2

    invoke-virtual {v0, p2, v2, v3, v1}, Lo0OOo0O/o00oOo00;->o00oo0O0(Ljava/lang/String;JLo0OOo0O/o00oOo00$o00oo0;)V

    invoke-virtual {p1}, Lo0OOOooO/o0OoO00O;->o00oOooO()Lo0OOOooO/o0O000Oo;

    move-result-object p1

    iget-object p1, p1, Lo0OOOooO/o0O000Oo;->o00oOo0O:Ljava/net/Socket;

    const/4 p2, 0x0

    invoke-virtual {p1, p2}, Ljava/net/Socket;->setSoTimeout(I)V

    iget-object p1, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    invoke-virtual {p1}, Lo0OOo0O/o00oOo00;->o00oo0O()V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    iget-object p2, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    const/4 v0, 0x0

    invoke-virtual {p2, p1, v0}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    :goto_0
    return-void

    :catch_1
    move-exception p1

    iget-object v0, p0, Lo0OOo0O/o00oOo00$o00oOOoO;->o00oOo00:Lo0OOo0O/o00oOo00;

    invoke-virtual {v0, p1, p2}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    invoke-static {p2}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    return-void
.end method
