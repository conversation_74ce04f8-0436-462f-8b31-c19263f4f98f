.class public abstract Lo0OOOO0o/o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract o00oOOo0()V
.end method

.method public o00oOOoO()V
    .locals 0

    :try_start_0
    invoke-virtual {p0}, Lo0OOOO0o/o00oOOoO;->o00oOOo0()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method
