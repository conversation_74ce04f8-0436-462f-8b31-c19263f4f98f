.class public Lo0OOo00/o00oo0OO$o00oOOoO;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO;->o0O0o0O(IJ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:I

.field public final synthetic o00oo0Oo:J

.field public final synthetic o00oo0o0:Lo0OOo00/o00oo0OO;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;IJ)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iput p4, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0O:I

    iput-wide p5, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0Oo:J

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 4

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    iget v1, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0O:I

    iget-wide v2, p0, Lo0OOo00/o00oo0OO$o00oOOoO;->o00oo0Oo:J

    invoke-virtual {v0, v1, v2, v3}, Lo0OOo00/o0O0o;->o0OoOoOo(IJ)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method
