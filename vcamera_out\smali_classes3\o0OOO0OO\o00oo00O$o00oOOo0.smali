.class public Lo0OOO0Oo/o00oo00O$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0Oo/o00oo00O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOO0Oo/o00oo00O;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo00O;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    const-wide/32 v0, 0x75300

    :try_start_0
    iget-object v2, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    invoke-static {v2}, Lo0OOO0Oo/o00oo00O;->o00oOOo0(Lo0OOO0Oo/o00oo00O;)J

    move-result-wide v2

    const-wide/32 v4, 0x6ddd00

    add-long/2addr v2, v4

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    cmp-long v2, v2, v4

    if-gez v2, :cond_0

    iget-object v2, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    const/4 v3, 0x0

    iput-object v3, v2, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v2, v3}, Lo0OOO0Oo/o00oo00O;->o00oOoO(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    sget-object v2, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v3, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    iget-object v3, v3, Lo0OOO0Oo/o00oo00O;->o00oOo0O:Ljava/lang/Runnable;

    invoke-virtual {v2, v3}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    sget-object v2, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v3, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    goto :goto_0

    :catchall_0
    sget-object v2, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v3, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    invoke-static {v3}, Lo0OOO0Oo/o00oo00O;->o00oOo0O(Lo0OOO0Oo/o00oo00O;)Ljava/lang/Runnable;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    sget-object v2, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v3, p0, Lo0OOO0Oo/o00oo00O$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oo00O;

    :goto_0
    iget-object v3, v3, Lo0OOO0Oo/o00oo00O;->o00oOo0O:Ljava/lang/Runnable;

    invoke-virtual {v2, v3, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method
