.class public Lo0OOo00/o00oo0O$o00oOo00;
.super Lokio/o00oOOo0;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oOo00"
.end annotation


# instance fields
.field public final synthetic o00oOooo:Lo0OOo00/o00oo0O;


# direct methods
.method public constructor <init>(Lo0OOo00/o00oo0O;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOo00;->o00oOooo:Lo0OOo00/o00oo0O;

    invoke-direct {p0}, Lokio/o00oOOo0;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oo()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lokio/o00oOOo0;->o00oo0O0()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo0O(Ljava/io/IOException;)Ljava/io/IOException;

    move-result-object v0

    throw v0
.end method

.method public o00oo0O(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 2

    new-instance v0, Ljava/net/SocketTimeoutException;

    const-string v1, "timeout"

    invoke-direct {v0, v1}, Ljava/net/SocketTimeoutException;-><init>(Ljava/lang/String;)V

    if-eqz p1, :cond_0

    invoke-virtual {v0, p1}, Ljava/lang/Throwable;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    :cond_0
    return-object v0
.end method

.method public o0O0o()V
    .locals 2

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOo00;->o00oOooo:Lo0OOo00/o00oo0O;

    sget-object v1, Lo0OOo00/o00oOo00;->CANCEL:Lo0OOo00/o00oOo00;

    invoke-virtual {v0, v1}, Lo0OOo00/o00oo0O;->o00oOo0o(Lo0OOo00/o00oOo00;)V

    return-void
.end method
