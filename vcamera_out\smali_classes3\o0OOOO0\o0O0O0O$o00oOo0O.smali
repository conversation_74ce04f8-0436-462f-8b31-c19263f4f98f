.class public Lo0OOOo0/o0O0O0O$o00oOo0O;
.super Lde/robv/android/xposed/XC_MethodHook;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOOo0/o0O0O0O;->o00oOOoO(Ljava/lang/ClassLoader;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:[D


# direct methods
.method public constructor <init>([D)V
    .locals 0

    iput-object p1, p0, Lo0OOOo0/o0O0O0O$o00oOo0O;->o00oo0O0:[D

    invoke-direct {p0}, Lde/robv/android/xposed/XC_MethodHook;-><init>()V

    return-void
.end method


# virtual methods
.method public beforeHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    const/4 v0, 0x5

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/4 v0, 0x6

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object v0, p1, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    invoke-static {v0}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    iget-object v0, p1, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    new-instance v2, Lo0OOOo0/o0O0O0O$o00oOo0O$o00oOOo0;

    invoke-direct {v2, p0, v0}, Lo0OOOo0/o0O0O0O$o00oOo0O$o00oOOo0;-><init>(Lo0OOOo0/o0O0O0O$o00oOo0O;Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v3

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v0

    invoke-static {v3, v0, v2}, Ljava/lang/reflect/Proxy;->newProxyInstance(Ljava/lang/ClassLoader;[Ljava/lang/Class;Ljava/lang/reflect/InvocationHandler;)Ljava/lang/Object;

    move-result-object v0

    iget-object p1, p1, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->args:[Ljava/lang/Object;

    aput-object v0, p1, v1

    return-void

    :array_0
    .array-data 1
        0xct
        0x53t
        0x77t
        -0x27t
        0x68t
    .end array-data

    nop

    :array_1
    .array-data 1
        0x3dt
        0x62t
        0x46t
        -0x18t
        0x59t
        -0x73t
        -0x79t
        -0x29t
    .end array-data

    :array_2
    .array-data 1
        -0x14t
        0x4dt
        0x1ct
        0x3bt
        0x1et
        0x70t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x64t
        0x2ct
        0x6et
        0x5at
        0x73t
        0x4at
        -0x25t
        -0x74t
    .end array-data
.end method
