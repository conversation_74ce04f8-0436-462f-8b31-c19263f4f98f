.class public final Lo0OOOO00/o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0019\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008)\u0010*J\u0006\u0010\u0003\u001a\u00020\u0002J0\u0010\u000c\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00062\u0012\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u0006J(\u0010\r\u001a\u00020\u00022\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00062\u0012\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u0006J(\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u00102\u0006\u0010\u000e\u001a\u00020\u00042\u0012\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\t0\u0006J$\u0010\u0015\u001a\u00020\u00022\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u000e\u001a\u00020\u00042\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0006J$\u0010\u0017\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0006J$\u0010\u0019\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u000e\u001a\u00020\u00042\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006J$\u0010\u001a\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0006J\u001c\u0010\u001c\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\tJ\u0008\u0010\u001d\u001a\u00020\u0002H\u0002J \u0010 \u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u0007H\u0002R\u001a\u0010$\u001a\u00020\u00128\u0006X\u0086D\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010!\u001a\u0004\u0008\"\u0010#R\u001c\u0010&\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010%R\u0014\u0010(\u001a\u00020\u00048\u0002X\u0082D\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\'\u00a8\u0006+"
    }
    d2 = {
        "Lo0OOOO00/o00oOOo0;",
        "",
        "Lo0O0oooo/oO0O00o0;",
        "o00oOoO",
        "",
        "userID",
        "Landroidx/lifecycle/o0O0OOOo;",
        "",
        "loadingLiveData",
        "",
        "Lo0OOO0oo/o0O00000;",
        "appsLiveData",
        "o00oOOoO",
        "o00oOo00",
        "userId",
        "Lo0OOO0oo/o0O0o;",
        "",
        "o00oOo0O",
        "",
        "source",
        "resultLiveData",
        "o00oOo0o",
        "packageName",
        "o00oOoOo",
        "launchLiveData",
        "o00oOoO0",
        "o00oOOo0",
        "dataList",
        "o00oOoo0",
        "o00oOoOO",
        "pkg",
        "isAdd",
        "o00oOooo",
        "Ljava/lang/String;",
        "o00oOooO",
        "()Ljava/lang/String;",
        "TAG",
        "Ljava/util/List;",
        "mInstalledList",
        "I",
        "START_SUCCESS",
        "<init>",
        "()V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# instance fields
.field public final o00oOOo0:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o00oOOoO:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O0o;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o00oOo00:I


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lo0OOOO00/o00oOOo0;->o00oOOo0:Ljava/lang/String;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    return-void

    nop

    :array_0
    .array-data 1
        0x4t
        0x2t
        0x30t
        -0x9t
        -0x4ct
        -0x2et
        -0x28t
        -0x32t
        0x36t
        0x1bt
        0x34t
        -0x15t
        -0x6ct
        -0x32t
    .end array-data

    nop

    :array_1
    .array-data 1
        0x45t
        0x72t
        0x40t
        -0x7ct
        -0x1at
        -0x49t
        -0x58t
        -0x5ft
    .end array-data
.end method


# virtual methods
.method public final o00oOOo0(Ljava/lang/String;ILandroidx/lifecycle/o0O0OOOo;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0xb

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p3, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1, p2}, LoooOO0/o00oOo0O;->o00oOo00(Ljava/lang/String;I)I

    const/4 p1, 0x0

    new-array p1, p1, [Ljava/lang/String;

    const p2, 0x7f12003c

    invoke-static {p2, p1}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    nop

    :array_0
    .array-data 1
        0x54t
        0x79t
        0x44t
        0x32t
        0x11t
        0x61t
        -0x51t
        0x4et
        0x45t
        0x75t
        0x42t
    .end array-data

    :array_1
    .array-data 1
        0x24t
        0x18t
        0x27t
        0x59t
        0x70t
        0x6t
        -0x36t
        0x0t
    .end array-data

    :array_2
    .array-data 1
        0x6ct
        -0x39t
        -0x49t
        0x5ct
        0x9t
        0x41t
        -0x4ft
        -0x3ft
        0x68t
        -0x39t
        -0x80t
        0x48t
        0x11t
        0x54t
    .end array-data

    nop

    :array_3
    .array-data 1
        0x1et
        -0x5et
        -0x3ct
        0x29t
        0x65t
        0x35t
        -0x3t
        -0x58t
    .end array-data
.end method

.method public final o00oOOoO(ILandroidx/lifecycle/o0O0OOOo;Landroidx/lifecycle/o0O0OOOo;)V
    .locals 22
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/Boolean;",
            ">;",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O00000;",
            ">;>;)V"
        }
    .end annotation

    move-object/from16 v1, p0

    move-object/from16 v0, p2

    move-object/from16 v2, p3

    const/16 v3, 0xf

    new-array v3, v3, [B

    fill-array-data v3, :array_0

    const/16 v4, 0x8

    new-array v5, v4, [B

    fill-array-data v5, :array_1

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v3, 0xc

    new-array v3, v3, [B

    fill-array-data v3, :array_2

    new-array v5, v4, [B

    fill-array-data v5, :array_3

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v3, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v3}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    iget-object v3, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    monitor-enter v3

    :try_start_0
    iget-object v5, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    move-object v6, v5

    check-cast v6, Ljava/lang/Iterable;

    const/4 v5, 0x1

    new-array v7, v5, [B

    const/16 v8, 0x17

    const/4 v15, 0x0

    aput-byte v8, v7, v15

    new-array v4, v4, [B

    const/16 v8, 0x3b

    aput-byte v8, v4, v15

    const/16 v8, -0x1e

    aput-byte v8, v4, v5

    const/4 v8, 0x2

    const/16 v9, 0x7b

    aput-byte v9, v4, v8

    const/4 v8, 0x3

    const/16 v9, -0x2f

    aput-byte v9, v4, v8

    const/4 v8, 0x4

    const/16 v9, 0x6f

    aput-byte v9, v4, v8

    const/4 v8, 0x5

    const/16 v9, -0x9

    aput-byte v9, v4, v8

    const/4 v8, 0x6

    const/16 v9, -0x3b

    aput-byte v9, v4, v8

    const/4 v8, 0x7

    const/16 v9, 0x5c

    aput-byte v9, v4, v8

    invoke-static {v7, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/16 v13, 0x3e

    const/4 v14, 0x0

    invoke-static/range {v6 .. v14}, Lkotlin/collections/o0O0oo0o;->o0OOo0o0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)Ljava/lang/String;

    iget-object v4, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    check-cast v4, Ljava/lang/Iterable;

    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :cond_0
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_1

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    move-object v8, v7

    check-cast v8, Lo0OOO0oo/o0O0o;

    iget-boolean v8, v8, Lo0OOO0oo/o0O0o;->o00oOo0O:Z

    xor-int/2addr v8, v5

    if-eqz v8, :cond_0

    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    new-instance v4, Ljava/util/ArrayList;

    const/16 v7, 0xa

    invoke-static {v6, v7}, Lkotlin/collections/o0O00O;->o0O000(Ljava/lang/Iterable;I)I

    move-result v7

    invoke-direct {v4, v7}, Ljava/util/ArrayList;-><init>(I)V

    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_3

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lo0OOO0oo/o0O0o;

    iget-object v8, v7, Lo0OOO0oo/o0O0o;->o00oOo00:Ljava/lang/String;

    move/from16 v9, p1

    invoke-static {v8, v9, v15}, LoooOO0/o00oOo0O;->o00oOoOo(Ljava/lang/String;II)Landroid/content/pm/PackageInfo;

    move-result-object v8

    if-eqz v8, :cond_2

    move/from16 v21, v5

    goto :goto_2

    :cond_2
    move/from16 v21, v15

    :goto_2
    new-instance v8, Lo0OOO0oo/o0O00000;

    iget-object v10, v7, Lo0OOO0oo/o0O0o;->o00oOOo0:Ljava/lang/String;

    iget-object v11, v7, Lo0OOO0oo/o0O0o;->o00oOOoO:Landroid/graphics/drawable/Drawable;

    iget-object v12, v7, Lo0OOO0oo/o0O0o;->o00oOo00:Ljava/lang/String;

    iget-object v7, v7, Lo0OOO0oo/o0O0o;->o00oOooO:Ljava/lang/String;

    move-object/from16 v16, v8

    move-object/from16 v17, v10

    move-object/from16 v18, v11

    move-object/from16 v19, v12

    move-object/from16 v20, v7

    invoke-direct/range {v16 .. v21}, Lo0OOO0oo/o0O00000;-><init>(Ljava/lang/String;Landroid/graphics/drawable/Drawable;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-interface {v4, v8}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_3
    invoke-virtual {v2, v4}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-virtual {v0, v2}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    sget-object v0, Lo0O0oooo/oO0O00o0;->o00oOOo0:Lo0O0oooo/oO0O00o0;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v3

    return-void

    :catchall_0
    move-exception v0

    monitor-exit v3

    throw v0

    nop

    :array_0
    .array-data 1
        -0x46t
        -0x4et
        -0x5at
        -0x3ct
        -0x1ct
        -0x4bt
        0x2dt
        -0xet
        -0x41t
        -0x55t
        -0x5et
        -0x1ct
        -0x14t
        -0x51t
        0x2bt
    .end array-data

    :array_1
    .array-data 1
        -0x2at
        -0x23t
        -0x39t
        -0x60t
        -0x73t
        -0x25t
        0x4at
        -0x42t
    .end array-data

    :array_2
    .array-data 1
        -0x50t
        0x73t
        0x46t
        -0xft
        -0x79t
        0x79t
        0xat
        -0x33t
        -0x6bt
        0x62t
        0x42t
        -0x1dt
    .end array-data

    :array_3
    .array-data 1
        -0x2ft
        0x3t
        0x36t
        -0x7et
        -0x35t
        0x10t
        0x7ct
        -0x58t
    .end array-data
.end method

.method public final o00oOo00(Landroidx/lifecycle/o0O0OOOo;Landroidx/lifecycle/o0O0OOOo;)V
    .locals 11
    .param p1    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/Boolean;",
            ">;",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O00000;",
            ">;>;)V"
        }
    .end annotation

    const/16 v0, 0xf

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0xc

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {p1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    iget-object v0, p0, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    check-cast v1, Ljava/lang/Iterable;

    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Lo0OOO0oo/o0O0o;

    iget-boolean v4, v4, Lo0OOO0oo/o0O0o;->o00oOo0O:Z

    if-eqz v4, :cond_0

    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    new-instance v1, Ljava/util/ArrayList;

    const/16 v3, 0xa

    invoke-static {v2, v3}, Lkotlin/collections/o0O00O;->o0O000(Ljava/lang/Iterable;I)I

    move-result v3

    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lo0OOO0oo/o0O0o;

    new-instance v10, Lo0OOO0oo/o0O00000;

    iget-object v5, v3, Lo0OOO0oo/o0O0o;->o00oOOo0:Ljava/lang/String;

    iget-object v6, v3, Lo0OOO0oo/o0O0o;->o00oOOoO:Landroid/graphics/drawable/Drawable;

    iget-object v7, v3, Lo0OOO0oo/o0O0o;->o00oOo00:Ljava/lang/String;

    iget-object v8, v3, Lo0OOO0oo/o0O0o;->o00oOooO:Ljava/lang/String;

    const/4 v3, 0x0

    invoke-static {v7, v3}, Lmultispace/multiapp/clone/util/o00oo0O;->o00oOOo0(Ljava/lang/String;I)Z

    move-result v9

    move-object v4, v10

    invoke-direct/range {v4 .. v9}, Lo0OOO0oo/o0O00000;-><init>(Ljava/lang/String;Landroid/graphics/drawable/Drawable;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-interface {v1, v10}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_2
    invoke-virtual {p2, v1}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    sget-object p2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-virtual {p1, p2}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    sget-object p1, Lo0O0oooo/oO0O00o0;->o00oOOo0:Lo0O0oooo/oO0O00o0;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0

    throw p1

    nop

    :array_0
    .array-data 1
        0x38t
        -0x60t
        -0x2ft
        -0x8t
        0x73t
        -0x4et
        0x5ft
        0xbt
        0x3dt
        -0x47t
        -0x2bt
        -0x28t
        0x7bt
        -0x58t
        0x59t
    .end array-data

    :array_1
    .array-data 1
        0x54t
        -0x31t
        -0x50t
        -0x64t
        0x1at
        -0x24t
        0x38t
        0x47t
    .end array-data

    :array_2
    .array-data 1
        -0x1ct
        0x29t
        -0x1et
        0x1dt
        0x72t
        0x8t
        -0x7ft
        -0x73t
        -0x3ft
        0x38t
        -0x1at
        0xft
    .end array-data

    :array_3
    .array-data 1
        -0x7bt
        0x59t
        -0x6et
        0x6et
        0x3et
        0x61t
        -0x9t
        -0x18t
    .end array-data
.end method

.method public final o00oOo0O(ILandroidx/lifecycle/o0O0OOOo;)Ljava/util/List;
    .locals 17
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O0o;",
            ">;>;)",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O0o;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    move/from16 v0, p1

    move-object/from16 v1, p2

    const/16 v2, 0xc

    new-array v3, v2, [B

    fill-array-data v3, :array_0

    const/16 v4, 0x8

    new-array v5, v4, [B

    fill-array-data v5, :array_1

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {}, Lo0OOO0o0/o00oOo0O;->o00oOOo0()Landroid/content/SharedPreferences;

    move-result-object v3

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v6, 0x7

    new-array v6, v6, [B

    fill-array-data v6, :array_2

    new-array v7, v4, [B

    fill-array-data v7, :array_3

    invoke-static {v6, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const-string v6, ""

    invoke-interface {v3, v5, v6}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    const/4 v3, 0x1

    const/4 v5, 0x0

    if-eqz v7, :cond_0

    new-array v8, v3, [Ljava/lang/String;

    new-array v6, v3, [B

    const/4 v9, -0x1

    aput-byte v9, v6, v5

    new-array v9, v4, [B

    fill-array-data v9, :array_4

    invoke-static {v6, v9}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    aput-object v6, v8, v5

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x6

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lkotlin/text/o0O00o00;->o0Ooo0(Ljava/lang/CharSequence;[Ljava/lang/String;ZIILjava/lang/Object;)Ljava/util/List;

    move-result-object v6

    goto :goto_0

    :cond_0
    const/4 v6, 0x0

    :goto_0
    invoke-static {v5, v0}, LoooOO0/o00oOo0O;->o00oOoO(II)Ljava/util/List;

    move-result-object v7

    if-eqz v7, :cond_1

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v8

    if-lez v8, :cond_1

    const/16 v8, 0x11

    new-array v8, v8, [B

    fill-array-data v8, :array_5

    new-array v9, v4, [B

    fill-array-data v9, :array_6

    invoke-static {v8, v9}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v8

    invoke-interface {v7, v8}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    :cond_1
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    const/16 v9, 0xd

    new-array v9, v9, [B

    fill-array-data v9, :array_7

    new-array v10, v4, [B

    fill-array-data v10, :array_8

    invoke-static {v9, v10}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v9

    invoke-static {v7, v9}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v7, Ljava/lang/Iterable;

    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v7

    :cond_2
    :goto_1
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v9

    const/16 v10, 0xe

    if-eqz v9, :cond_6

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    const/16 v11, 0x80

    invoke-static {v9, v0, v11}, LoooOO0/o00oOo0O;->o00oOoOo(Ljava/lang/String;II)Landroid/content/pm/PackageInfo;

    move-result-object v9

    const/16 v11, 0x16

    new-array v12, v11, [B

    fill-array-data v12, :array_9

    new-array v13, v4, [B

    fill-array-data v13, :array_a

    invoke-static {v12, v13}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v12

    iget-object v13, v9, Landroid/content/pm/PackageInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v12, v13}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v12

    if-nez v12, :cond_2

    new-array v11, v11, [B

    fill-array-data v11, :array_b

    new-array v12, v4, [B

    fill-array-data v12, :array_c

    invoke-static {v11, v12}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v11

    iget-object v12, v9, Landroid/content/pm/PackageInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_3

    goto :goto_1

    :cond_3
    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOo00()Z

    move-result v11

    if-eqz v11, :cond_4

    new-array v10, v10, [B

    fill-array-data v10, :array_d

    new-array v11, v4, [B

    fill-array-data v11, :array_e

    invoke-static {v10, v11}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v10

    iget-object v11, v9, Landroid/content/pm/PackageInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v10, v11}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v10

    if-nez v10, :cond_2

    const/16 v10, 0x14

    new-array v10, v10, [B

    fill-array-data v10, :array_f

    new-array v11, v4, [B

    fill-array-data v11, :array_10

    invoke-static {v10, v11}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v10

    iget-object v11, v9, Landroid/content/pm/PackageInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v10, v11}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_4

    goto :goto_1

    :cond_4
    iget-object v10, v9, Landroid/content/pm/PackageInfo;->applicationInfo:Landroid/content/pm/ApplicationInfo;

    invoke-static {v10}, Lmultispace/multiapp/clone/util/o0O0O0Oo;->o00oOOoO(Landroid/content/pm/ApplicationInfo;)Z

    move-result v10

    if-eqz v10, :cond_5

    goto/16 :goto_1

    :cond_5
    iget-object v9, v9, Landroid/content/pm/PackageInfo;->applicationInfo:Landroid/content/pm/ApplicationInfo;

    const/16 v10, 0x1b

    new-array v10, v10, [B

    fill-array-data v10, :array_11

    new-array v11, v4, [B

    fill-array-data v11, :array_12

    invoke-static {v10, v11}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v10

    invoke-static {v9, v10}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_1

    :cond_6
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    move-object v7, v6

    check-cast v7, Ljava/util/Collection;

    if-eqz v7, :cond_8

    invoke-interface {v7}, Ljava/util/Collection;->isEmpty()Z

    move-result v7

    if-eqz v7, :cond_7

    goto :goto_2

    :cond_7
    move v3, v5

    :cond_8
    :goto_2
    if-eqz v3, :cond_9

    goto :goto_3

    :cond_9
    new-instance v3, Lo0OOOO00/o00oOOoO;

    invoke-direct {v3, v6}, Lo0OOOO00/o00oOOoO;-><init>(Ljava/util/List;)V

    invoke-static {v8, v3}, Lkotlin/collections/o0oO0Ooo;->o0O00OO(Ljava/util/List;Ljava/util/Comparator;)V

    :goto_3
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_4
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_a

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroid/content/pm/ApplicationInfo;

    new-instance v6, Lo0OOO0oo/o0O0o;

    sget-object v7, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v7}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v8

    invoke-virtual {v8}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v8

    invoke-virtual {v5, v8}, Landroid/content/pm/PackageItemInfo;->loadLabel(Landroid/content/pm/PackageManager;)Ljava/lang/CharSequence;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v7

    invoke-virtual {v5, v7}, Landroid/content/pm/PackageItemInfo;->loadIcon(Landroid/content/pm/PackageManager;)Landroid/graphics/drawable/Drawable;

    move-result-object v13

    const/16 v7, 0x34

    new-array v7, v7, [B

    fill-array-data v7, :array_13

    new-array v8, v4, [B

    fill-array-data v8, :array_14

    invoke-static {v7, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    invoke-static {v13, v7}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v14, v5, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    new-array v7, v10, [B

    fill-array-data v7, :array_15

    new-array v8, v4, [B

    fill-array-data v8, :array_16

    invoke-static {v7, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    invoke-static {v14, v7}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v15, v5, Landroid/content/pm/ApplicationInfo;->sourceDir:Ljava/lang/String;

    new-array v5, v2, [B

    fill-array-data v5, :array_17

    new-array v7, v4, [B

    fill-array-data v7, :array_18

    invoke-static {v5, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-static {v15, v5}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v16, 0x0

    move-object v11, v6

    invoke-direct/range {v11 .. v16}, Lo0OOO0oo/o0O0o;-><init>(Ljava/lang/String;Landroid/graphics/drawable/Drawable;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-interface {v0, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_4

    :cond_a
    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-object v0

    :array_0
    .array-data 1
        -0x1at
        0x6dt
        -0x43t
        0x5ft
        -0x4bt
        0x44t
        0x1ct
        -0x10t
        -0x3dt
        0x7ct
        -0x47t
        0x4dt
    .end array-data

    :array_1
    .array-data 1
        -0x79t
        0x1dt
        -0x33t
        0x2ct
        -0x7t
        0x2dt
        0x6at
        -0x6bt
    .end array-data

    :array_2
    .array-data 1
        0xbt
        -0x69t
        0x8t
        -0x1dt
        0x35t
        0x38t
        -0x65t
    .end array-data

    :array_3
    .array-data 1
        0x4at
        -0x19t
        0x78t
        -0x51t
        0x5ct
        0x4bt
        -0x11t
        -0x2dt
    .end array-data

    :array_4
    .array-data 1
        -0x2dt
        -0x9t
        0x2at
        0x6bt
        -0x7ct
        0x8t
        -0x61t
        -0x80t
    .end array-data

    :array_5
    .array-data 1
        0x51t
        0x10t
        0x41t
        -0x60t
        0x14t
        -0x4at
        0x3t
        0x5ft
        0x5dt
        0x10t
        0x42t
        -0x60t
        0xet
        -0x4at
        0x55t
        0x55t
        0x42t
    .end array-data

    nop

    :array_6
    .array-data 1
        0x32t
        0x7ft
        0x2ct
        -0x72t
        0x63t
        -0x29t
        0x7bt
        0x32t
    .end array-data

    :array_7
    .array-data 1
        -0x1et
        -0x7ft
        0x39t
        0x55t
        0x51t
        0x12t
        0x76t
        -0xat
        -0x11t
        -0x41t
        0x21t
        0x46t
        0x43t
    .end array-data

    nop

    :array_8
    .array-data 1
        -0x75t
        -0x11t
        0x4at
        0x21t
        0x30t
        0x7et
        0x1at
        -0x6dt
    .end array-data

    :array_9
    .array-data 1
        -0x9t
        -0x2at
        -0x7ct
        -0x7et
        -0x5t
        -0x3ft
        -0x2t
        0x6ft
        -0x8t
        -0x24t
        -0x39t
        -0x33t
        -0xet
        -0x36t
        -0x1dt
        0x67t
        -0x3t
        -0x23t
        -0x39t
        -0x35t
        -0xft
        -0x23t
    .end array-data

    nop

    :array_a
    .array-data 1
        -0x6ct
        -0x47t
        -0x17t
        -0x54t
        -0x64t
        -0x52t
        -0x6ft
        0x8t
    .end array-data

    :array_b
    .array-data 1
        -0x55t
        0x51t
        -0x2at
        0x72t
        0x53t
        0x6ft
        -0x40t
        0x45t
        -0x5ct
        0x5bt
        -0x6bt
        0x3dt
        0x5at
        0x64t
        -0x23t
        0x4dt
        -0x5ft
        0x5at
        -0x6bt
        0x3bt
        0x47t
        0x66t
    .end array-data

    nop

    :array_c
    .array-data 1
        -0x38t
        0x3et
        -0x45t
        0x5ct
        0x34t
        0x0t
        -0x51t
        0x22t
    .end array-data

    :array_d
    .array-data 1
        0x76t
        0x65t
        -0xct
        0x26t
        -0x29t
        0x32t
        -0x37t
        0x1t
        0x70t
        0x64t
        -0x13t
        0x26t
        -0x32t
        0x3at
    .end array-data

    nop

    :array_e
    .array-data 1
        0x15t
        0xat
        -0x67t
        0x8t
        -0x5dt
        0x57t
        -0x59t
        0x62t
    .end array-data

    :array_f
    .array-data 1
        0xdt
        0x24t
        0x1at
        0x32t
        -0x6bt
        0x57t
        -0x42t
        0x4bt
        0xbt
        0x25t
        0x3t
        0x32t
        -0x74t
        0x5dt
        -0x4et
        0x41t
        0x2t
        0x2et
        0x6t
        0x6dt
    .end array-data

    :array_10
    .array-data 1
        0x6et
        0x4bt
        0x77t
        0x1ct
        -0x1ft
        0x32t
        -0x30t
        0x28t
    .end array-data

    :array_11
    .array-data 1
        -0x57t
        -0x5bt
        0x5ct
        -0x7t
        0x6t
        0x63t
        -0x42t
        -0x60t
        -0x49t
        -0x5et
        0x50t
        -0x44t
        0x6t
        0x74t
        -0x55t
        -0x7bt
        -0x50t
        -0x59t
        0x5et
        -0x1at
        0xet
        0x6bt
        -0x4bt
        -0x60t
        -0x49t
        -0x5et
        0x50t
    .end array-data

    :array_12
    .array-data 1
        -0x27t
        -0x3ct
        0x3ft
        -0x6et
        0x67t
        0x4t
        -0x25t
        -0x17t
    .end array-data

    :array_13
    .array-data 1
        -0x20t
        -0x47t
        0x2at
        0x5t
        -0x68t
        -0x20t
        0x55t
        0xdt
        -0x16t
        -0x5et
        0x6at
        0x41t
        -0x66t
        -0xct
        0x5dt
        0x30t
        -0x20t
        -0x42t
        0x74t
        0x8t
        -0x6ct
        -0x1ct
        0x1ft
        0x29t
        0x6bt
        0x4dt
        -0x5et
        0x11t
        -0x7dt
        -0x57t
        0x18t
        0x6at
        -0x12t
        -0x58t
        0x70t
        0x39t
        -0x6at
        -0x1et
        0x5at
        0x25t
        -0x12t
        -0x58t
        0x49t
        0x8t
        -0x67t
        -0x20t
        0x56t
        0x21t
        -0x5t
        -0x1bt
        0x2dt
        0x40t
    .end array-data

    :array_14
    .array-data 1
        -0x77t
        -0x33t
        0x4t
        0x69t
        -0x9t
        -0x7ft
        0x31t
        0x44t
    .end array-data

    :array_15
    .array-data 1
        -0x39t
        -0x65t
        0x6et
        -0x63t
        -0x74t
        0x25t
        -0x31t
        0x36t
        -0x37t
        -0x76t
        0xet
        -0x74t
        -0x80t
        0x23t
    .end array-data

    nop

    :array_16
    .array-data 1
        -0x52t
        -0x11t
        0x40t
        -0x13t
        -0x13t
        0x46t
        -0x5ct
        0x57t
    .end array-data

    :array_17
    .array-data 1
        0x41t
        -0x5dt
        0x6bt
        -0x36t
        -0x62t
        -0x1bt
        -0x4dt
        0x1et
        0x4dt
        -0x6dt
        0x2ct
        -0x35t
    .end array-data

    :array_18
    .array-data 1
        0x28t
        -0x29t
        0x45t
        -0x47t
        -0xft
        -0x70t
        -0x3ft
        0x7dt
    .end array-data
.end method

.method public final o00oOo0o(Ljava/lang/String;ILandroidx/lifecycle/o0O0OOOo;)V
    .locals 25
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p1

    move/from16 v1, p2

    move-object/from16 v2, p3

    const/4 v3, 0x6

    new-array v4, v3, [B

    fill-array-data v4, :array_0

    const/16 v5, 0x8

    new-array v6, v5, [B

    fill-array-data v6, :array_1

    invoke-static {v4, v6}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    invoke-static {v0, v4}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v4, 0xe

    new-array v6, v4, [B

    fill-array-data v6, :array_2

    new-array v7, v5, [B

    fill-array-data v7, :array_3

    invoke-static {v6, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    invoke-static {v2, v6}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    invoke-static/range {p1 .. p1}, Landroid/webkit/URLUtil;->isValidUrl(Ljava/lang/String;)Z

    move-result v6

    const/16 v9, -0x37

    const/16 v13, 0x3d

    const/4 v14, 0x7

    const/4 v15, 0x5

    const/16 v16, 0x4

    const/16 v17, 0x3

    const/16 v18, 0x2

    const/4 v8, 0x0

    const/4 v12, 0x1

    if-eqz v6, :cond_1

    invoke-static/range {p1 .. p1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v6

    sget-object v20, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual/range {v20 .. v20}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v10

    invoke-virtual {v10, v6}, Landroid/content/ContentResolver;->openInputStream(Landroid/net/Uri;)Ljava/io/InputStream;

    move-result-object v6

    new-instance v10, Ljava/io/File;

    invoke-virtual/range {v20 .. v20}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v11

    new-array v4, v5, [B

    const/16 v22, -0x1

    aput-byte v22, v4, v8

    const/16 v22, 0x2c

    aput-byte v22, v4, v12

    const/16 v22, 0x6c

    aput-byte v22, v4, v18

    aput-byte v13, v4, v17

    const/16 v22, 0x57

    aput-byte v22, v4, v16

    const/16 v23, -0x68

    aput-byte v23, v4, v15

    const/16 v23, -0x24

    aput-byte v23, v4, v3

    const/16 v23, -0x2d

    aput-byte v23, v4, v14

    new-array v7, v5, [B

    const/16 v24, -0x75

    aput-byte v24, v7, v8

    const/16 v24, 0x49

    aput-byte v24, v7, v12

    aput-byte v12, v7, v18

    const/16 v24, 0x4d

    aput-byte v24, v7, v17

    const/16 v24, 0x79

    aput-byte v24, v7, v16

    const/16 v24, -0x7

    aput-byte v24, v7, v15

    const/16 v24, -0x54

    aput-byte v24, v7, v3

    const/16 v24, -0x48

    aput-byte v24, v7, v14

    invoke-static {v4, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v10, v11, v4}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {v10}, Ljava/io/File;->delete()Z

    invoke-static {v6, v10}, Lmultispace/multiapp/clone/util/o00oo;->o00oOoO0(Ljava/io/InputStream;Ljava/io/File;)Z

    invoke-virtual {v10}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lmultispace/multiapp/clone/util/o0O0O0Oo;->o00oOo00(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_0

    new-array v0, v12, [Ljava/lang/String;

    const/16 v1, 0x3e

    new-array v1, v1, [B

    const/16 v4, 0x4a

    aput-byte v4, v1, v8

    const/16 v4, 0x4a

    aput-byte v4, v1, v12

    const/16 v4, 0x72

    aput-byte v4, v1, v18

    const/16 v4, -0x2d

    aput-byte v4, v1, v17

    const/16 v4, -0x12

    aput-byte v4, v1, v16

    const/16 v4, 0x75

    aput-byte v4, v1, v15

    aput-byte v22, v1, v3

    const/16 v4, -0x2f

    aput-byte v4, v1, v14

    const/16 v4, 0x5b

    aput-byte v4, v1, v5

    const/16 v4, 0x9

    const/16 v6, 0x4e

    aput-byte v6, v1, v4

    const/16 v4, 0xa

    aput-byte v13, v1, v4

    const/16 v4, 0xb

    aput-byte v9, v1, v4

    const/4 v4, -0x8

    const/16 v6, 0xc

    aput-byte v4, v1, v6

    const/16 v6, 0xd

    const/16 v7, 0x31

    aput-byte v7, v1, v6

    const/16 v6, 0x16

    const/16 v10, 0xe

    aput-byte v6, v1, v10

    const/16 v10, 0xf

    const/16 v11, -0x70

    aput-byte v11, v1, v10

    const/16 v10, 0x10

    const/16 v11, 0x53

    aput-byte v11, v1, v10

    const/16 v10, 0x11

    const/16 v11, 0x55

    aput-byte v11, v1, v10

    const/16 v10, 0x12

    const/16 v11, 0x7e

    aput-byte v11, v1, v10

    const/16 v10, 0x13

    const/16 v11, -0x36

    aput-byte v11, v1, v10

    const/16 v10, 0x14

    const/16 v11, -0xf

    aput-byte v11, v1, v10

    const/16 v10, 0x15

    const/16 v11, 0x30

    aput-byte v11, v1, v10

    aput-byte v22, v1, v6

    const/16 v10, 0x17

    const/16 v11, -0x7e

    aput-byte v11, v1, v10

    const/16 v10, 0x18

    const/16 v11, 0x46

    aput-byte v11, v1, v10

    const/16 v10, 0x19

    const/16 v11, 0x1e

    aput-byte v11, v1, v10

    const/16 v10, 0x1a

    const/16 v11, 0x3b

    aput-byte v11, v1, v10

    const/16 v10, 0x1b

    const/16 v20, -0x2c

    aput-byte v20, v1, v10

    const/16 v10, 0x1c

    const/16 v20, -0x12

    aput-byte v20, v1, v10

    const/16 v10, 0x1d

    const/16 v20, 0x21

    aput-byte v20, v1, v10

    const/16 v10, 0x1e

    aput-byte v22, v1, v10

    const/16 v20, 0x1f

    const/16 v21, -0x63

    aput-byte v21, v1, v20

    const/16 v20, 0x20

    const/16 v21, 0x4f

    aput-byte v21, v1, v20

    const/16 v20, 0x21

    aput-byte v10, v1, v20

    const/16 v10, 0x22

    aput-byte v11, v1, v10

    const/16 v10, 0x23

    const/16 v20, -0x32

    aput-byte v20, v1, v10

    const/16 v10, 0x24

    const/16 v20, -0x43

    aput-byte v20, v1, v10

    const/16 v10, 0x25

    const/16 v20, 0x3c

    aput-byte v20, v1, v10

    const/16 v10, 0x26

    const/16 v20, 0x58

    aput-byte v20, v1, v10

    const/16 v10, 0x27

    const/16 v20, -0x2f

    aput-byte v20, v1, v10

    const/16 v10, 0x28

    const/16 v19, 0x7b

    aput-byte v19, v1, v10

    const/16 v10, 0x29

    const/16 v20, 0x4e

    aput-byte v20, v1, v10

    const/16 v10, 0x2a

    aput-byte v13, v1, v10

    const/16 v10, 0x2b

    aput-byte v9, v1, v10

    const/16 v9, 0x2c

    aput-byte v4, v1, v9

    const/16 v9, 0x2d

    aput-byte v7, v1, v9

    const/16 v9, 0x2e

    aput-byte v6, v1, v9

    const/16 v6, 0x2f

    const/16 v9, -0x44

    aput-byte v9, v1, v6

    const/16 v6, 0x30

    const/16 v9, 0x4c

    aput-byte v9, v1, v6

    const/16 v6, 0x5a

    aput-byte v6, v1, v7

    const/16 v6, 0x32

    const/16 v7, 0x27

    aput-byte v7, v1, v6

    const/16 v6, 0x33

    const/16 v7, -0x2a

    aput-byte v7, v1, v6

    const/16 v6, 0x34

    aput-byte v4, v1, v6

    const/16 v4, 0x35

    const/16 v6, 0x75

    aput-byte v6, v1, v4

    const/16 v4, 0x36

    const/16 v6, 0x7b

    aput-byte v6, v1, v4

    const/16 v4, 0x37

    const/16 v6, -0x70

    aput-byte v6, v1, v4

    const/16 v4, 0x38

    const/16 v6, 0x4d

    aput-byte v6, v1, v4

    const/16 v4, 0x39

    const/16 v6, 0x5f

    aput-byte v6, v1, v4

    const/16 v4, 0x3a

    const/16 v6, 0x35

    aput-byte v6, v1, v4

    const/16 v4, -0x21

    aput-byte v4, v1, v11

    const/16 v4, 0x3c

    const/16 v6, -0x11

    aput-byte v6, v1, v4

    const/16 v4, 0x7b

    aput-byte v4, v1, v13

    new-array v4, v5, [B

    const/16 v5, 0x23

    aput-byte v5, v4, v8

    const/16 v5, 0x3e

    aput-byte v5, v4, v12

    const/16 v5, 0x52

    aput-byte v5, v4, v18

    const/16 v5, -0x46

    aput-byte v5, v4, v17

    const/16 v5, -0x63

    aput-byte v5, v4, v16

    const/16 v5, 0x55

    aput-byte v5, v4, v15

    const/16 v5, 0x36

    aput-byte v5, v4, v3

    const/16 v3, -0xf

    aput-byte v3, v4, v14

    invoke-static {v1, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    aput-object v1, v0, v8

    const v1, 0x7f1200c1

    invoke-static {v1, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    :cond_0
    invoke-virtual {v10}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4, v1, v12}, LoooOO0/o00oOo0O;->o00oo0O(Ljava/lang/String;IZ)I

    move-result v4

    goto :goto_0

    :cond_1
    invoke-static {v0, v1, v12}, LoooOO0/o00oOo0O;->o00oo0Oo(Ljava/lang/String;IZ)I

    move-result v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :goto_0
    move-object/from16 v6, p0

    if-ne v4, v12, :cond_2

    :try_start_1
    invoke-virtual {v6, v1, v0, v12}, Lo0OOOO00/o00oOOo0;->o00oOooo(ILjava/lang/String;Z)V

    new-array v0, v8, [Ljava/lang/String;

    const v1, 0x7f1200c3

    invoke-static {v1, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    :goto_1
    invoke-virtual {v2, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    goto/16 :goto_3

    :cond_2
    new-array v0, v12, [Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v7, 0xc

    new-array v7, v7, [B

    const/16 v10, 0x21

    aput-byte v10, v7, v8

    const/16 v10, -0x34

    aput-byte v10, v7, v12

    const/16 v10, -0xb

    aput-byte v10, v7, v18

    const/16 v10, 0x46

    aput-byte v10, v7, v17

    const/16 v10, 0x7b

    aput-byte v10, v7, v16

    const/16 v10, 0x15

    aput-byte v10, v7, v15

    aput-byte v13, v7, v3

    const/16 v10, -0x40

    aput-byte v10, v7, v14

    const/16 v10, 0x28

    aput-byte v10, v7, v5

    const/16 v10, 0x9

    aput-byte v9, v7, v10

    const/16 v9, 0xa

    const/4 v10, -0x7

    aput-byte v10, v7, v9

    const/16 v9, 0xb

    const/16 v10, 0x10

    aput-byte v10, v7, v9

    new-array v5, v5, [B

    const/16 v9, 0x47

    aput-byte v9, v5, v8

    const/16 v9, -0x53

    aput-byte v9, v5, v12

    const/16 v9, -0x64

    aput-byte v9, v5, v18

    const/16 v9, 0x2a

    aput-byte v9, v5, v17

    const/16 v9, 0x1e

    aput-byte v9, v5, v16

    const/16 v9, 0x71

    aput-byte v9, v5, v15

    const/16 v9, 0x1d

    aput-byte v9, v5, v3

    const/16 v3, -0x5d

    aput-byte v3, v5, v14

    invoke-static {v7, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    aput-object v1, v0, v8

    const v1, 0x7f1200c1

    invoke-static {v1, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_2

    :catchall_1
    move-exception v0

    move-object/from16 v6, p0

    :goto_2
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_3
    return-void

    nop

    :array_0
    .array-data 1
        0x2bt
        0x8t
        0x5at
        0x4et
        0x4ct
        -0x5bt
    .end array-data

    nop

    :array_1
    .array-data 1
        0x58t
        0x67t
        0x2ft
        0x3ct
        0x2ft
        -0x40t
        0x3bt
        -0x1at
    .end array-data

    :array_2
    .array-data 1
        -0x24t
        -0x16t
        -0x5t
        0x7et
        -0x3at
        0x61t
        -0x2bt
        0x18t
        -0x28t
        -0x16t
        -0x34t
        0x6at
        -0x22t
        0x74t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x52t
        -0x71t
        -0x78t
        0xbt
        -0x56t
        0x15t
        -0x67t
        0x71t
    .end array-data
.end method

.method public final o00oOoO()V
    .locals 39

    move-object/from16 v1, p0

    iget-object v2, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    monitor-enter v2

    :try_start_0
    sget-object v0, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v0

    const/16 v3, 0x80

    invoke-virtual {v0, v3}, Landroid/content/pm/PackageManager;->getInstalledApplications(I)Ljava/util/List;

    move-result-object v0

    const/16 v3, 0x34

    new-array v3, v3, [B

    const/16 v4, -0x25

    const/4 v5, 0x0

    aput-byte v4, v3, v5

    const/16 v4, -0x57

    const/4 v6, 0x1

    aput-byte v4, v3, v6

    const/16 v4, -0x5b

    const/4 v7, 0x2

    aput-byte v4, v3, v7

    const/4 v4, -0x1

    const/4 v8, 0x3

    aput-byte v4, v3, v8

    const/16 v4, -0x62

    const/4 v9, 0x4

    aput-byte v4, v3, v9

    const/16 v4, -0x45

    const/4 v10, 0x5

    aput-byte v4, v3, v10

    const/16 v4, 0x16

    const/4 v11, 0x6

    aput-byte v4, v3, v11

    const/16 v12, 0x4d

    const/4 v13, 0x7

    aput-byte v12, v3, v13

    const/16 v12, -0xb

    const/16 v14, 0x8

    aput-byte v12, v3, v14

    const/16 v12, -0x49

    const/16 v15, 0x9

    aput-byte v12, v3, v15

    const/16 v12, -0x5f

    const/16 v16, 0xa

    aput-byte v12, v3, v16

    const/16 v12, -0x4c

    const/16 v17, 0xb

    aput-byte v12, v3, v17

    const/16 v12, -0x7f

    const/16 v18, 0xc

    aput-byte v12, v3, v18

    const/16 v12, -0x56

    const/16 v19, 0xd

    aput-byte v12, v3, v19

    const/16 v12, 0x4a

    const/16 v15, 0xe

    aput-byte v12, v3, v15

    const/16 v12, 0x27

    const/16 v21, 0xf

    aput-byte v12, v3, v21

    const/16 v12, -0x4c

    const/16 v22, 0x10

    aput-byte v12, v3, v22

    const/16 v12, -0x42

    const/16 v23, 0x11

    aput-byte v12, v3, v23

    const/16 v12, -0x50

    const/16 v24, 0x12

    aput-byte v12, v3, v24

    const/16 v12, -0x5b

    const/16 v13, 0x13

    aput-byte v12, v3, v13

    const/16 v12, -0x57

    const/16 v13, 0x14

    aput-byte v12, v3, v13

    const/16 v12, 0x15

    const/16 v27, -0x41

    aput-byte v27, v3, v12

    aput-byte v6, v3, v4

    const/16 v12, 0x17

    const/16 v27, 0x65

    aput-byte v27, v3, v12

    const/16 v12, 0x18

    const/16 v27, 0x78

    aput-byte v27, v3, v12

    const/16 v12, 0x19

    const/16 v27, 0x59

    aput-byte v27, v3, v12

    const/16 v12, 0x1a

    const/16 v27, 0x73

    aput-byte v27, v3, v12

    const/16 v12, 0x1b

    const/16 v27, -0x50

    aput-byte v27, v3, v12

    const/16 v12, 0x1c

    const/16 v27, -0x62

    aput-byte v27, v3, v12

    const/16 v12, 0x1d

    const/16 v27, -0x45

    aput-byte v27, v3, v12

    const/16 v12, 0x1e

    const/16 v27, 0x2f

    aput-byte v27, v3, v12

    const/16 v12, 0x1f

    const/16 v27, 0x6f

    aput-byte v27, v3, v12

    const/16 v12, 0x20

    const/16 v27, -0xc

    aput-byte v27, v3, v12

    const/16 v12, 0x21

    const/16 v27, -0x48

    aput-byte v27, v3, v12

    const/16 v12, 0x22

    const/16 v27, -0x4e

    aput-byte v27, v3, v12

    const/16 v12, 0x23

    const/16 v27, -0x4c

    aput-byte v27, v3, v12

    const/16 v12, 0x24

    const/16 v27, -0x75

    aput-byte v27, v3, v12

    const/16 v12, 0x25

    const/16 v27, -0x10

    aput-byte v27, v3, v12

    const/16 v12, 0x26

    const/16 v27, 0x25

    aput-byte v27, v3, v12

    const/16 v12, 0x27

    const/16 v27, 0x4b

    aput-byte v27, v3, v12

    const/16 v12, 0x28

    const/16 v27, -0x32

    aput-byte v27, v3, v12

    const/16 v12, 0x29

    const/16 v27, -0x7a

    aput-byte v27, v3, v12

    const/16 v12, 0x2a

    const/16 v27, -0x68

    aput-byte v27, v3, v12

    const/16 v12, 0x2b

    const/16 v27, -0x6c

    aput-byte v27, v3, v12

    const/16 v12, 0x2c

    const/16 v27, -0x53

    aput-byte v27, v3, v12

    const/16 v12, 0x2d

    const/16 v27, -0x61

    aput-byte v27, v3, v12

    const/16 v12, 0x2e

    const/16 v27, 0x3d

    aput-byte v27, v3, v12

    const/16 v12, 0x2f

    const/16 v27, 0x4a

    aput-byte v27, v3, v12

    const/16 v12, 0x30

    const/16 v27, -0x25

    aput-byte v27, v3, v12

    const/16 v12, 0x31

    const/16 v27, -0x73

    aput-byte v27, v3, v12

    const/16 v12, 0x32

    const/16 v27, -0x6c

    aput-byte v27, v3, v12

    const/16 v12, 0x33

    const/16 v27, -0x8

    aput-byte v27, v3, v12

    new-array v12, v14, [B

    const/16 v27, -0x66

    aput-byte v27, v12, v5

    const/16 v27, -0x27

    aput-byte v27, v12, v6

    const/16 v27, -0x2b

    aput-byte v27, v12, v7

    const/16 v27, -0x2f

    aput-byte v27, v12, v8

    const/16 v27, -0x7

    aput-byte v27, v12, v9

    const/16 v27, -0x22

    aput-byte v27, v12, v10

    const/16 v27, 0x62

    aput-byte v27, v12, v11

    const/16 v25, 0x7

    aput-byte v15, v12, v25

    invoke-static {v3, v12}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v12

    :goto_0
    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    move-object v13, v0

    check-cast v13, Landroid/content/pm/ApplicationInfo;

    new-instance v0, Ljava/io/File;

    iget-object v15, v13, Landroid/content/pm/ApplicationInfo;->sourceDir:Ljava/lang/String;

    invoke-direct {v0, v15}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    iget v15, v13, Landroid/content/pm/ApplicationInfo;->flags:I

    and-int/2addr v15, v6

    if-nez v15, :cond_6

    invoke-static {v0}, Lmultispace/multiapp/clone/util/o00oOOoO;->o00oOo0O(Ljava/io/File;)Z

    move-result v0

    if-eqz v0, :cond_6

    iget-object v0, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    sget-object v15, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v15}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v29

    invoke-virtual/range {v29 .. v29}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v0, v14}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    :goto_1
    const/16 v13, 0x14

    const/16 v14, 0x8

    goto/16 :goto_5

    :cond_0
    iget-object v0, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-static {v0, v5}, Lmultispace/multiapp/clone/util/o00oOo0O;->o00oOOo0(Ljava/lang/String;I)V

    new-array v0, v4, [B

    const/16 v14, -0x14

    aput-byte v14, v0, v5

    const/16 v14, 0x74

    aput-byte v14, v0, v6

    const/16 v14, 0x1b

    aput-byte v14, v0, v7

    aput-byte v17, v0, v8

    const/16 v14, -0x17

    aput-byte v14, v0, v9

    const/16 v14, -0x43

    aput-byte v14, v0, v10

    const/16 v14, -0x7c

    aput-byte v14, v0, v11

    const/16 v14, 0x43

    const/16 v25, 0x7

    aput-byte v14, v0, v25

    const/16 v14, -0x1d

    const/16 v29, 0x8

    aput-byte v14, v0, v29

    const/16 v14, 0x7e

    const/16 v20, 0x9

    aput-byte v14, v0, v20

    const/16 v14, 0x58

    aput-byte v14, v0, v16

    const/16 v14, 0x44

    aput-byte v14, v0, v17

    const/16 v14, -0x20

    aput-byte v14, v0, v18

    const/16 v14, -0x4a

    aput-byte v14, v0, v19

    const/16 v14, -0x67

    const/16 v28, 0xe

    aput-byte v14, v0, v28

    const/16 v14, 0x4b

    aput-byte v14, v0, v21

    const/16 v14, -0x1a

    aput-byte v14, v0, v22

    const/16 v14, 0x7f

    aput-byte v14, v0, v23

    const/16 v14, 0x58

    aput-byte v14, v0, v24

    const/16 v14, 0x42

    const/16 v26, 0x13

    aput-byte v14, v0, v26

    const/16 v14, -0x1d

    const/16 v27, 0x14

    aput-byte v14, v0, v27

    const/16 v14, 0x15

    const/16 v29, -0x5f

    aput-byte v29, v0, v14

    const/16 v14, 0x8

    new-array v4, v14, [B

    const/16 v14, -0x71

    aput-byte v14, v4, v5

    const/16 v14, 0x1b

    aput-byte v14, v4, v6

    const/16 v14, 0x76

    aput-byte v14, v4, v7

    const/16 v14, 0x25

    aput-byte v14, v4, v8

    const/16 v14, -0x72

    aput-byte v14, v4, v9

    const/16 v14, -0x2e

    aput-byte v14, v4, v10

    const/16 v14, -0x15

    aput-byte v14, v4, v11

    const/16 v14, 0x24

    const/16 v25, 0x7

    aput-byte v14, v4, v25

    invoke-static {v0, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    const/16 v4, 0x16

    new-array v0, v4, [B

    const/16 v4, -0x3a

    aput-byte v4, v0, v5

    const/16 v4, 0x38

    aput-byte v4, v0, v6

    const/16 v4, -0x3c

    aput-byte v4, v0, v7

    const/16 v4, -0x24

    aput-byte v4, v0, v8

    const/16 v4, 0x66

    aput-byte v4, v0, v9

    const/16 v4, 0x41

    aput-byte v4, v0, v10

    const/16 v4, -0x48

    aput-byte v4, v0, v11

    const/16 v4, -0x17

    const/4 v14, 0x7

    aput-byte v4, v0, v14

    const/16 v4, -0x37

    const/16 v14, 0x8

    aput-byte v4, v0, v14

    const/16 v4, 0x32

    const/16 v14, 0x9

    aput-byte v4, v0, v14

    const/16 v4, -0x79

    aput-byte v4, v0, v16

    const/16 v4, -0x6d

    aput-byte v4, v0, v17

    const/16 v4, 0x6f

    aput-byte v4, v0, v18

    const/16 v4, 0x4a

    aput-byte v4, v0, v19

    const/16 v4, -0x5b

    const/16 v14, 0xe

    aput-byte v4, v0, v14

    const/16 v4, -0x1f

    aput-byte v4, v0, v21

    const/16 v4, -0x34

    aput-byte v4, v0, v22

    const/16 v4, 0x33

    aput-byte v4, v0, v23

    const/16 v4, -0x79

    aput-byte v4, v0, v24

    const/16 v4, -0x6b

    const/16 v14, 0x13

    aput-byte v4, v0, v14

    const/16 v4, 0x72

    const/16 v14, 0x14

    aput-byte v4, v0, v14

    const/16 v4, 0x15

    const/16 v14, 0x48

    aput-byte v14, v0, v4

    const/16 v4, 0x8

    new-array v14, v4, [B

    const/16 v4, -0x5b

    aput-byte v4, v14, v5

    const/16 v4, 0x57

    aput-byte v4, v14, v6

    const/16 v4, -0x57

    aput-byte v4, v14, v7

    const/16 v4, -0xe

    aput-byte v4, v14, v8

    aput-byte v6, v14, v9

    const/16 v4, 0x2e

    aput-byte v4, v14, v10

    const/16 v4, -0x29

    aput-byte v4, v14, v11

    const/16 v4, -0x72

    const/16 v25, 0x7

    aput-byte v4, v14, v25

    invoke-static {v0, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_5

    const/16 v4, 0x13

    new-array v0, v4, [B

    const/16 v4, -0x7e

    aput-byte v4, v0, v5

    const/16 v4, 0x77

    aput-byte v4, v0, v6

    const/16 v4, 0x75

    aput-byte v4, v0, v7

    const/16 v4, 0x1d

    aput-byte v4, v0, v8

    const/16 v4, 0x65

    aput-byte v4, v0, v9

    const/16 v4, 0x36

    aput-byte v4, v0, v10

    const/16 v4, -0x66

    aput-byte v4, v0, v11

    const/16 v4, -0x60

    const/4 v14, 0x7

    aput-byte v4, v0, v14

    const/16 v4, -0x72

    const/16 v14, 0x8

    aput-byte v4, v0, v14

    const/16 v4, 0x71

    const/16 v14, 0x9

    aput-byte v4, v0, v14

    const/16 v4, 0x7c

    aput-byte v4, v0, v16

    const/16 v4, 0x1d

    aput-byte v4, v0, v17

    const/16 v4, 0x72

    aput-byte v4, v0, v18

    const/16 v4, 0x3d

    aput-byte v4, v0, v19

    const/16 v4, -0x70

    const/16 v14, 0xe

    aput-byte v4, v0, v14

    const/16 v4, -0x4a

    aput-byte v4, v0, v21

    const/16 v4, -0x78

    aput-byte v4, v0, v22

    const/16 v4, 0x76

    aput-byte v4, v0, v23

    const/16 v4, 0x7f

    aput-byte v4, v0, v24

    const/16 v4, 0x8

    new-array v14, v4, [B

    const/16 v4, -0x1f

    aput-byte v4, v14, v5

    const/16 v4, 0x18

    aput-byte v4, v14, v6

    const/16 v4, 0x18

    aput-byte v4, v14, v7

    const/16 v4, 0x33

    aput-byte v4, v14, v8

    aput-byte v9, v14, v9

    const/16 v4, 0x58

    aput-byte v4, v14, v10

    const/4 v4, -0x2

    aput-byte v4, v14, v11

    const/16 v4, -0x2e

    const/16 v25, 0x7

    aput-byte v4, v14, v25

    invoke-static {v0, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto/16 :goto_3

    :cond_1
    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOo00()Z

    move-result v0

    if-eqz v0, :cond_2

    const/16 v4, 0xe

    new-array v0, v4, [B

    const/16 v4, 0x46

    aput-byte v4, v0, v5

    const/16 v4, -0x33

    aput-byte v4, v0, v6

    const/16 v4, -0x73

    aput-byte v4, v0, v7

    const/16 v4, 0x63

    aput-byte v4, v0, v8

    const/16 v4, -0x67

    aput-byte v4, v0, v9

    const/16 v4, 0x6c

    aput-byte v4, v0, v10

    const/16 v4, -0x34

    aput-byte v4, v0, v11

    const/16 v4, -0x66

    const/4 v14, 0x7

    aput-byte v4, v0, v14

    const/16 v4, 0x40

    const/16 v14, 0x8

    aput-byte v4, v0, v14

    const/16 v4, -0x34

    const/16 v14, 0x9

    aput-byte v4, v0, v14

    const/16 v4, -0x6c

    aput-byte v4, v0, v16

    const/16 v4, 0x63

    aput-byte v4, v0, v17

    const/16 v4, -0x80

    aput-byte v4, v0, v18

    const/16 v4, 0x64

    aput-byte v4, v0, v19

    const/16 v4, 0x8

    new-array v14, v4, [B

    const/16 v4, 0x25

    aput-byte v4, v14, v5

    const/16 v4, -0x5e

    aput-byte v4, v14, v6

    const/16 v4, -0x20

    aput-byte v4, v14, v7

    const/16 v4, 0x4d

    aput-byte v4, v14, v8

    const/16 v4, -0x13

    aput-byte v4, v14, v9

    const/16 v4, 0x9

    aput-byte v4, v14, v10

    const/16 v4, -0x5e

    aput-byte v4, v14, v11

    const/4 v4, -0x7

    const/16 v25, 0x7

    aput-byte v4, v14, v25

    invoke-static {v0, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    const/16 v4, 0x14

    new-array v0, v4, [B

    const/16 v4, -0x74

    aput-byte v4, v0, v5

    const/16 v4, 0x6e

    aput-byte v4, v0, v6

    const/16 v4, -0x6c

    aput-byte v4, v0, v7

    const/16 v4, 0x55

    aput-byte v4, v0, v8

    const/16 v4, -0x25

    aput-byte v4, v0, v9

    const/16 v4, 0x66

    aput-byte v4, v0, v10

    const/16 v4, 0x20

    aput-byte v4, v0, v11

    const/16 v4, 0x1d

    const/4 v14, 0x7

    aput-byte v4, v0, v14

    const/16 v4, -0x76

    const/16 v14, 0x8

    aput-byte v4, v0, v14

    const/16 v4, 0x6f

    const/16 v14, 0x9

    aput-byte v4, v0, v14

    const/16 v4, -0x73

    aput-byte v4, v0, v16

    const/16 v4, 0x55

    aput-byte v4, v0, v17

    const/16 v4, -0x3e

    aput-byte v4, v0, v18

    const/16 v4, 0x6c

    aput-byte v4, v0, v19

    const/16 v4, 0x2c

    const/16 v14, 0xe

    aput-byte v4, v0, v14

    const/16 v4, 0x17

    aput-byte v4, v0, v21

    const/16 v4, -0x7d

    aput-byte v4, v0, v22

    const/16 v4, 0x64

    aput-byte v4, v0, v23

    const/16 v4, -0x78

    aput-byte v4, v0, v24

    const/16 v4, 0x13

    aput-byte v16, v0, v4

    const/16 v4, 0x8

    new-array v14, v4, [B

    const/16 v4, -0x11

    aput-byte v4, v14, v5

    aput-byte v6, v14, v6

    const/4 v4, -0x7

    aput-byte v4, v14, v7

    const/16 v4, 0x7b

    aput-byte v4, v14, v8

    const/16 v4, -0x51

    aput-byte v4, v14, v9

    aput-byte v8, v14, v10

    const/16 v4, 0x4e

    aput-byte v4, v14, v11

    const/16 v4, 0x7e

    const/16 v25, 0x7

    aput-byte v4, v14, v25

    invoke-static {v0, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_2

    :cond_2
    invoke-static {v13}, Lmultispace/multiapp/clone/util/o0O0O0Oo;->o00oOOoO(Landroid/content/pm/ApplicationInfo;)Z

    move-result v36
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    if-nez v36, :cond_4

    :try_start_1
    invoke-virtual {v15}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v0

    iget-object v4, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    invoke-virtual {v0, v4}, Landroid/content/pm/PackageManager;->getLaunchIntentForPackage(Ljava/lang/String;)Landroid/content/Intent;

    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez v0, :cond_4

    :cond_3
    :goto_2
    const/16 v4, 0x16

    goto/16 :goto_1

    :catchall_0
    move-exception v0

    :try_start_2
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_4
    new-instance v0, Lo0OOO0oo/o0O0o;

    sget-object v4, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v4}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v14

    invoke-virtual {v14}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v14

    invoke-virtual {v13, v14}, Landroid/content/pm/PackageItemInfo;->loadLabel(Landroid/content/pm/PackageManager;)Ljava/lang/CharSequence;

    move-result-object v14

    invoke-virtual {v14}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v32

    invoke-virtual {v4}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v4

    invoke-virtual {v4}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v4

    invoke-virtual {v13, v4}, Landroid/content/pm/PackageItemInfo;->loadIcon(Landroid/content/pm/PackageManager;)Landroid/graphics/drawable/Drawable;

    move-result-object v4

    const/16 v14, 0x34

    new-array v14, v14, [B

    const/16 v15, 0x60

    aput-byte v15, v14, v5

    const/16 v15, 0x2a

    aput-byte v15, v14, v6

    const/16 v15, 0x62

    aput-byte v15, v14, v7

    const/16 v15, 0x6d

    aput-byte v15, v14, v8

    const/16 v15, -0x5e

    aput-byte v15, v14, v9

    const/16 v15, -0x74

    aput-byte v15, v14, v10

    const/16 v15, -0x58

    aput-byte v15, v14, v11

    const/16 v15, -0x3a

    const/16 v25, 0x7

    aput-byte v15, v14, v25

    const/16 v15, 0x6d

    const/16 v30, 0x8

    aput-byte v15, v14, v30

    const/16 v15, 0x9

    aput-byte v10, v14, v15

    const/16 v15, 0x61

    aput-byte v15, v14, v16

    const/16 v15, 0x69

    aput-byte v15, v14, v17

    const/16 v15, -0x51

    aput-byte v15, v14, v18

    const/16 v15, -0x77

    aput-byte v15, v14, v19

    const/16 v15, -0x59

    const/16 v28, 0xe

    aput-byte v15, v14, v28

    const/16 v15, -0x3e

    aput-byte v15, v14, v21

    const/16 v15, 0x7d

    aput-byte v15, v14, v22

    const/16 v15, 0x2d

    aput-byte v15, v14, v23

    const/16 v15, 0x7e

    aput-byte v15, v14, v24

    const/16 v15, 0x77

    const/16 v26, 0x13

    aput-byte v15, v14, v26

    const/16 v15, -0x13

    const/16 v27, 0x14

    aput-byte v15, v14, v27

    const/16 v15, 0x15

    const/16 v31, -0x74

    aput-byte v31, v14, v15

    const/16 v15, -0x55

    const/16 v29, 0x16

    aput-byte v15, v14, v29

    const/16 v15, 0x17

    const/16 v31, -0x3e

    aput-byte v31, v14, v15

    const/16 v15, 0x18

    const/16 v31, -0x15

    aput-byte v31, v14, v15

    const/16 v15, 0x19

    const/16 v31, -0x3c

    aput-byte v31, v14, v15

    const/16 v15, 0x1a

    const/16 v31, -0x49

    aput-byte v31, v14, v15

    const/16 v15, 0x1b

    const/16 v31, 0x61

    aput-byte v31, v14, v15

    const/16 v15, 0x1c

    const/16 v31, -0x49

    aput-byte v31, v14, v15

    const/16 v15, 0x1d

    const/16 v31, -0x38

    aput-byte v31, v14, v15

    const/16 v15, 0x1e

    const/16 v31, -0x13

    aput-byte v31, v14, v15

    const/16 v15, 0x1f

    const/16 v31, -0x73

    aput-byte v31, v14, v15

    const/16 v15, 0x20

    const/16 v31, 0x6e

    aput-byte v31, v14, v15

    const/16 v15, 0x21

    const/16 v31, 0x21

    aput-byte v31, v14, v15

    const/16 v15, 0x22

    const/16 v31, 0x65

    aput-byte v31, v14, v15

    const/16 v15, 0x23

    const/16 v31, 0x49

    aput-byte v31, v14, v15

    const/16 v15, 0x24

    const/16 v31, -0x5e

    aput-byte v31, v14, v15

    const/16 v15, 0x25

    const/16 v31, -0x7d

    aput-byte v31, v14, v15

    const/16 v15, 0x26

    const/16 v31, -0x51

    aput-byte v31, v14, v15

    const/16 v15, 0x27

    const/16 v31, -0x3e

    aput-byte v31, v14, v15

    const/16 v15, 0x28

    const/16 v31, 0x6e

    aput-byte v31, v14, v15

    const/16 v15, 0x29

    const/16 v31, 0x21

    aput-byte v31, v14, v15

    const/16 v15, 0x2a

    const/16 v31, 0x5c

    aput-byte v31, v14, v15

    const/16 v15, 0x2b

    const/16 v31, 0x78

    aput-byte v31, v14, v15

    const/16 v15, 0x2c

    const/16 v31, -0x53

    aput-byte v31, v14, v15

    const/16 v15, 0x2d

    const/16 v31, -0x7f

    aput-byte v31, v14, v15

    const/16 v15, 0x2e

    const/16 v31, -0x5d

    aput-byte v31, v14, v15

    const/16 v15, 0x2f

    const/16 v31, -0x3a

    aput-byte v31, v14, v15

    const/16 v15, 0x30

    const/16 v31, 0x7b

    aput-byte v31, v14, v15

    const/16 v15, 0x31

    const/16 v31, 0x6c

    aput-byte v31, v14, v15

    const/16 v15, 0x32

    const/16 v31, 0x38

    aput-byte v31, v14, v15

    const/16 v15, 0x33

    const/16 v31, 0x30

    aput-byte v31, v14, v15

    const/16 v15, 0x8

    new-array v11, v15, [B

    const/16 v15, 0x9

    aput-byte v15, v11, v5

    const/16 v15, 0x44

    aput-byte v15, v11, v6

    aput-byte v23, v11, v7

    const/16 v15, 0x19

    aput-byte v15, v11, v8

    const/16 v15, -0x3d

    aput-byte v15, v11, v9

    const/16 v15, -0x20

    aput-byte v15, v11, v10

    const/16 v15, -0x3c

    const/16 v31, 0x6

    aput-byte v15, v11, v31

    const/16 v15, -0x5d

    const/16 v25, 0x7

    aput-byte v15, v11, v25

    invoke-static {v14, v11}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v11

    invoke-static {v4, v11}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v11, v13, Landroid/content/pm/ApplicationInfo;->packageName:Ljava/lang/String;

    const/16 v14, 0x20

    new-array v14, v14, [B

    aput-byte v17, v14, v5

    const/16 v15, 0x64

    aput-byte v15, v14, v6

    aput-byte v17, v14, v7

    const/16 v15, -0x35

    aput-byte v15, v14, v8

    const/4 v15, -0x3

    aput-byte v15, v14, v9

    const/16 v15, 0x4f

    aput-byte v15, v14, v10

    const/16 v15, 0x27

    const/16 v31, 0x6

    aput-byte v15, v14, v31

    const/16 v15, 0x7b

    const/16 v25, 0x7

    aput-byte v15, v14, v25

    const/16 v15, 0x8

    aput-byte v31, v14, v15

    const/16 v30, 0x4b

    const/16 v20, 0x9

    aput-byte v30, v14, v20

    aput-byte v15, v14, v16

    const/16 v15, -0x31

    aput-byte v15, v14, v17

    const/16 v15, -0x10

    aput-byte v15, v14, v18

    const/16 v15, 0x4a

    aput-byte v15, v14, v19

    const/16 v15, 0x28

    const/16 v28, 0xe

    aput-byte v15, v14, v28

    const/16 v15, 0x7f

    aput-byte v15, v14, v21

    const/16 v15, 0x16

    aput-byte v15, v14, v22

    const/16 v15, 0x63

    aput-byte v15, v14, v23

    const/16 v15, 0x17

    aput-byte v15, v14, v24

    const/16 v15, -0x2f

    const/16 v26, 0x13

    aput-byte v15, v14, v26

    const/16 v15, -0x4e

    const/16 v27, 0x14

    aput-byte v15, v14, v27

    const/16 v15, 0x15

    const/16 v31, 0x53

    aput-byte v31, v14, v15

    const/16 v15, 0x2a

    const/16 v29, 0x16

    aput-byte v15, v14, v29

    const/16 v15, 0x17

    const/16 v31, 0x7d

    aput-byte v31, v14, v15

    const/16 v15, 0x18

    const/16 v20, 0x9

    aput-byte v20, v14, v15

    const/16 v15, 0x19

    const/16 v31, 0x6b

    aput-byte v31, v14, v15

    const/16 v15, 0x1a

    const/16 v31, 0x1f

    aput-byte v31, v14, v15

    const/16 v15, 0x1b

    const/16 v31, -0x26

    aput-byte v31, v14, v15

    const/16 v15, 0x1c

    const/16 v31, -0x2e

    aput-byte v31, v14, v15

    const/16 v15, 0x1d

    const/16 v31, 0x42

    aput-byte v31, v14, v15

    const/16 v15, 0x1e

    const/16 v31, 0x26

    aput-byte v31, v14, v15

    const/16 v15, 0x1f

    const/16 v31, 0x7b

    aput-byte v31, v14, v15

    const/16 v15, 0x8

    new-array v10, v15, [B

    const/16 v15, 0x62

    aput-byte v15, v10, v5

    aput-byte v16, v10, v6

    const/16 v15, 0x78

    aput-byte v15, v10, v7

    const/16 v15, -0x41

    aput-byte v15, v10, v8

    const/16 v15, -0x64

    aput-byte v15, v10, v9

    const/16 v15, 0x23

    const/16 v31, 0x5

    aput-byte v15, v10, v31

    const/16 v15, 0x4b

    const/16 v31, 0x6

    aput-byte v15, v10, v31

    const/16 v15, 0x1e

    const/16 v25, 0x7

    aput-byte v15, v10, v25

    invoke-static {v14, v10}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v10

    invoke-static {v11, v10}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v10, v13, Landroid/content/pm/ApplicationInfo;->sourceDir:Ljava/lang/String;

    const/16 v13, 0x1e

    new-array v13, v13, [B

    const/16 v14, 0x36

    aput-byte v14, v13, v5

    const/16 v14, -0x69

    aput-byte v14, v13, v6

    const/16 v14, 0x34

    aput-byte v14, v13, v7

    const/16 v14, -0xb

    aput-byte v14, v13, v8

    const/16 v14, 0x7f

    aput-byte v14, v13, v9

    const/16 v14, 0x4b

    const/4 v15, 0x5

    aput-byte v14, v13, v15

    const/16 v14, -0x80

    const/4 v15, 0x6

    aput-byte v14, v13, v15

    const/16 v14, 0x77

    const/4 v15, 0x7

    aput-byte v14, v13, v15

    const/16 v14, 0x3b

    const/16 v15, 0x8

    aput-byte v14, v13, v15

    const/16 v14, -0x48

    const/16 v15, 0x9

    aput-byte v14, v13, v15

    const/16 v14, 0x37

    aput-byte v14, v13, v16

    const/16 v14, -0xf

    aput-byte v14, v13, v17

    const/16 v14, 0x72

    aput-byte v14, v13, v18

    const/16 v14, 0x4e

    aput-byte v14, v13, v19

    const/16 v14, -0x71

    const/16 v20, 0xe

    aput-byte v14, v13, v20

    const/16 v14, 0x73

    aput-byte v14, v13, v21

    const/16 v14, 0x2b

    aput-byte v14, v13, v22

    const/16 v14, -0x70

    aput-byte v14, v13, v23

    const/16 v14, 0x28

    aput-byte v14, v13, v24

    const/16 v14, -0x11

    const/16 v26, 0x13

    aput-byte v14, v13, v26

    const/16 v14, 0x30

    const/16 v27, 0x14

    aput-byte v14, v13, v27

    const/16 v14, 0x15

    const/16 v28, 0x54

    aput-byte v28, v13, v14

    const/16 v14, -0x7d

    const/16 v28, 0x16

    aput-byte v14, v13, v28

    const/16 v14, 0x17

    const/16 v29, 0x67

    aput-byte v29, v13, v14

    const/16 v14, 0x18

    const/16 v29, 0x2d

    aput-byte v29, v13, v14

    const/16 v14, 0x19

    const/16 v29, -0x66

    aput-byte v29, v13, v14

    const/16 v14, 0x1a

    const/16 v29, 0x22

    aput-byte v29, v13, v14

    const/16 v14, 0x1b

    const/16 v29, -0x3b

    aput-byte v29, v13, v14

    const/16 v14, 0x1c

    const/16 v29, 0x77

    aput-byte v29, v13, v14

    const/16 v14, 0x1d

    const/16 v29, 0x55

    aput-byte v29, v13, v14

    const/16 v14, 0x8

    new-array v15, v14, [B

    const/16 v30, 0x5f

    aput-byte v30, v15, v5

    const/16 v30, -0x7

    aput-byte v30, v15, v6

    const/16 v30, 0x47

    aput-byte v30, v15, v7

    const/16 v30, -0x7f

    aput-byte v30, v15, v8

    const/16 v30, 0x1e

    aput-byte v30, v15, v9

    const/16 v30, 0x27

    const/16 v38, 0x5

    aput-byte v30, v15, v38

    const/16 v30, -0x14

    const/16 v37, 0x6

    aput-byte v30, v15, v37

    const/16 v25, 0x7

    aput-byte v24, v15, v25

    invoke-static {v13, v15}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v13

    invoke-static {v10, v13}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    move-object/from16 v31, v0

    move-object/from16 v33, v4

    move-object/from16 v34, v11

    move-object/from16 v35, v10

    invoke-direct/range {v31 .. v36}, Lo0OOO0oo/o0O0o;-><init>(Ljava/lang/String;Landroid/graphics/drawable/Drawable;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-interface {v3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_4

    :cond_5
    :goto_3
    move/from16 v38, v10

    move/from16 v37, v11

    const/16 v14, 0x8

    const/16 v20, 0xe

    const/16 v25, 0x7

    const/16 v26, 0x13

    const/16 v27, 0x14

    const/16 v28, 0x16

    :goto_4
    move/from16 v15, v20

    move/from16 v13, v27

    move/from16 v4, v28

    move/from16 v11, v37

    move/from16 v10, v38

    goto/16 :goto_0

    :cond_6
    const/16 v25, 0x7

    const/16 v26, 0x13

    const/16 v27, 0x14

    move/from16 v13, v27

    :goto_5
    const/16 v15, 0xe

    goto/16 :goto_0

    :cond_7
    iget-object v0, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    iget-object v0, v1, Lo0OOOO00/o00oOOo0;->o00oOOoO:Ljava/util/List;

    invoke-interface {v0, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    monitor-exit v2

    return-void

    :catchall_1
    move-exception v0

    monitor-exit v2

    throw v0
.end method

.method public final o00oOoO0(Ljava/lang/String;ILandroidx/lifecycle/o0O0OOOo;)V
    .locals 28
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    move/from16 v0, p2

    move-object/from16 v1, p3

    const/16 v2, 0xb

    new-array v3, v2, [B

    fill-array-data v3, :array_0

    const/16 v4, 0x8

    new-array v5, v4, [B

    fill-array-data v5, :array_1

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    move-object/from16 v5, p1

    invoke-static {v5, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v3, 0xe

    new-array v6, v3, [B

    fill-array-data v6, :array_2

    new-array v7, v4, [B

    fill-array-data v7, :array_3

    invoke-static {v6, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    invoke-static {v1, v6}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v6, 0x15

    const/16 v7, 0xa

    const/4 v8, 0x5

    :try_start_0
    new-array v6, v6, [B

    const/16 v9, -0x7d

    const/4 v10, 0x0

    aput-byte v9, v6, v10

    const/4 v9, 0x6

    const/4 v11, 0x1

    aput-byte v9, v6, v11

    const/16 v12, 0x7f

    const/4 v13, 0x2

    aput-byte v12, v6, v13

    const/16 v12, 0x16

    const/4 v14, 0x3

    aput-byte v12, v6, v14

    const/16 v15, -0x62

    const/16 v16, 0x4

    aput-byte v15, v6, v16

    const/16 v15, -0x68

    aput-byte v15, v6, v8

    const/16 v15, 0x65

    aput-byte v15, v6, v9

    const/16 v15, 0x4e

    const/16 v17, 0x7

    aput-byte v15, v6, v17

    const/16 v15, -0x3c

    aput-byte v15, v6, v4

    const/16 v15, 0x50

    const/16 v18, 0x9

    aput-byte v15, v6, v18

    const/16 v15, 0x3e

    aput-byte v15, v6, v7

    aput-byte v8, v6, v2

    const/16 v15, -0x3f

    const/16 v19, 0xc

    aput-byte v15, v6, v19

    const/16 v15, -0x27

    const/16 v12, 0xd

    aput-byte v15, v6, v12

    const/16 v15, 0x75

    aput-byte v15, v6, v3

    const/16 v15, 0x4e

    const/16 v21, 0xf

    aput-byte v15, v6, v21

    const/16 v15, -0x2d

    const/16 v22, 0x10

    aput-byte v15, v6, v22

    const/16 v15, 0x5d

    const/16 v23, 0x11

    aput-byte v15, v6, v23

    const/16 v15, 0x12

    aput-byte v12, v6, v15

    const/16 v24, 0x55

    const/16 v15, 0x13

    aput-byte v24, v6, v15

    const/16 v24, 0x14

    const/16 v26, -0x3a

    aput-byte v26, v6, v24

    new-array v15, v4, [B

    const/16 v26, -0x50

    aput-byte v26, v15, v10

    const/16 v26, 0x35

    aput-byte v26, v15, v11

    const/16 v26, 0x4c

    aput-byte v26, v15, v13

    const/16 v26, 0x25

    aput-byte v26, v15, v14

    const/16 v26, -0x53

    aput-byte v26, v15, v16

    const/16 v26, -0x48

    aput-byte v26, v15, v8

    aput-byte v10, v15, v9

    const/16 v26, 0x20

    aput-byte v26, v15, v17

    invoke-static {v6, v15}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lmultispace/multiapp/clone/util/o0O000o0;->o00oOOo0(Ljava/lang/String;)V

    invoke-static/range {p1 .. p2}, LoooOO0/o00oOo0O;->o00oOoOO(Ljava/lang/String;I)Landroid/content/Intent;

    move-result-object v5

    const/16 v6, 0x2d

    new-array v6, v6, [B

    const/16 v15, -0x49

    aput-byte v15, v6, v10

    aput-byte v10, v6, v11

    const/16 v15, -0x2b

    aput-byte v15, v6, v13

    const/16 v15, 0x6c

    aput-byte v15, v6, v14

    aput-byte v19, v6, v16

    const/16 v15, 0x7e

    aput-byte v15, v6, v8

    aput-byte v10, v6, v9

    const/16 v15, -0x3a

    aput-byte v15, v6, v17

    const/16 v15, -0x48

    aput-byte v15, v6, v4

    const/16 v15, 0x2c

    aput-byte v15, v6, v18

    const/16 v15, -0x31

    aput-byte v15, v6, v7

    const/16 v15, 0x54

    aput-byte v15, v6, v2

    aput-byte v4, v6, v19

    const/16 v15, 0x65

    aput-byte v15, v6, v12

    const/16 v15, 0x1a

    aput-byte v15, v6, v3

    const/16 v15, -0x1d

    aput-byte v15, v6, v21

    const/16 v15, -0x41

    aput-byte v15, v6, v22

    const/16 v15, 0x17

    aput-byte v15, v6, v23

    const/16 v26, -0xf

    const/16 v25, 0x12

    aput-byte v26, v6, v25

    const/16 v26, 0x41

    const/16 v24, 0x13

    aput-byte v26, v6, v24

    const/16 v26, 0x14

    aput-byte v3, v6, v26

    const/16 v26, 0x15

    const/16 v27, 0x60

    aput-byte v27, v6, v26

    const/16 v20, 0x16

    aput-byte v21, v6, v20

    const/16 v26, -0x3e

    aput-byte v26, v6, v15

    const/16 v26, 0x18

    const/16 v27, -0x4b

    aput-byte v27, v6, v26

    const/16 v26, 0x19

    const/16 v27, 0x4d

    aput-byte v27, v6, v26

    const/16 v26, 0x1a

    const/16 v27, -0x2f

    aput-byte v27, v6, v26

    const/16 v26, 0x1b

    const/16 v27, 0x41

    aput-byte v27, v6, v26

    const/16 v26, 0x1c

    aput-byte v3, v6, v26

    const/16 v26, 0x1d

    const/16 v27, 0x60

    aput-byte v27, v6, v26

    const/16 v26, 0x1e

    aput-byte v21, v6, v26

    const/16 v26, 0x1f

    const/16 v27, -0x3e

    aput-byte v27, v6, v26

    const/16 v26, 0x20

    const/16 v27, -0x4b

    aput-byte v27, v6, v26

    const/16 v26, 0x21

    const/16 v27, 0x2b

    aput-byte v27, v6, v26

    const/16 v26, 0x22

    const/16 v27, -0x40

    aput-byte v27, v6, v26

    const/16 v26, 0x23

    const/16 v27, 0x4d

    aput-byte v27, v6, v26

    const/16 v26, 0x24

    aput-byte v4, v6, v26

    const/16 v26, 0x25

    const/16 v27, 0x27

    aput-byte v27, v6, v26

    const/16 v26, 0x26

    const/16 v27, 0x1b

    aput-byte v27, v6, v26

    const/16 v26, 0x27

    const/16 v27, -0x2a

    aput-byte v27, v6, v26

    const/16 v26, 0x28

    const/16 v27, -0x4b

    aput-byte v27, v6, v26

    const/16 v26, 0x29

    aput-byte v15, v6, v26

    const/16 v26, 0x2a

    const/16 v27, -0x18

    aput-byte v27, v6, v26

    const/16 v26, 0x2b

    const/16 v27, 0x44

    aput-byte v27, v6, v26

    const/16 v26, 0x2c

    const/16 v27, 0x44

    aput-byte v27, v6, v26

    new-array v15, v4, [B

    const/16 v26, -0x30

    aput-byte v26, v15, v10

    const/16 v26, 0x65

    aput-byte v26, v15, v11

    const/16 v26, -0x5f

    aput-byte v26, v15, v13

    const/16 v26, 0x20

    aput-byte v26, v15, v14

    const/16 v26, 0x6d

    aput-byte v26, v15, v16

    aput-byte v2, v15, v8

    const/16 v26, 0x6e

    aput-byte v26, v15, v9

    const/16 v26, -0x5b

    aput-byte v26, v15, v17

    invoke-static {v6, v15}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    const/high16 v6, 0x200000

    invoke-virtual {v5, v6}, Landroid/content/Intent;->addFlags(I)Landroid/content/Intent;

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    new-array v15, v12, [B

    const/16 v26, -0x28

    aput-byte v26, v15, v10

    const/16 v26, -0x21

    aput-byte v26, v15, v11

    const/16 v26, 0x5c

    aput-byte v26, v15, v13

    const/16 v26, -0x2

    aput-byte v26, v15, v14

    const/16 v26, -0x53

    aput-byte v26, v15, v16

    const/16 v26, 0x7e

    aput-byte v26, v15, v8

    const/16 v26, -0x6a

    aput-byte v26, v15, v9

    const/16 v26, 0x64

    aput-byte v26, v15, v17

    const/16 v26, -0x61

    aput-byte v26, v15, v4

    const/16 v26, -0x77

    aput-byte v26, v15, v18

    aput-byte v11, v15, v7

    const/16 v26, -0x47

    aput-byte v26, v15, v2

    const/16 v26, -0x5c

    aput-byte v26, v15, v19

    new-array v3, v4, [B

    const/16 v27, -0x15

    aput-byte v27, v3, v10

    const/16 v27, -0x14

    aput-byte v27, v3, v11

    const/16 v27, 0x6f

    aput-byte v27, v3, v13

    const/16 v27, -0x33

    aput-byte v27, v3, v14

    const/16 v27, -0x62

    aput-byte v27, v3, v16

    const/16 v27, 0x5e

    aput-byte v27, v3, v8

    const/16 v27, -0x1

    aput-byte v27, v3, v9

    aput-byte v7, v3, v17

    invoke-static {v15, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lmultispace/multiapp/clone/util/o0O000o0;->o00oOOo0(Ljava/lang/String;)V

    invoke-static {v5, v0}, LoooOO0/o00oOo0O;->o00ooO0o(Landroid/content/Intent;I)I

    move-result v3

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v15, 0x13

    new-array v12, v15, [B

    aput-byte v19, v12, v10

    const/16 v15, 0x21

    aput-byte v15, v12, v11

    const/16 v15, -0x6d

    aput-byte v15, v12, v13

    const/16 v15, -0x31

    aput-byte v15, v12, v14

    const/16 v15, -0xf

    aput-byte v15, v12, v16

    const/16 v15, -0x43

    aput-byte v15, v12, v8

    const/16 v15, 0x52

    aput-byte v15, v12, v9

    const/16 v15, -0x54

    aput-byte v15, v12, v17

    const/16 v15, 0x16

    aput-byte v15, v12, v4

    const/16 v15, 0x23

    aput-byte v15, v12, v18

    const/16 v15, -0x65

    aput-byte v15, v12, v7

    const/16 v15, -0x37

    aput-byte v15, v12, v2

    const/4 v15, -0x4

    aput-byte v15, v12, v19

    const/16 v15, -0x24

    const/16 v20, 0xd

    aput-byte v15, v12, v20

    const/16 v15, 0x43

    const/16 v20, 0xe

    aput-byte v15, v12, v20

    const/16 v15, -0x43

    aput-byte v15, v12, v21

    aput-byte v2, v12, v22

    const/16 v15, 0x64

    aput-byte v15, v12, v23

    const/16 v15, -0x38

    const/16 v20, 0x12

    aput-byte v15, v12, v20

    new-array v15, v4, [B

    const/16 v20, 0x7f

    aput-byte v20, v15, v10

    const/16 v20, 0x55

    aput-byte v20, v15, v11

    const/16 v20, -0xe

    aput-byte v20, v15, v13

    const/16 v20, -0x43

    aput-byte v20, v15, v14

    const/16 v20, -0x7b

    aput-byte v20, v15, v16

    const/16 v20, -0x4

    aput-byte v20, v15, v8

    const/16 v20, 0x31

    aput-byte v20, v15, v9

    const/16 v20, -0x28

    aput-byte v20, v15, v17

    invoke-static {v12, v15}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lmultispace/multiapp/clone/util/o0O000o0;->o00oOOo0(Ljava/lang/String;)V

    if-eqz v3, :cond_2

    if-eq v3, v13, :cond_2

    invoke-static {}, Lmultispace/multiapp/clone/util/o00oOo0O;->o00oOo0O()V

    invoke-static {v5, v0}, LoooOO0/o00oOo0O;->o00ooO0o(Landroid/content/Intent;I)I

    move-result v0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v5, 0x13

    new-array v5, v5, [B

    const/16 v6, 0x4b

    aput-byte v6, v5, v10

    const/16 v6, 0x63

    aput-byte v6, v5, v11

    const/16 v6, -0x52

    aput-byte v6, v5, v13

    const/16 v6, 0x75

    aput-byte v6, v5, v14

    const/16 v6, -0x6b

    aput-byte v6, v5, v16

    const/16 v6, -0x74

    aput-byte v6, v5, v8

    const/16 v6, -0x1b

    aput-byte v6, v5, v9

    const/16 v6, 0x41

    aput-byte v6, v5, v17

    const/16 v6, 0x51

    aput-byte v6, v5, v4

    const/16 v6, 0x61

    aput-byte v6, v5, v18

    const/16 v6, -0x5a

    aput-byte v6, v5, v7

    const/16 v6, 0x73

    aput-byte v6, v5, v2

    const/16 v2, -0x68

    aput-byte v2, v5, v19

    const/16 v2, -0x13

    const/16 v6, 0xd

    aput-byte v2, v5, v6

    const/16 v2, -0xc

    const/16 v6, 0xe

    aput-byte v2, v5, v6

    const/16 v2, 0x50

    aput-byte v2, v5, v21

    const/16 v2, 0x4c

    aput-byte v2, v5, v22

    const/16 v2, 0x25

    aput-byte v2, v5, v23

    const/16 v2, -0xb

    const/16 v6, 0x12

    aput-byte v2, v5, v6

    new-array v2, v4, [B

    const/16 v6, 0x38

    aput-byte v6, v2, v10

    const/16 v6, 0x17

    aput-byte v6, v2, v11

    const/16 v6, -0x31

    aput-byte v6, v2, v13

    aput-byte v17, v2, v14

    const/16 v6, -0x1f

    aput-byte v6, v2, v16

    const/16 v6, -0x33

    aput-byte v6, v2, v8

    const/16 v6, -0x7a

    aput-byte v6, v2, v9

    const/16 v6, 0x35

    aput-byte v6, v2, v17

    invoke-static {v5, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lmultispace/multiapp/clone/util/o0O000o0;->o00oOOo0(Ljava/lang/String;)V

    if-eqz v0, :cond_0

    if-ne v0, v13, :cond_1

    :cond_0
    move v10, v11

    :cond_1
    invoke-static {v10}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-void

    :catchall_0
    move-exception v0

    new-array v2, v8, [B

    fill-array-data v2, :array_4

    new-array v3, v4, [B

    fill-array-data v3, :array_5

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    new-array v2, v7, [B

    fill-array-data v2, :array_6

    new-array v3, v4, [B

    fill-array-data v3, :array_7

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    :goto_0
    return-void

    :array_0
    .array-data 1
        -0x49t
        0x6et
        0x4at
        0x32t
        -0x33t
        -0x3bt
        0x4bt
        -0x1dt
        -0x5at
        0x62t
        0x4ct
    .end array-data

    :array_1
    .array-data 1
        -0x39t
        0xft
        0x29t
        0x59t
        -0x54t
        -0x5et
        0x2et
        -0x53t
    .end array-data

    :array_2
    .array-data 1
        -0x7ct
        0x21t
        -0x3ft
        -0x45t
        -0x62t
        0x2ft
        -0x71t
        0x3dt
        -0x62t
        0x25t
        -0x10t
        -0x4ct
        -0x77t
        0x26t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x18t
        0x40t
        -0x4ct
        -0x2bt
        -0x3t
        0x47t
        -0x3dt
        0x54t
    .end array-data

    :array_4
    .array-data 1
        0xct
        0x23t
        0x29t
        -0x7et
        -0x1t
    .end array-data

    nop

    :array_5
    .array-data 1
        0x3dt
        0x12t
        0x18t
        -0x4dt
        -0x32t
        -0x74t
        0x4dt
        0x4dt
    .end array-data

    :array_6
    .array-data 1
        0x14t
        0x52t
        0x60t
        0x3dt
        0x33t
        -0x52t
        0x5ft
        -0x37t
        0x13t
        0x9t
    .end array-data

    nop

    :array_7
    .array-data 1
        0x78t
        0x33t
        0x15t
        0x53t
        0x50t
        -0x3at
        0x1et
        -0x47t
    .end array-data
.end method

.method public final o00oOoOO()V
    .locals 0

    return-void
.end method

.method public final o00oOoOo(Ljava/lang/String;ILandroidx/lifecycle/o0O0OOOo;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0xb

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p3, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p1, p2}, LoooOO0/o00oOo0O;->o00ooOO0(Ljava/lang/String;I)I

    const/4 v0, 0x0

    invoke-virtual {p0, p2, p1, v0}, Lo0OOOO00/o00oOOo0;->o00oOooo(ILjava/lang/String;Z)V

    const p1, 0x7f120167

    new-array p2, v0, [Ljava/lang/String;

    invoke-static {p1, p2}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    :array_0
    .array-data 1
        -0x7at
        0x28t
        -0x78t
        -0x3dt
        0x5t
        0xat
        0x51t
        0x29t
        -0x69t
        0x24t
        -0x72t
    .end array-data

    :array_1
    .array-data 1
        -0xat
        0x49t
        -0x15t
        -0x58t
        0x64t
        0x6dt
        0x34t
        0x67t
    .end array-data

    :array_2
    .array-data 1
        0x1ft
        0x4at
        -0x35t
        -0x4ct
        -0x58t
        -0x3ct
        -0xbt
        0x21t
        0x1bt
        0x4at
        -0x4t
        -0x60t
        -0x50t
        -0x2ft
    .end array-data

    nop

    :array_3
    .array-data 1
        0x6dt
        0x2ft
        -0x48t
        -0x3ft
        -0x3ct
        -0x50t
        -0x47t
        0x48t
    .end array-data
.end method

.method public final o00oOoo0(ILjava/util/List;)V
    .locals 11
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O0o;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_0

    new-array v2, v0, [B

    fill-array-data v2, :array_1

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-static {p2, v1}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {}, Lo0OOO0o0/o00oOo0O;->o00oOOo0()Landroid/content/SharedPreferences;

    move-result-object v1

    invoke-interface {v1}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v1

    const/4 v2, 0x6

    new-array v2, v2, [B

    fill-array-data v2, :array_2

    new-array v3, v0, [B

    fill-array-data v3, :array_3

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v3, 0x7

    new-array v3, v3, [B

    fill-array-data v3, :array_4

    new-array v4, v0, [B

    fill-array-data v4, :array_5

    invoke-static {v3, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    move-object v2, p2

    check-cast v2, Ljava/lang/Iterable;

    const/4 p2, 0x1

    new-array p2, p2, [B

    const/4 v3, 0x0

    const/16 v4, -0x3c

    aput-byte v4, p2, v3

    new-array v0, v0, [B

    fill-array-data v0, :array_6

    invoke-static {p2, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    sget-object v8, Lo0OOOO00/o00oOOo0$o00oOOo0;->INSTANCE:Lo0OOOO00/o00oOOo0$o00oOOo0;

    const/16 v9, 0x1e

    const/4 v10, 0x0

    invoke-static/range {v2 .. v10}, Lkotlin/collections/o0O0oo0o;->o0OOo0o0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-interface {v1, p1, p2}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    invoke-interface {v1}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void

    nop

    :array_0
    .array-data 1
        0x42t
        0x50t
        -0x5ct
        0x35t
        0x43t
        -0x76t
        0x7dt
        -0x48t
    .end array-data

    :array_1
    .array-data 1
        0x26t
        0x31t
        -0x30t
        0x54t
        0xft
        -0x1dt
        0xet
        -0x34t
    .end array-data

    :array_2
    .array-data 1
        0x11t
        0x71t
        -0x11t
        -0x67t
        -0x38t
        -0xet
    .end array-data

    nop

    :array_3
    .array-data 1
        0x74t
        0x15t
        -0x7at
        -0x13t
        -0x59t
        -0x80t
        -0x3bt
        -0x3ft
    .end array-data

    :array_4
    .array-data 1
        0x59t
        -0x34t
        -0x21t
        0x4et
        -0x7at
        -0x32t
        0x5at
    .end array-data

    :array_5
    .array-data 1
        0x18t
        -0x44t
        -0x51t
        0x2t
        -0x11t
        -0x43t
        0x2et
        0x26t
    .end array-data

    :array_6
    .array-data 1
        -0x18t
        -0x51t
        0x17t
        0x3t
        0x3at
        0x78t
        0x6ft
        0x5at
    .end array-data
.end method

.method public final o00oOooO()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OOOO00/o00oOOo0;->o00oOOo0:Ljava/lang/String;

    return-object v0
.end method

.method public final o00oOooo(ILjava/lang/String;Z)V
    .locals 17

    move/from16 v0, p1

    move-object/from16 v1, p2

    invoke-static {}, Lo0OOO0o0/o00oOo0O;->o00oOOo0()Landroid/content/SharedPreferences;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v4, 0x7

    new-array v5, v4, [B

    fill-array-data v5, :array_0

    const/16 v6, 0x8

    new-array v7, v6, [B

    fill-array-data v7, :array_1

    invoke-static {v5, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v5, ""

    invoke-interface {v2, v3, v5}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    new-instance v2, Ljava/util/LinkedHashSet;

    invoke-direct {v2}, Ljava/util/LinkedHashSet;-><init>()V

    const/4 v3, 0x0

    const/4 v5, 0x1

    if-eqz v7, :cond_0

    new-array v8, v5, [Ljava/lang/String;

    new-array v9, v5, [B

    const/16 v10, -0x36

    aput-byte v10, v9, v3

    new-array v10, v6, [B

    fill-array-data v10, :array_2

    invoke-static {v9, v10}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v9

    aput-object v9, v8, v3

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x6

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Lkotlin/text/o0O00o00;->o0Ooo0(Ljava/lang/CharSequence;[Ljava/lang/String;ZIILjava/lang/Object;)Ljava/util/List;

    move-result-object v7

    check-cast v7, Ljava/util/Collection;

    invoke-virtual {v2, v7}, Ljava/util/AbstractCollection;->addAll(Ljava/util/Collection;)Z

    :cond_0
    if-eqz p3, :cond_1

    invoke-virtual {v2, v1}, Ljava/util/AbstractCollection;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    invoke-virtual {v2, v1}, Ljava/util/AbstractCollection;->remove(Ljava/lang/Object;)Z

    :goto_0
    invoke-static {}, Lo0OOO0o0/o00oOo0O;->o00oOOo0()Landroid/content/SharedPreferences;

    move-result-object v1

    invoke-interface {v1}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v1

    const/4 v7, 0x6

    new-array v7, v7, [B

    fill-array-data v7, :array_3

    new-array v8, v6, [B

    fill-array-data v8, :array_4

    invoke-static {v7, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    invoke-static {v1, v7}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    new-array v4, v4, [B

    fill-array-data v4, :array_5

    new-array v8, v6, [B

    fill-array-data v8, :array_6

    invoke-static {v4, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-array v4, v5, [B

    const/16 v5, 0x24

    aput-byte v5, v4, v3

    new-array v3, v6, [B

    fill-array-data v3, :array_7

    invoke-static {v4, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v9

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/16 v15, 0x3e

    const/16 v16, 0x0

    move-object v8, v2

    invoke-static/range {v8 .. v16}, Lkotlin/collections/o0O0oo0o;->o0OOo0o0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v0, v2}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    invoke-interface {v1}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void

    nop

    :array_0
    .array-data 1
        -0x6t
        0x2t
        -0x40t
        0x38t
        0x63t
        -0x43t
        -0x47t
    .end array-data

    :array_1
    .array-data 1
        -0x45t
        0x72t
        -0x50t
        0x74t
        0xat
        -0x32t
        -0x33t
        -0x63t
    .end array-data

    :array_2
    .array-data 1
        -0x1at
        -0x72t
        0x1t
        -0x4ct
        0x3bt
        0x73t
        0x35t
        -0x6ft
    .end array-data

    :array_3
    .array-data 1
        0x6at
        0x11t
        -0x28t
        -0x66t
        0x58t
        -0x3ct
    .end array-data

    nop

    :array_4
    .array-data 1
        0xft
        0x75t
        -0x4ft
        -0x12t
        0x37t
        -0x4at
        0x65t
        -0x35t
    .end array-data

    :array_5
    .array-data 1
        -0x71t
        -0x56t
        -0x34t
        0x5dt
        -0x58t
        -0x28t
        0x6at
    .end array-data

    :array_6
    .array-data 1
        -0x32t
        -0x26t
        -0x44t
        0x11t
        -0x3ft
        -0x55t
        0x1et
        -0x75t
    .end array-data

    :array_7
    .array-data 1
        0x8t
        -0x1ft
        0x31t
        -0x43t
        -0x2ct
        -0xbt
        0x37t
        -0x6at
    .end array-data
.end method
