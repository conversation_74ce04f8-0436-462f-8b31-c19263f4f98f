.class public final Lo0OOo00/o00oo0O$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0OoO00O;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOOoO"
.end annotation


# static fields
.field public static final synthetic o0O0o:Z


# instance fields
.field public final o00oo0O:Lokio/o00oOo00;

.field public final o00oo0O0:Lokio/o00oOo00;

.field public final o00oo0Oo:J

.field public o00oo0o:Z

.field public o00oo0o0:Z

.field public final synthetic o00oo0oO:Lo0OOo00/o00oo0O;


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lo0OOo00/o00oo0O;J)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Lokio/o00oOo00;

    invoke-direct {p1}, Lokio/o00oOo00;-><init>()V

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O0:Lokio/o00oOo00;

    new-instance p1, Lokio/o00oOo00;

    invoke-direct {p1}, Lokio/o00oOo00;-><init>()V

    iput-object p1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    iput-wide p2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0Oo:J

    return-void
.end method


# virtual methods
.method public close()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    monitor-enter v0

    const/4 v1, 0x1

    :try_start_0
    iput-boolean v1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0o0:Z

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    invoke-virtual {v1}, Lokio/o00oOo00;->o00oOo0o()V

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    invoke-virtual {v1}, Ljava/lang/Object;->notifyAll()V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oOOoO()V

    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public final o00oOOo0()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0o0:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOooo:Lo0OOo00/o00oOo00;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Lo0OOo00/o0O00000;

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOooo:Lo0OOo00/o00oOo00;

    invoke-direct {v0, v1}, Lo0OOo00/o0O00000;-><init>(Lo0OOo00/o00oOo00;)V

    throw v0

    :cond_1
    new-instance v0, Ljava/io/IOException;

    const-string v1, "stream closed"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public o00oOOoO(Lokio/o00oOoO;J)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-lez v2, :cond_6

    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    monitor-enter v2

    :try_start_0
    iget-boolean v3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0o:Z

    iget-object v4, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    iget-wide v4, v4, Lokio/o00oOo00;->o00oo0O:J

    add-long/2addr v4, p2

    iget-wide v6, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0Oo:J

    cmp-long v4, v4, v6

    const/4 v5, 0x1

    const/4 v6, 0x0

    if-lez v4, :cond_0

    move v4, v5

    goto :goto_1

    :cond_0
    move v4, v6

    :goto_1
    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    if-eqz v4, :cond_1

    invoke-interface {p1, p2, p3}, Lokio/o00oOoO;->skip(J)V

    iget-object p1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    sget-object p2, Lo0OOo00/o00oOo00;->FLOW_CONTROL_ERROR:Lo0OOo00/o00oOo00;

    invoke-virtual {p1, p2}, Lo0OOo00/o00oo0O;->o00oOo0o(Lo0OOo00/o00oOo00;)V

    return-void

    :cond_1
    if-eqz v3, :cond_2

    invoke-interface {p1, p2, p3}, Lokio/o00oOoO;->skip(J)V

    return-void

    :cond_2
    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O0:Lokio/o00oOo00;

    invoke-interface {p1, v2, p2, p3}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide v2

    const-wide/16 v7, -0x1

    cmp-long v4, v2, v7

    if-eqz v4, :cond_5

    sub-long/2addr p2, v2

    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    monitor-enter v2

    :try_start_1
    iget-object v3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    iget-wide v7, v3, Lokio/o00oOo00;->o00oo0O:J

    cmp-long v0, v7, v0

    if-nez v0, :cond_3

    goto :goto_2

    :cond_3
    move v5, v6

    :goto_2
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O0:Lokio/o00oOo00;

    invoke-virtual {v3, v0}, Lokio/o00oOo00;->o0O000O(Lokio/o0OoO00O;)J

    if-eqz v5, :cond_4

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    invoke-virtual {v0}, Ljava/lang/Object;->notifyAll()V

    :cond_4
    monitor-exit v2

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_5
    new-instance p1, Ljava/io/EOFException;

    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    throw p1

    :catchall_1
    move-exception p1

    :try_start_2
    monitor-exit v2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1

    :cond_6
    return-void
.end method

.method public final o00oOo0O()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOoOo:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v0}, Lokio/o00oOOo0;->o00oo00O()V

    :goto_0
    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    iget-wide v0, v0, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0o:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0o0:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v1, v0, Lo0OOo00/o00oo0O;->o00oOooo:Lo0OOo00/o00oOo00;

    if-nez v1, :cond_0

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o0O0o()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOoOo:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v0}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    return-void

    :catchall_0
    move-exception v0

    iget-object v1, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v1, v1, Lo0OOo00/o00oo0O;->o00oOoOo:Lo0OOo00/o00oo0O$o00oOo00;

    invoke-virtual {v1}, Lo0OOo00/o00oo0O$o00oOo00;->o00oo()V

    throw v0
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0O;->o00oOoOo:Lo0OOo00/o00oo0O$o00oOo00;

    return-object v0
.end method

.method public o0O0o0oO(Lokio/o00oOo00;J)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-ltz v2, :cond_3

    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    monitor-enter v2

    :try_start_0
    invoke-virtual {p0}, Lo0OOo00/o00oo0O$o00oOOoO;->o00oOo0O()V

    invoke-virtual {p0}, Lo0OOo00/o00oo0O$o00oOOoO;->o00oOOo0()V

    iget-object v3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0O:Lokio/o00oOo00;

    iget-wide v4, v3, Lokio/o00oOo00;->o00oo0O:J

    cmp-long v6, v4, v0

    if-nez v6, :cond_0

    monitor-exit v2

    const-wide/16 p1, -0x1

    return-wide p1

    :cond_0
    invoke-static {p2, p3, v4, v5}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p2

    invoke-virtual {v3, p1, p2, p3}, Lokio/o00oOo00;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide p1

    iget-object p3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-wide v3, p3, Lo0OOo00/o00oo0O;->o00oOOo0:J

    add-long/2addr v3, p1

    iput-wide v3, p3, Lo0OOo00/o00oo0O;->o00oOOo0:J

    iget-object p3, p3, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget-object p3, p3, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    invoke-virtual {p3}, Lo0OOo00/o0;->o00oOo0O()I

    move-result p3

    div-int/lit8 p3, p3, 0x2

    int-to-long v5, p3

    cmp-long p3, v3, v5

    if-ltz p3, :cond_1

    iget-object p3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v3, p3, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget v4, p3, Lo0OOo00/o00oo0O;->o00oOo00:I

    iget-wide v5, p3, Lo0OOo00/o00oo0O;->o00oOOo0:J

    invoke-virtual {v3, v4, v5, v6}, Lo0OOo00/o00oo0OO;->o0O0o0O(IJ)V

    iget-object p3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iput-wide v0, p3, Lo0OOo00/o00oo0O;->o00oOOo0:J

    :cond_1
    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    iget-object p3, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object p3, p3, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    monitor-enter p3

    :try_start_1
    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v2, v2, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget-wide v3, v2, Lo0OOo00/o00oo0OO;->o00ooO0o:J

    add-long/2addr v3, p1

    iput-wide v3, v2, Lo0OOo00/o00oo0OO;->o00ooO0o:J

    iget-object v2, v2, Lo0OOo00/o00oo0OO;->o00ooOO0:Lo0OOo00/o0;

    invoke-virtual {v2}, Lo0OOo00/o0;->o00oOo0O()I

    move-result v2

    div-int/lit8 v2, v2, 0x2

    int-to-long v5, v2

    cmp-long v2, v3, v5

    if-ltz v2, :cond_2

    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v2, v2, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iget-wide v3, v2, Lo0OOo00/o00oo0OO;->o00ooO0o:J

    const/4 v5, 0x0

    invoke-virtual {v2, v5, v3, v4}, Lo0OOo00/o00oo0OO;->o0O0o0O(IJ)V

    iget-object v2, p0, Lo0OOo00/o00oo0O$o00oOOoO;->o00oo0oO:Lo0OOo00/o00oo0O;

    iget-object v2, v2, Lo0OOo00/o00oo0O;->o00oOooO:Lo0OOo00/o00oo0OO;

    iput-wide v0, v2, Lo0OOo00/o00oo0OO;->o00ooO0o:J

    :cond_2
    monitor-exit p3

    return-wide p1

    :catchall_0
    move-exception p1

    monitor-exit p3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :catchall_1
    move-exception p1

    :try_start_2
    monitor-exit v2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1

    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "byteCount < 0: "

    invoke-static {v0, p2, p3}, Lo0O0O0O/o00oOo0O;->o00oOOo0(Ljava/lang/String;J)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
