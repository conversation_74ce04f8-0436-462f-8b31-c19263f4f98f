.class public Lo0OOo000/o00oOOo0$o00oo00O;
.super Lo0OOo000/o00oOOo0$o00oOOoO;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oo00O"
.end annotation


# instance fields
.field public final synthetic o00oo0o:Lo0OOo000/o00oOOo0;

.field public o00oo0o0:J


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;J)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o:Lo0OOo000/o00oOOo0;

    invoke-direct {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;-><init>(Lo0OOo000/o00oOOo0;)V

    iput-wide p2, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o0:J

    const-wide/16 v0, 0x0

    cmp-long p1, p2, v0

    if-nez p1, :cond_0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_0
    return-void
.end method


# virtual methods
.method public close()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o0:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-eqz v0, :cond_1

    const/16 v0, 0x64

    sget-object v1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-static {p0, v0, v1}, Lo0OOOoOo/o0O0000O;->o00oOoOo(Lokio/o0OoO00O;ILjava/util/concurrent/TimeUnit;)Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    return-void
.end method

.method public o0O0o0oO(Lokio/o00oOo00;J)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-ltz v2, :cond_4

    iget-boolean v2, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-nez v2, :cond_3

    iget-wide v2, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o0:J

    cmp-long v4, v2, v0

    const-wide/16 v5, -0x1

    if-nez v4, :cond_0

    return-wide v5

    :cond_0
    iget-object v4, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o:Lo0OOo000/o00oOOo0;

    iget-object v4, v4, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-static {v2, v3, p2, p3}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p2

    invoke-interface {v4, p1, p2, p3}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide p1

    cmp-long p3, p1, v5

    if-eqz p3, :cond_2

    iget-wide v2, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o0:J

    sub-long/2addr v2, p1

    iput-wide v2, p0, Lo0OOo000/o00oOOo0$o00oo00O;->o00oo0o0:J

    cmp-long p3, v2, v0

    if-nez p3, :cond_1

    const/4 p3, 0x1

    invoke-virtual {p0, p3}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_1
    return-wide p1

    :cond_2
    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    new-instance p1, Ljava/net/ProtocolException;

    const-string p2, "unexpected end of stream"

    invoke-direct {p1, p2}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_4
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "byteCount < 0: "

    invoke-static {v0, p2, p3}, Lo0O0O0O/o00oOo0O;->o00oOOo0(Ljava/lang/String;J)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
