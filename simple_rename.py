#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from pathlib import Path

def rename_main_obfuscated_dirs():
    """Rename main obfuscated directories only"""
    base_path = Path("vcamera_out/smali_classes3")
    
    if not base_path.exists():
        print("smali_classes3 directory not found!")
        return
    
    # List of obfuscated directory names to rename
    obfuscated_dirs = []
    
    # Get all directories that look obfuscated
    for item in base_path.iterdir():
        if item.is_dir():
            name = item.name
            # Check if name contains only o, O, 0 and is likely obfuscated
            if (len(name) > 2 and 
                any(c in name for c in ['o', 'O', '0']) and
                name.count('o') + name.count('O') + name.count('0') > len(name) * 0.6):
                obfuscated_dirs.append(name)
    
    print(f"Found {len(obfuscated_dirs)} obfuscated directories:")
    for dir_name in obfuscated_dirs[:10]:  # Show first 10
        print(f"  - {dir_name}")
    
    if len(obfuscated_dirs) > 10:
        print(f"  ... and {len(obfuscated_dirs) - 10} more")
    
    # Rename them to more readable names
    counter = 1
    renamed_count = 0
    
    for old_name in obfuscated_dirs:
        old_path = base_path / old_name
        new_name = f"ObfuscatedClass{counter:03d}"
        new_path = base_path / new_name
        
        # Make sure new name doesn't exist
        while new_path.exists():
            counter += 1
            new_name = f"ObfuscatedClass{counter:03d}"
            new_path = base_path / new_name
        
        try:
            shutil.move(str(old_path), str(new_path))
            print(f"Renamed: {old_name} -> {new_name}")
            renamed_count += 1
            counter += 1
        except Exception as e:
            print(f"Error renaming {old_name}: {e}")
    
    print(f"\nSuccessfully renamed {renamed_count} directories!")

if __name__ == "__main__":
    rename_main_obfuscated_dirs()
