.class public Lo0OOOO0o/o00oOoO$o00oOOo0;
.super Lde/robv/android/xposed/XC_MethodHook;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOOO0o/o00oOoO;->o00oOOo0()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOOO0o/o00oOoO;


# direct methods
.method public constructor <init>(Lo0OOOO0o/o00oOoO;)V
    .locals 0

    iput-object p1, p0, Lo0OOOO0o/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOOO0o/o00oOoO;

    invoke-direct {p0}, Lde/robv/android/xposed/XC_MethodHook;-><init>()V

    return-void
.end method


# virtual methods
.method public beforeHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {p1, v0}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setResult(Ljava/lang/Object;)V

    const/16 p1, 0x28

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    const/16 v0, 0x8

    new-array v0, v0, [B

    fill-array-data v0, :array_1

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lmultispace/multiapp/clone/util/o0O000o0;->o00oOOo0(Ljava/lang/String;)V

    return-void

    nop

    :array_0
    .array-data 1
        -0x37t
        0x1t
        -0x23t
        -0x53t
        -0x7t
        -0x44t
        -0xet
        0x63t
        -0x34t
        0xbt
        -0x2ft
        -0x5bt
        -0x1bt
        -0x43t
        -0x3t
        0x7et
        -0x32t
        0x15t
        -0x33t
        -0x4at
        -0x16t
        -0x49t
        -0x1et
        0x76t
        -0x3dt
        0x6t
        -0x10t
        -0x55t
        -0x1bt
        -0x7t
        -0x1et
        0x72t
        -0x2ct
        0x52t
        -0x13t
        -0x4at
        -0x2t
        -0x44t
        -0x41t
        0x39t
    .end array-data

    :array_1
    .array-data 1
        -0x60t
        0x72t
        -0x67t
        -0x3ct
        -0x75t
        -0x27t
        -0x6ft
        0x17t
    .end array-data
.end method
