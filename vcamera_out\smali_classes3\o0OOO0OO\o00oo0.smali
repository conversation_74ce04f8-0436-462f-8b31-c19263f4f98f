.class public Lo0OOO0Oo/o00oo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOO0Oo/o00oo0$o00oOo0O;
    }
.end annotation


# static fields
.field public static final o00oOo0O:Ljava/lang/String;

.field public static final o00oOo0o:J = 0x6ddd00L

.field public static o00oOoO0:Lo0OOO0Oo/o00oo0;


# instance fields
.field public o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

.field public o00oOOoO:Z

.field public o00oOo00:J

.field public o00oOooO:Ljava/lang/Runnable;


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    const/16 v0, 0x13

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lo0OOO0Oo/o00oo0;->o00oOo0O:Ljava/lang/String;

    const/4 v0, 0x0

    sput-object v0, Lo0OOO0Oo/o00oo0;->o00oOoO0:Lo0OOO0Oo/o00oo0;

    return-void

    :array_0
    .array-data 1
        -0x55t
        -0x68t
        -0x3et
        0x75t
        0x31t
        0x68t
        0x78t
        0xet
        -0x68t
        -0x4dt
        -0x1t
        0x71t
        0x31t
        0x5at
        0x7ct
        0x19t
        -0x71t
        -0x67t
        -0x1ft
    .end array-data

    :array_1
    .array-data 1
        -0x16t
        -0x4t
        -0x71t
        0x14t
        0x5ft
        0x9t
        0x1ft
        0x6bt
    .end array-data
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lo0OOO0Oo/o00oo0;->o00oOo00:J

    new-instance v0, Lo0OOO0Oo/o00oo0$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oo0$o00oOOo0;-><init>(Lo0OOO0Oo/o00oo0;)V

    iput-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOooO:Ljava/lang/Runnable;

    return-void
.end method

.method public static synthetic o00oOOo0(Lo0OOO0Oo/o00oo0;)J
    .locals 2

    iget-wide v0, p0, Lo0OOO0Oo/o00oo0;->o00oOo00:J

    return-wide v0
.end method

.method public static synthetic o00oOOoO(Lo0OOO0Oo/o00oo0;J)J
    .locals 0

    iput-wide p1, p0, Lo0OOO0Oo/o00oo0;->o00oOo00:J

    return-wide p1
.end method

.method public static synthetic o00oOo00(Lo0OOO0Oo/o00oo0;)Lcom/google/android/gms/ads/appopen/AppOpenAd;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    return-object p0
.end method

.method public static synthetic o00oOo0O(Lo0OOO0Oo/o00oo0;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oo0;->o00oOooO:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static o00oOo0o()Lo0OOO0Oo/o00oo0;
    .locals 2

    sget-object v0, Lo0OOO0Oo/o00oo0;->o00oOoO0:Lo0OOO0Oo/o00oo0;

    if-nez v0, :cond_1

    const-class v0, Lo0OOO0Oo/o00oo0;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lo0OOO0Oo/o00oo0;->o00oOoO0:Lo0OOO0Oo/o00oo0;

    if-nez v1, :cond_0

    new-instance v1, Lo0OOO0Oo/o00oo0;

    invoke-direct {v1}, Lo0OOO0Oo/o00oo0;-><init>()V

    sput-object v1, Lo0OOO0Oo/o00oo0;->o00oOoO0:Lo0OOO0Oo/o00oo0;

    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    :cond_1
    :goto_0
    sget-object v0, Lo0OOO0Oo/o00oo0;->o00oOoO0:Lo0OOO0Oo/o00oo0;

    return-object v0
.end method

.method public static synthetic o00oOooO(Lo0OOO0Oo/o00oo0;Lcom/google/android/gms/ads/appopen/AppOpenAd;)Lcom/google/android/gms/ads/appopen/AppOpenAd;
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    return-object p1
.end method


# virtual methods
.method public o00oOoO()Z
    .locals 1

    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOoO:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public final o00oOoO0()Z
    .locals 2

    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    if-eqz v0, :cond_0

    const-wide/16 v0, 0x4

    invoke-virtual {p0, v0, v1}, Lo0OOO0Oo/o00oo0;->o00oo0(J)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public declared-synchronized o00oOoOO(Landroid/content/Context;)V
    .locals 4

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    if-nez v0, :cond_0

    iget-boolean v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOoO:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOoO:Z

    new-instance v1, Lcom/google/android/gms/ads/AdRequest$Builder;

    invoke-direct {v1}, Lcom/google/android/gms/ads/AdRequest$Builder;-><init>()V

    invoke-virtual {v1}, Lcom/google/android/gms/ads/AdRequest$Builder;->build()Lcom/google/android/gms/ads/AdRequest;

    move-result-object v1

    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOo00()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Lo0OOO0Oo/o00oo0$o00oOOoO;

    invoke-direct {v3, p0}, Lo0OOO0Oo/o00oo0$o00oOOoO;-><init>(Lo0OOO0Oo/o00oo0;)V

    invoke-static {p1, v2, v1, v0, v3}, Lcom/google/android/gms/ads/appopen/AppOpenAd;->load(Landroid/content/Context;Ljava/lang/String;Lcom/google/android/gms/ads/AdRequest;ILcom/google/android/gms/ads/appopen/AppOpenAd$AppOpenAdLoadCallback;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o00oOoOo(Lcom/google/android/gms/ads/AdValue;)V
    .locals 17

    :try_start_0
    invoke-static {}, Lo0OOOo00/o00oo0O0;->o00oOooO()Lo0OOOo00/o00oo0O0;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    move-object/from16 v1, p0

    :try_start_1
    iget-object v2, v1, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    invoke-virtual {v2}, Lcom/google/android/gms/ads/appopen/AppOpenAd;->getAdUnitId()Ljava/lang/String;

    move-result-object v2

    const-string v3, ""

    const/16 v4, 0xa

    new-array v4, v4, [B

    const/16 v5, 0xd

    const/4 v6, 0x0

    aput-byte v5, v4, v6

    const/16 v5, -0x6b

    const/4 v7, 0x1

    aput-byte v5, v4, v7

    const/16 v5, 0x14

    const/4 v8, 0x2

    aput-byte v5, v4, v8

    const/16 v9, 0x7e

    const/4 v10, 0x3

    aput-byte v9, v4, v10

    const/16 v9, -0x16

    const/4 v11, 0x4

    aput-byte v9, v4, v11

    const/16 v9, -0x2c

    const/4 v12, 0x5

    aput-byte v9, v4, v12

    const/16 v9, 0x21

    const/4 v13, 0x6

    aput-byte v9, v4, v13

    const/16 v9, 0x71

    const/4 v14, 0x7

    aput-byte v9, v4, v14

    const/16 v15, 0x27

    const/16 v5, 0x8

    aput-byte v15, v4, v5

    const/16 v15, 0x9

    const/16 v16, -0x75

    aput-byte v16, v4, v15

    new-array v5, v5, [B

    const/16 v15, 0x42

    aput-byte v15, v5, v6

    const/16 v6, -0x1b

    aput-byte v6, v5, v7

    aput-byte v9, v5, v8

    const/16 v6, 0x10

    aput-byte v6, v5, v10

    const/16 v6, -0x47

    aput-byte v6, v5, v11

    const/16 v6, -0x49

    aput-byte v6, v5, v12

    const/16 v6, 0x53

    aput-byte v6, v5, v13

    const/16 v6, 0x14

    aput-byte v6, v5, v14

    invoke-static {v4, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    move-object/from16 v5, p1

    invoke-virtual {v0, v5, v2, v3, v4}, Lo0OOOo00/o00oo0O0;->o00oOoO0(Lcom/google/android/gms/ads/AdValue;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_0

    :catchall_1
    move-exception v0

    move-object/from16 v1, p0

    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method public o00oOoo0()V
    .locals 4

    sget-object v0, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v1, p0, Lo0OOO0Oo/o00oo0;->o00oOooO:Ljava/lang/Runnable;

    const-wide/16 v2, 0x7530

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0, v0}, Lo0OOO0Oo/o00oo0;->o00oOoOO(Landroid/content/Context;)V

    return-void
.end method

.method public o00oOooo(Landroid/app/Activity;)Z
    .locals 16

    move-object/from16 v0, p1

    const/4 v1, 0x0

    :try_start_0
    invoke-static {}, Lo0OOO0Oo/o00oo0;->o00oOo0o()Lo0OOO0Oo/o00oo0;

    move-result-object v2

    invoke-virtual {v2}, Lo0OOO0Oo/o00oo0;->o00oOoO()Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_0

    invoke-static {}, Lo0OOO0Oo/o00oo0;->o00oOo0o()Lo0OOO0Oo/o00oo0;

    move-result-object v2

    invoke-virtual {v2, v0}, Lo0OOO0Oo/o00oo0;->o00oo00O(Landroid/app/Activity;)V

    return v3

    :cond_0
    const/16 v2, 0x20

    new-array v2, v2, [B

    const/16 v4, 0x38

    aput-byte v4, v2, v1

    const/16 v4, 0x36

    aput-byte v4, v2, v3

    const/16 v4, 0x3a

    const/4 v5, 0x2

    aput-byte v4, v2, v5

    const/4 v4, 0x3

    const/16 v6, -0x54

    aput-byte v6, v2, v4

    const/4 v7, -0x4

    const/4 v8, 0x4

    aput-byte v7, v2, v8

    const/16 v7, -0x47

    const/4 v9, 0x5

    aput-byte v7, v2, v9

    const/16 v7, -0x77

    const/4 v10, 0x6

    aput-byte v7, v2, v10

    const/16 v7, 0x15

    const/4 v11, 0x7

    aput-byte v7, v2, v11

    const/16 v12, 0x4c

    const/16 v13, 0x8

    aput-byte v12, v2, v13

    const/16 v12, 0x9

    const/16 v14, 0x2d

    aput-byte v14, v2, v12

    const/16 v12, 0xa

    const/16 v14, 0x3c

    aput-byte v14, v2, v12

    const/16 v12, 0xb

    const/4 v14, -0x2

    aput-byte v14, v2, v12

    const/16 v12, 0xc

    const/16 v14, -0xa

    aput-byte v14, v2, v12

    const/16 v12, 0xd

    aput-byte v6, v2, v12

    const/16 v12, 0xe

    const/16 v14, -0x7e

    aput-byte v14, v2, v12

    const/16 v12, 0xf

    const/16 v14, 0x5b

    aput-byte v14, v2, v12

    const/16 v12, 0x10

    aput-byte v8, v2, v12

    const/16 v12, 0x11

    const/16 v15, 0x3f

    aput-byte v15, v2, v12

    const/16 v12, 0x12

    const/16 v15, 0x2c

    aput-byte v15, v2, v12

    const/16 v12, 0x13

    aput-byte v6, v2, v12

    const/16 v6, 0x14

    const/4 v12, -0x3

    aput-byte v12, v2, v6

    const/16 v6, -0x5a

    aput-byte v6, v2, v7

    const/16 v6, 0x16

    const/16 v7, -0x68

    aput-byte v7, v2, v6

    const/16 v6, 0x17

    aput-byte v14, v2, v6

    const/16 v6, 0x18

    const/16 v7, 0x1e

    aput-byte v7, v2, v6

    const/16 v6, 0x19

    const/16 v12, 0x3b

    aput-byte v12, v2, v6

    const/16 v6, 0x1a

    const/16 v12, 0x3e

    aput-byte v12, v2, v6

    const/16 v6, 0x1b

    const/16 v12, -0x18

    aput-byte v12, v2, v6

    const/16 v6, 0x1c

    const/16 v12, -0x16

    aput-byte v12, v2, v6

    const/16 v6, 0x1d

    const/16 v12, -0x19

    aput-byte v12, v2, v6

    const/16 v6, -0x3e

    aput-byte v6, v2, v7

    const/16 v6, 0x1f

    const/16 v7, 0x55

    aput-byte v7, v2, v6

    new-array v6, v13, [B

    const/16 v7, 0x6c

    aput-byte v7, v6, v1

    const/16 v7, 0x5e

    aput-byte v7, v6, v3

    const/16 v3, 0x5f

    aput-byte v3, v6, v5

    const/16 v3, -0x74

    aput-byte v3, v6, v4

    const/16 v3, -0x6d

    aput-byte v3, v6, v8

    const/16 v3, -0x37

    aput-byte v3, v6, v9

    const/16 v3, -0x14

    aput-byte v3, v6, v10

    const/16 v3, 0x7b

    aput-byte v3, v6, v11

    invoke-static {v2, v6}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-static {}, Lo0OOO0Oo/o00oo0;->o00oOo0o()Lo0OOO0Oo/o00oo0;

    move-result-object v2

    invoke-virtual {v2, v0}, Lo0OOO0Oo/o00oo0;->o00oOoOO(Landroid/content/Context;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return v1

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    return v1
.end method

.method public final o00oo0(J)Z
    .locals 4

    new-instance v0, Ljava/util/Date;

    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    invoke-virtual {v0}, Ljava/util/Date;->getTime()J

    move-result-wide v0

    iget-wide v2, p0, Lo0OOO0Oo/o00oo0;->o00oOo00:J

    sub-long/2addr v0, v2

    const-wide/32 v2, 0x36ee80

    mul-long/2addr p1, v2

    cmp-long p1, v0, p1

    if-gez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final o00oo00O(Landroid/app/Activity;)V
    .locals 3

    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    const/16 v1, 0x8

    if-nez v0, :cond_0

    const/16 p1, 0x21

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    new-array v0, v1, [B

    fill-array-data v0, :array_1

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    return-void

    :cond_0
    invoke-virtual {p0}, Lo0OOO0Oo/o00oo0;->o00oOoO0()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0, p1}, Lo0OOO0Oo/o00oo0;->o00oOoOO(Landroid/content/Context;)V

    return-void

    :cond_1
    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    new-instance v2, Lo0OOO0Oo/o00oo0$o00oOo00;

    invoke-direct {v2, p0, p1}, Lo0OOO0Oo/o00oo0$o00oOo00;-><init>(Lo0OOO0Oo/o00oo0;Landroid/app/Activity;)V

    invoke-virtual {v0, v2}, Lcom/google/android/gms/ads/appopen/AppOpenAd;->setFullScreenContentCallback(Lcom/google/android/gms/ads/FullScreenContentCallback;)V

    iget-object v0, p0, Lo0OOO0Oo/o00oo0;->o00oOOo0:Lcom/google/android/gms/ads/appopen/AppOpenAd;

    invoke-virtual {v0, p1}, Lcom/google/android/gms/ads/appopen/AppOpenAd;->show(Landroid/app/Activity;)V

    const/16 p1, 0x14

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v1, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    return-void

    nop

    :array_0
    .array-data 1
        0x33t
        -0x62t
        0x3dt
        -0x4ft
        -0x24t
        -0x61t
        0x40t
        -0x77t
        0x15t
        -0x6et
        0x3dt
        -0xbt
        -0x72t
        -0x65t
        0x53t
        -0x38t
        0x10t
        -0x69t
        0x2bt
        -0x1t
        -0x77t
        -0x72t
        0x17t
        -0x66t
        0x2t
        -0x69t
        0x3ct
        -0x18t
        -0x72t
        -0x7dt
        0x52t
        -0x64t
        0x49t
    .end array-data

    nop

    :array_1
    .array-data 1
        0x67t
        -0xat
        0x58t
        -0x6ft
        -0x52t
        -0x6t
        0x37t
        -0x18t
    .end array-data

    :array_2
    .array-data 1
        0x75t
        -0x32t
        0x43t
        -0x5ft
        0xat
        0x48t
        -0x22t
        -0x6ft
        0x68t
        -0x7at
        0x5ft
        -0x4bt
        0x58t
        0x42t
        -0x35t
        -0x66t
        0x28t
        -0x78t
        0x2t
        -0x8t
    .end array-data

    :array_3
    .array-data 1
        0x6t
        -0x5at
        0x2ct
        -0x2at
        0x2at
        0x27t
        -0x52t
        -0xct
    .end array-data
.end method
