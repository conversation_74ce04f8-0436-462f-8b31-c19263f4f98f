.class public final synthetic Lo0OO0oOo/o0O0o00$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OO0oOo/o0O0o00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = "o00oOOoO"
.end annotation

.annotation runtime Lo0O0oooo/o0OOOO;
    k = 0x3
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic o00oOOo0:[I


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    invoke-static {}, Lo0OO0oOo/o0O0o00O;->values()[Lo0OO0oOo/o0O0o00O;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    :try_start_0
    sget-object v1, Lo0OO0oOo/o0O0o00O;->INVARIANT:Lo0OO0oOo/o0O0o00O;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    const/4 v2, 0x1

    aput v2, v0, v1
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :try_start_1
    sget-object v1, Lo0OO0oOo/o0O0o00O;->IN:Lo0OO0oOo/o0O0o00O;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    const/4 v2, 0x2

    aput v2, v0, v1
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :try_start_2
    sget-object v1, Lo0OO0oOo/o0O0o00O;->OUT:Lo0OO0oOo/o0O0o00O;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    const/4 v2, 0x3

    aput v2, v0, v1
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    sput-object v0, Lo0OO0oOo/o0O0o00$o00oOOoO;->o00oOOo0:[I

    return-void
.end method
