.class public final Lo0OOOO00/o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\t\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001a\u0010\u0007\u001a\u00020\u00062\u0012\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u0002J\u001c\u0010\u000b\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0002J\u001c\u0010\r\u001a\u00020\u00062\u0006\u0010\u000c\u001a\u00020\u00082\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0002J\u001c\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000c\u001a\u00020\u00082\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0002\u00a8\u0006\u0011"
    }
    d2 = {
        "Lo0OOOO00/o00oOoO;",
        "",
        "Landroidx/lifecycle/o0O0OOOo;",
        "",
        "Lo0OOO0oo/o0O0000O;",
        "modulesLiveData",
        "Lo0O0oooo/oO0O00o0;",
        "o00oOOoO",
        "",
        "source",
        "resultLiveData",
        "o00oOo00",
        "packageName",
        "o00oOooO",
        "o00oOOo0",
        "<init>",
        "()V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final o00oOOo0(Ljava/lang/String;Landroidx/lifecycle/o0O0OOOo;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0xb

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-static {p1, v0}, LoooOO0/o00oOo0O;->o00oOo00(Ljava/lang/String;I)I

    const p1, 0x7f12003c

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {p1, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    nop

    :array_0
    .array-data 1
        0x7dt
        0x20t
        0x2dt
        -0xft
        -0x22t
        0x5et
        0x56t
        -0x53t
        0x6ct
        0x2ct
        0x2bt
    .end array-data

    :array_1
    .array-data 1
        0xdt
        0x41t
        0x4et
        -0x66t
        -0x41t
        0x39t
        0x33t
        -0x1dt
    .end array-data

    :array_2
    .array-data 1
        0x75t
        -0x4ft
        0x4ct
        -0x7bt
        -0x7ft
        -0xet
        -0x57t
        -0x13t
        0x71t
        -0x4ft
        0x7bt
        -0x6ft
        -0x67t
        -0x19t
    .end array-data

    nop

    :array_3
    .array-data 1
        0x7t
        -0x2ct
        0x3ft
        -0x10t
        -0x13t
        -0x7at
        -0x1bt
        -0x7ct
    .end array-data
.end method

.method public final o00oOOoO(Landroidx/lifecycle/o0O0OOOo;)V
    .locals 13
    .param p1    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/util/List<",
            "Lo0OOO0oo/o0O0000O;",
            ">;>;)V"
        }
    .end annotation

    const/16 v0, 0xf

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {}, Lo0OOOOo/o00oOo00;->o00oOo00()Lo0OOOOo/o00oOo00;

    move-result-object v0

    invoke-virtual {v0}, Lo0OOOOo/o00oOo00;->o00oOOoO()Ljava/util/List;

    move-result-object v0

    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    const/16 v3, 0xa

    new-array v3, v3, [B

    fill-array-data v3, :array_2

    new-array v4, v1, [B

    fill-array-data v4, :array_3

    invoke-static {v3, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lo0OOOOo/o00oOOoO;

    iget-object v4, v3, Lo0OOOOo/o00oOOoO;->o00oOOo0:Ljava/lang/String;

    const/4 v5, 0x0

    invoke-static {v4, v5, v5}, LoooOO0/o00oOo0O;->o00oOoOo(Ljava/lang/String;II)Landroid/content/pm/PackageInfo;

    move-result-object v4

    new-instance v12, Lo0OOO0oo/o0O0000O;

    iget-object v6, v3, Lo0OOOOo/o00oOOoO;->o00oOOoO:Ljava/lang/String;

    const/4 v5, 0x7

    new-array v7, v5, [B

    fill-array-data v7, :array_4

    new-array v8, v1, [B

    fill-array-data v8, :array_5

    invoke-static {v7, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    invoke-static {v6, v7}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v7, v3, Lo0OOOOo/o00oOOoO;->o00oOo00:Ljava/lang/String;

    new-array v5, v5, [B

    fill-array-data v5, :array_6

    new-array v8, v1, [B

    fill-array-data v8, :array_7

    invoke-static {v5, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-static {v7, v5}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v8, v3, Lo0OOOOo/o00oOOoO;->o00oOOo0:Ljava/lang/String;

    const/16 v5, 0xe

    new-array v5, v5, [B

    fill-array-data v5, :array_8

    new-array v9, v1, [B

    fill-array-data v9, :array_9

    invoke-static {v5, v9}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-static {v8, v5}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v9, v4, Landroid/content/pm/PackageInfo;->versionName:Ljava/lang/String;

    const/16 v5, 0x17

    new-array v5, v5, [B

    fill-array-data v5, :array_a

    new-array v10, v1, [B

    fill-array-data v10, :array_b

    invoke-static {v5, v10}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-static {v9, v5}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    iget-boolean v10, v3, Lo0OOOOo/o00oOOoO;->o00oOo0O:Z

    iget-object v3, v4, Landroid/content/pm/PackageInfo;->applicationInfo:Landroid/content/pm/ApplicationInfo;

    sget-object v4, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v4}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v4

    invoke-virtual {v4}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/content/pm/PackageItemInfo;->loadIcon(Landroid/content/pm/PackageManager;)Landroid/graphics/drawable/Drawable;

    move-result-object v11

    const/16 v3, 0x34

    new-array v3, v3, [B

    fill-array-data v3, :array_c

    new-array v4, v1, [B

    fill-array-data v4, :array_d

    invoke-static {v3, v4}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v11, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v5, v12

    invoke-direct/range {v5 .. v11}, Lo0OOO0oo/o0O0000O;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroid/graphics/drawable/Drawable;)V

    invoke-interface {v2, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :cond_0
    invoke-virtual {p1, v2}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    nop

    :array_0
    .array-data 1
        -0x41t
        0x2t
        -0x14t
        -0x11t
        -0x4dt
        -0x73t
        0x75t
        -0x22t
        -0x45t
        0x1bt
        -0x13t
        -0x22t
        -0x42t
        -0x64t
        0x67t
    .end array-data

    :array_1
    .array-data 1
        -0x2et
        0x6dt
        -0x78t
        -0x66t
        -0x21t
        -0x18t
        0x6t
        -0x6et
    .end array-data

    :array_2
    .array-data 1
        0x37t
        -0x8t
        0x3bt
        -0x5dt
        -0x51t
        -0x67t
        0x3et
        0x7ct
        0x29t
        -0x1dt
    .end array-data

    nop

    :array_3
    .array-data 1
        0x5at
        -0x69t
        0x5ft
        -0x2at
        -0x3dt
        -0x4t
        0x72t
        0x15t
    .end array-data

    :array_4
    .array-data 1
        -0x6at
        -0x70t
        0x5dt
        0x30t
        -0x1at
        -0x5at
        -0x63t
    .end array-data

    :array_5
    .array-data 1
        -0x1t
        -0x1ct
        0x73t
        0x5et
        -0x79t
        -0x35t
        -0x8t
        0x72t
    .end array-data

    :array_6
    .array-data 1
        0x10t
        -0xdt
        0x4t
        0x12t
        -0x1et
        -0x11t
        0x75t
    .end array-data

    :array_7
    .array-data 1
        0x79t
        -0x79t
        0x2at
        0x76t
        -0x79t
        -0x64t
        0x16t
        0xat
    .end array-data

    :array_8
    .array-data 1
        -0x4ft
        0x1t
        -0x39t
        -0x35t
        -0x66t
        -0x46t
        -0x11t
        0x70t
        -0x41t
        0x10t
        -0x59t
        -0x26t
        -0x6at
        -0x44t
    .end array-data

    nop

    :array_9
    .array-data 1
        -0x28t
        0x75t
        -0x17t
        -0x45t
        -0x5t
        -0x27t
        -0x7ct
        0x11t
    .end array-data

    :array_a
    .array-data 1
        0x43t
        0x2t
        0x45t
        -0x10t
        -0x20t
        -0x18t
        -0xat
        0x3at
        0x5dt
        0x5t
        0x49t
        -0x4bt
        -0x9t
        -0x16t
        -0x1ft
        0x0t
        0x5at
        0xct
        0x48t
        -0x2bt
        -0x20t
        -0x1et
        -0xat
    .end array-data

    :array_b
    .array-data 1
        0x33t
        0x63t
        0x26t
        -0x65t
        -0x7ft
        -0x71t
        -0x6dt
        0x73t
    .end array-data

    :array_c
    .array-data 1
        -0x57t
        0x2at
        -0x5at
        0x7ct
        0x68t
        -0x26t
        0xet
        0x63t
        -0x49t
        0x2dt
        -0x56t
        0x39t
        0x68t
        -0x33t
        0x1bt
        0x46t
        -0x50t
        0x28t
        -0x5ct
        0x63t
        0x60t
        -0x2et
        0x5t
        0x63t
        0x3bt
        -0x35t
        0x63t
        0x54t
        0x66t
        -0x2dt
        0x1ft
        0x4ft
        -0x5ft
        0x3ft
        -0x13t
        0x3et
        0x27t
        -0x33t
        0xat
        0x49t
        -0x4et
        0x2at
        -0x5et
        0x72t
        0x44t
        -0x24t
        0x5t
        0x4bt
        -0x42t
        0x2et
        -0x49t
        0x3et
    .end array-data

    :array_d
    .array-data 1
        -0x27t
        0x4bt
        -0x3bt
        0x17t
        0x9t
        -0x43t
        0x6bt
        0x2at
    .end array-data
.end method

.method public final o00oOo00(Ljava/lang/String;Landroidx/lifecycle/o0O0OOOo;)V
    .locals 25
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p1

    move-object/from16 v1, p2

    const/4 v2, 0x6

    new-array v3, v2, [B

    fill-array-data v3, :array_0

    const/16 v4, 0x8

    new-array v5, v4, [B

    fill-array-data v5, :array_1

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v3, 0xe

    new-array v5, v3, [B

    fill-array-data v5, :array_2

    new-array v6, v4, [B

    fill-array-data v6, :array_3

    invoke-static {v5, v6}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v5

    invoke-static {v1, v5}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v5, 0x3

    const/4 v6, 0x5

    :try_start_0
    const-string v7, ""

    invoke-static/range {p1 .. p1}, Landroid/webkit/URLUtil;->isValidUrl(Ljava/lang/String;)Z

    move-result v8

    const/16 v9, 0x62

    const/16 v10, -0x19

    const/16 v15, 0x9

    const/16 v16, -0x7c

    const/16 v18, 0x7

    const/16 v19, 0x4

    const/16 v20, 0x2

    const/4 v12, 0x1

    const/4 v3, 0x0

    if-eqz v8, :cond_1

    invoke-static/range {p1 .. p1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v0

    sget-object v8, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v8}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v11

    invoke-virtual {v11, v0}, Landroid/content/ContentResolver;->openInputStream(Landroid/net/Uri;)Ljava/io/InputStream;

    move-result-object v0

    new-instance v11, Ljava/io/File;

    invoke-virtual {v8}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v8

    invoke-virtual {v8}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v8

    new-array v13, v4, [B

    const/16 v23, 0x5f

    aput-byte v23, v13, v3

    const/16 v23, -0x2d

    aput-byte v23, v13, v12

    const/16 v23, 0x4b

    aput-byte v23, v13, v20

    const/16 v23, 0x19

    aput-byte v23, v13, v5

    const/16 v23, -0x5a

    aput-byte v23, v13, v19

    const/16 v23, -0x12

    aput-byte v23, v13, v6

    const/16 v23, 0x12

    aput-byte v23, v13, v2

    const/16 v23, 0x64

    aput-byte v23, v13, v18

    new-array v14, v4, [B

    const/16 v24, 0x2b

    aput-byte v24, v14, v3

    const/16 v24, -0x4a

    aput-byte v24, v14, v12

    const/16 v24, 0x26

    aput-byte v24, v14, v20

    const/16 v24, 0x69

    aput-byte v24, v14, v5

    const/16 v24, -0x78

    aput-byte v24, v14, v19

    const/16 v24, -0x71

    aput-byte v24, v14, v6

    aput-byte v9, v14, v2

    const/16 v9, 0xf

    aput-byte v9, v14, v18

    invoke-static {v13, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v13

    invoke-direct {v11, v8, v13}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {v11}, Ljava/io/File;->delete()Z

    invoke-static {v0, v11}, Lmultispace/multiapp/clone/util/o00oo;->o00oOoO0(Ljava/io/InputStream;Ljava/io/File;)Z

    invoke-virtual {v11}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lmultispace/multiapp/clone/util/o0O0O0Oo;->o00oOo00(Ljava/lang/String;)Z

    move-result v0

    const/16 v8, -0x3b

    if-nez v0, :cond_0

    new-array v0, v12, [Ljava/lang/String;

    new-array v7, v9, [B

    const/16 v11, 0x2a

    aput-byte v11, v7, v3

    const/16 v11, -0x32

    aput-byte v11, v7, v12

    aput-byte v8, v7, v20

    const/16 v11, -0x53

    aput-byte v11, v7, v5

    aput-byte v10, v7, v19

    const/16 v10, 0x7f

    aput-byte v10, v7, v6

    const/16 v10, -0x52

    aput-byte v10, v7, v2

    const/16 v10, -0x5f

    aput-byte v10, v7, v18

    const/16 v10, 0x21

    aput-byte v10, v7, v4

    aput-byte v8, v7, v15

    const/16 v8, -0x6f

    const/16 v10, 0xa

    aput-byte v8, v7, v10

    const/16 v8, -0x14

    const/16 v10, 0xb

    aput-byte v8, v7, v10

    const/16 v8, -0x11

    const/16 v10, 0xc

    aput-byte v8, v7, v10

    const/16 v8, 0xd

    aput-byte v23, v7, v8

    const/16 v8, -0x11

    const/16 v10, 0xe

    aput-byte v8, v7, v10

    new-array v8, v4, [B

    const/16 v10, 0x44

    aput-byte v10, v8, v3

    const/16 v10, -0x5f

    aput-byte v10, v8, v12

    const/16 v10, -0x4f

    aput-byte v10, v8, v20

    const/16 v10, -0x73

    aput-byte v10, v8, v5

    const/16 v10, -0x61

    aput-byte v10, v8, v19

    aput-byte v9, v8, v6

    const/16 v9, -0x3f

    aput-byte v9, v8, v2

    const/16 v2, -0x2e

    aput-byte v2, v8, v18

    invoke-static {v7, v8}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v3

    const v2, 0x7f1200c1

    invoke-static {v2, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    :cond_0
    invoke-static {v3, v3}, LoooOO0/o00oOo0O;->o00oOoO(II)Ljava/util/List;

    move-result-object v0

    invoke-virtual {v11}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v9

    invoke-static {v9, v3, v12}, LoooOO0/o00oOo0O;->o00oo0O(Ljava/lang/String;IZ)I

    move-result v9

    invoke-static {v3, v3}, LoooOO0/o00oOo0O;->o00oOoO(II)Ljava/util/List;

    move-result-object v11

    new-array v13, v6, [B

    const/16 v14, 0x2d

    aput-byte v14, v13, v3

    const/16 v14, -0x61

    aput-byte v14, v13, v12

    const/16 v14, -0x62

    aput-byte v14, v13, v20

    const/16 v14, 0x46

    aput-byte v14, v13, v5

    const/16 v14, -0x45

    aput-byte v14, v13, v19

    new-array v14, v4, [B

    const/16 v21, 0x41

    aput-byte v21, v14, v3

    const/16 v21, -0xa

    aput-byte v21, v14, v12

    const/16 v21, -0x13

    aput-byte v21, v14, v20

    const/16 v21, 0x32

    aput-byte v21, v14, v5

    const/16 v21, -0x76

    aput-byte v21, v14, v19

    const/16 v21, 0x52

    aput-byte v21, v14, v6

    const/16 v17, -0x4

    aput-byte v17, v14, v2

    const/16 v21, 0x31

    aput-byte v21, v14, v18

    invoke-static {v13, v14}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v13

    invoke-static {v0, v13}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/util/Collection;

    invoke-interface {v11, v0}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    invoke-interface {v11}, Ljava/util/List;->size()I

    move-result v0

    if-ne v0, v12, :cond_3

    invoke-interface {v11, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    const/16 v7, 0xc

    new-array v11, v7, [B

    const/16 v7, -0x57

    aput-byte v7, v11, v3

    const/16 v7, 0x57

    aput-byte v7, v11, v12

    const/16 v7, -0xb

    aput-byte v7, v11, v20

    const/16 v7, -0x33

    aput-byte v7, v11, v5

    const/16 v7, -0x35

    aput-byte v7, v11, v19

    aput-byte v16, v11, v6

    const/16 v7, 0x78

    aput-byte v7, v11, v2

    const/16 v7, -0x6d

    aput-byte v7, v11, v18

    const/16 v7, -0x4f

    aput-byte v7, v11, v4

    const/16 v7, 0x16

    aput-byte v7, v11, v15

    const/16 v7, -0x4a

    const/16 v13, 0xa

    aput-byte v7, v11, v13

    const/16 v7, -0x70

    const/16 v13, 0xb

    aput-byte v7, v11, v13

    new-array v7, v4, [B

    aput-byte v8, v7, v3

    const/16 v8, 0x3e

    aput-byte v8, v7, v12

    const/16 v8, -0x7a

    aput-byte v8, v7, v20

    const/16 v8, -0x47

    aput-byte v8, v7, v5

    const/4 v8, -0x7

    aput-byte v8, v7, v19

    const/16 v8, -0x56

    aput-byte v8, v7, v6

    const/16 v8, 0x1f

    aput-byte v8, v7, v2

    const/16 v8, -0xa

    aput-byte v8, v7, v18

    invoke-static {v11, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v7

    invoke-static {v0, v7}, Lkotlin/jvm/internal/o0ooO;->o00oo0OO(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v7, v0

    check-cast v7, Ljava/lang/String;

    goto :goto_0

    :cond_1
    invoke-static {v0, v3, v3}, LoooOO0/o00oOo0O;->o00oOoOo(Ljava/lang/String;II)Landroid/content/pm/PackageInfo;

    move-result-object v8

    if-nez v8, :cond_2

    move-object v7, v0

    :cond_2
    invoke-static {v0, v3, v12}, LoooOO0/o00oOo0O;->o00oo0Oo(Ljava/lang/String;IZ)I

    move-result v9

    :cond_3
    :goto_0
    if-ne v9, v12, :cond_4

    invoke-static {}, Lo0OOOOo/o00oOo00;->o00oOo00()Lo0OOOOo/o00oOo00;

    move-result-object v0

    invoke-virtual {v0, v7}, Lo0OOOOo/o00oOo00;->o00oOoOO(Ljava/lang/String;)V

    new-array v0, v3, [Ljava/lang/String;

    const v2, 0x7f1200c3

    invoke-static {v2, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    :goto_1
    invoke-virtual {v1, v0}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    goto/16 :goto_2

    :cond_4
    new-array v0, v12, [Ljava/lang/String;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v8, 0xc

    new-array v8, v8, [B

    const/16 v11, 0x79

    aput-byte v11, v8, v3

    aput-byte v16, v8, v12

    const/16 v11, 0x6e

    aput-byte v11, v8, v20

    const/16 v11, -0x55

    aput-byte v11, v8, v5

    const/16 v11, 0x38

    aput-byte v11, v8, v19

    const/16 v11, 0x30

    aput-byte v11, v8, v6

    const/4 v11, -0x4

    aput-byte v11, v8, v2

    aput-byte v10, v8, v18

    const/16 v10, 0x70

    aput-byte v10, v8, v4

    const/16 v10, -0x7f

    aput-byte v10, v8, v15

    const/16 v10, 0x62

    const/16 v11, 0xa

    aput-byte v10, v8, v11

    const/4 v10, -0x3

    const/16 v11, 0xb

    aput-byte v10, v8, v11

    new-array v10, v4, [B

    const/16 v11, 0x1f

    aput-byte v11, v10, v3

    const/16 v11, -0x1b

    aput-byte v11, v10, v12

    aput-byte v18, v10, v20

    const/16 v11, -0x39

    aput-byte v11, v10, v5

    const/16 v11, 0x5d

    aput-byte v11, v10, v19

    const/16 v11, 0x54

    aput-byte v11, v10, v6

    const/16 v11, -0x24

    aput-byte v11, v10, v2

    aput-byte v16, v10, v18

    invoke-static {v8, v10}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v3

    const v2, 0x7f1200c1

    invoke-static {v2, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    new-array v1, v6, [B

    fill-array-data v1, :array_4

    new-array v2, v4, [B

    fill-array-data v2, :array_5

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    new-array v1, v5, [B

    fill-array-data v1, :array_6

    new-array v2, v4, [B

    fill-array-data v2, :array_7

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    :goto_2
    return-void

    :array_0
    .array-data 1
        -0x5bt
        -0x65t
        -0x3ft
        0x50t
        0x7t
        0x57t
    .end array-data

    nop

    :array_1
    .array-data 1
        -0x2at
        -0xct
        -0x4ct
        0x22t
        0x64t
        0x32t
        -0x3bt
        -0x11t
    .end array-data

    :array_2
    .array-data 1
        -0x62t
        0x5dt
        -0x5bt
        -0x6ft
        -0x23t
        0x36t
        -0x69t
        -0x52t
        -0x66t
        0x5dt
        -0x6et
        -0x7bt
        -0x3bt
        0x23t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x14t
        0x38t
        -0x2at
        -0x1ct
        -0x4ft
        0x42t
        -0x25t
        -0x39t
    .end array-data

    :array_4
    .array-data 1
        -0x37t
        0x12t
        -0x5ft
        -0x5ct
        0x14t
    .end array-data

    nop

    :array_5
    .array-data 1
        -0x8t
        0x23t
        -0x70t
        -0x6bt
        0x25t
        -0x25t
        0x4t
        -0x39t
    .end array-data

    :array_6
    .array-data 1
        -0x51t
        -0x3ft
        0x3at
    .end array-data

    :array_7
    .array-data 1
        -0x36t
        -0x47t
        0x0t
        0x21t
        -0xdt
        0x22t
        -0x16t
        -0x1et
    .end array-data
.end method

.method public final o00oOooO(Ljava/lang/String;Landroidx/lifecycle/o0O0OOOo;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/o0O0OOOo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroidx/lifecycle/o0O0OOOo<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0xb

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v0, 0xe

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    invoke-static {p1, v0}, LoooOO0/o00oOo0O;->o00ooOO0(Ljava/lang/String;I)I

    invoke-static {}, Lo0OOOOo/o00oOo00;->o00oOo00()Lo0OOOOo/o00oOo00;

    move-result-object v1

    invoke-virtual {v1, p1}, Lo0OOOOo/o00oOo00;->o00oOoOo(Ljava/lang/String;)V

    const p1, 0x7f12013d

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {p1, v0}, Lmultispace/multiapp/clone/util/o0O00OOO;->o00oOOo0(I[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroidx/lifecycle/o0O0OOOo;->o00oo0OO(Ljava/lang/Object;)V

    return-void

    :array_0
    .array-data 1
        0x76t
        -0x59t
        0x51t
        -0x22t
        -0x7ft
        -0x26t
        0x3t
        -0x7dt
        0x67t
        -0x55t
        0x57t
    .end array-data

    :array_1
    .array-data 1
        0x6t
        -0x3at
        0x32t
        -0x4bt
        -0x20t
        -0x43t
        0x66t
        -0x33t
    .end array-data

    :array_2
    .array-data 1
        -0x7ct
        0x29t
        0x60t
        0x74t
        0x3at
        0x2et
        -0x71t
        -0x61t
        -0x80t
        0x29t
        0x57t
        0x60t
        0x22t
        0x3bt
    .end array-data

    nop

    :array_3
    .array-data 1
        -0xat
        0x4ct
        0x13t
        0x1t
        0x56t
        0x5at
        -0x3dt
        -0xat
    .end array-data
.end method
