.class public final Lo0OOo0O/o00oo00O$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0O00O0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oo00O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOOo0"
.end annotation


# instance fields
.field public o00oo0O:J

.field public o00oo0O0:I

.field public o00oo0Oo:Z

.field public final synthetic o00oo0o:Lo0OOo0O/o00oo00O;

.field public o00oo0o0:Z


# direct methods
.method public constructor <init>(Lo0OOo0O/o00oo00O;)V
    .locals 0

    iput-object p1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public close()V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o0:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget v2, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O0:I

    iget-object v3, v1, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    iget-wide v3, v3, Lokio/o00oOo00;->o00oo0O:J

    iget-boolean v5, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    const/4 v6, 0x1

    invoke-virtual/range {v1 .. v6}, Lo0OOo0O/o00oo00O;->o00oOooO(IJZZ)V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o0:Z

    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    const/4 v1, 0x0

    iput-boolean v1, v0, Lo0OOo0O/o00oo00O;->o00oOoO0:Z

    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1

    :cond_0
    new-instance v0, Ljava/io/IOException;

    const-string v1, "closed"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public flush()V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o0:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget v2, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O0:I

    iget-object v3, v1, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    iget-wide v3, v3, Lokio/o00oOo00;->o00oo0O:J

    iget-boolean v5, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    const/4 v6, 0x0

    invoke-virtual/range {v1 .. v6}, Lo0OOo0O/o00oo00O;->o00oOooO(IJZZ)V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x0

    iput-boolean v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1

    :cond_0
    new-instance v0, Ljava/io/IOException;

    const-string v1, "closed"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget-object v0, v0, Lo0OOo0O/o00oo00O;->o00oOo00:Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o0O00O0;->o00oOooO()Lokio/o0O00O0o;

    move-result-object v0

    return-object v0
.end method

.method public o0O0000o(Lokio/o00oOo00;J)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o0:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget-object v0, v0, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    invoke-virtual {v0, p1, p2, p3}, Lokio/o00oOo00;->o0O0000o(Lokio/o00oOo00;J)V

    iget-boolean p1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    const/4 p2, 0x0

    if-eqz p1, :cond_0

    iget-wide v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O:J

    const-wide/16 v2, -0x1

    cmp-long p1, v0, v2

    if-eqz p1, :cond_0

    iget-object p1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget-object p1, p1, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    iget-wide v2, p1, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v4, 0x2000

    sub-long/2addr v0, v4

    cmp-long p1, v2, v0

    if-lez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    move p1, p2

    :goto_0
    iget-object p3, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget-object p3, p3, Lo0OOo0O/o00oo00O;->o00oOo0O:Lokio/o00oOo00;

    invoke-virtual {p3}, Lokio/o00oOo00;->o00oOoOo()J

    move-result-wide v2

    const-wide/16 v0, 0x0

    cmp-long p3, v2, v0

    if-lez p3, :cond_1

    if-nez p1, :cond_1

    iget-object p1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    monitor-enter p1

    :try_start_0
    iget-object v0, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0o:Lo0OOo0O/o00oo00O;

    iget v1, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0O0:I

    iget-boolean v4, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    const/4 v5, 0x0

    invoke-virtual/range {v0 .. v5}, Lo0OOo0O/o00oo00O;->o00oOooO(IJZZ)V

    monitor-exit p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iput-boolean p2, p0, Lo0OOo0O/o00oo00O$o00oOOo0;->o00oo0Oo:Z

    goto :goto_1

    :catchall_0
    move-exception p2

    :try_start_1
    monitor-exit p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p2

    :cond_1
    :goto_1
    return-void

    :cond_2
    new-instance p1, Ljava/io/IOException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
