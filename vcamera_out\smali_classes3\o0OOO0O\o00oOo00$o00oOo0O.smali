.class public final Lo0OOo0O/o00oOo00$o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oOo00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o00oOo0O"
.end annotation


# instance fields
.field public final o00oOOo0:I

.field public final o00oOOoO:Lokio/o00oo00O;

.field public final o00oOo00:J


# direct methods
.method public constructor <init>(ILokio/o00oo00O;J)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOOo0:I

    iput-object p2, p0, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOOoO:Lokio/o00oo00O;

    iput-wide p3, p0, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOo00:J

    return-void
.end method
