.class public abstract Lo0OOo00/o00oo0OO$o00oo0O0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0OO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "o00oo0O0"
.end annotation


# static fields
.field public static final o00oOOo0:Lo0OOo00/o00oo0OO$o00oo0O0;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOo00/o00oo0OO$o00oo0O0$o00oOOo0;

    invoke-direct {v0}, Lo0OOo00/o00oo0OO$o00oo0O0$o00oOOo0;-><init>()V

    sput-object v0, Lo0OOo00/o00oo0OO$o00oo0O0;->o00oOOo0:Lo0OOo00/o00oo0OO$o00oo0O0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOo0O(Lo0OOo00/o00oo0OO;)V
    .locals 0

    return-void
.end method

.method public abstract o00oOo0o(Lo0OOo00/o00oo0O;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
