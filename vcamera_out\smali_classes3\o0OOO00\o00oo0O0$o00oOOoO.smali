.class public interface abstract Lo0OOo00/o00oo0O0$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0O0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "o00oOOoO"
.end annotation


# virtual methods
.method public abstract o00oOOo0(ILo0OOo00/o00oOo00;Lokio/o00oo00O;)V
.end method

.method public abstract o00oOOoO(ZLo0OOo00/o0;)V
.end method

.method public abstract o00oOo00(ZIILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZII",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract o00oOo0O(ILjava/lang/String;Lokio/o00oo00O;Ljava/lang/String;IJ)V
.end method

.method public abstract o00oOo0o(IILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract o00oOoO(ZILokio/o00oOoO;I)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract o00oOoO0()V
.end method

.method public abstract o00oOoOO(ZII)V
.end method

.method public abstract o00oOoOo(IIIZ)V
.end method

.method public abstract o00oOoo0(ILo0OOo00/o00oOo00;)V
.end method

.method public abstract o00oOooO(IJ)V
.end method
