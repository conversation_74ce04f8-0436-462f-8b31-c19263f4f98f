.class public final Lo0OOO0O/o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a@\u0010\u0006\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0000*\u0018\u0008\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u00012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0002H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001aT\u0010\u000b\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0008\"\u0004\u0008\u0001\u0010\u0000*\u001e\u0008\u0001\u0012\u0004\u0012\u00028\u0000\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00010\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\t2\u0006\u0010\n\u001a\u00028\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00010\u0002H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a@\u0010\r\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0000*\u0018\u0008\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u00012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0002H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\r\u0010\u0007\u001aT\u0010\u000e\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0008\"\u0004\u0008\u0001\u0010\u0000*\u001e\u0008\u0001\u0012\u0004\u0012\u00028\u0000\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00010\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\t2\u0006\u0010\n\u001a\u00028\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00010\u0002H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u000e\u0010\u000c\u001a9\u0010\u0010\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u001a\u0010\u000f\u001a\u0016\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0001H\u0082\u0008\u001a[\u0010\u0013\u001a\u0004\u0018\u00010\u0003\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0008*\u0008\u0012\u0004\u0012\u00028\u00000\u00112\u0006\u0010\n\u001a\u00028\u00012\'\u0010\u000f\u001a#\u0008\u0001\u0012\u0004\u0012\u00028\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\t\u00a2\u0006\u0002\u0008\u0012H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0013\u0010\u0014\u001a[\u0010\u0015\u001a\u0004\u0018\u00010\u0003\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0008*\u0008\u0012\u0004\u0012\u00028\u00000\u00112\u0006\u0010\n\u001a\u00028\u00012\'\u0010\u000f\u001a#\u0008\u0001\u0012\u0004\u0012\u00028\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\t\u00a2\u0006\u0002\u0008\u0012H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0015\u0010\u0014\u001a?\u0010\u001b\u001a\u0004\u0018\u00010\u0003\"\u0004\u0008\u0000\u0010\u0000*\u0008\u0012\u0004\u0012\u00028\u00000\u00112\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00012\u000e\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0019H\u0082\u0008\u0082\u0002\u0004\n\u0002\u0008\u0019\u00a8\u0006\u001c"
    }
    d2 = {
        "T",
        "Lkotlin/Function1;",
        "Lkotlin/coroutines/o00oOo0O;",
        "",
        "completion",
        "Lo0O0oooo/oO0O00o0;",
        "o00oOo00",
        "(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)V",
        "R",
        "Lkotlin/Function2;",
        "receiver",
        "o00oOooO",
        "(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;)V",
        "o00oOOo0",
        "o00oOOoO",
        "block",
        "o00oOo0O",
        "Lkotlinx/coroutines/internal/o0O0OO;",
        "Lo0O0oooo/o0OO0;",
        "o00oOo0o",
        "(Lkotlinx/coroutines/internal/o0O0OO;Ljava/lang/Object;Lo0OO0Ooo/o0O0000O;)Ljava/lang/Object;",
        "o00oOoO0",
        "",
        "",
        "shouldThrow",
        "Lkotlin/Function0;",
        "startBlock",
        "o00oOoO",
        "kotlinx-coroutines-core"
    }
    k = 0x2
    mv = {
        0x1,
        0x6,
        0x0
    }
.end annotation


# direct methods
.method public static final o00oOOo0(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)V
    .locals 3
    .param p0    # Lo0OO0Ooo/o00ooO0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;)V"
        }
    .end annotation

    const-string v0, "completion"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    invoke-interface {p1}, Lkotlin/coroutines/o00oOo0O;->getContext()Lkotlin/coroutines/o00oo0;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOo00(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v2, 0x1

    :try_start_1
    invoke-static {p0, v2}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lo0OO0Ooo/o00ooO0;

    invoke-interface {p0, p1}, Lo0OO0Ooo/o00ooO0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOOo0(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    sget-object v0, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-eq p0, v0, :cond_0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    goto :goto_0

    :catchall_0
    move-exception p0

    :try_start_3
    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOOo0(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)V

    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_1
    move-exception p0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p0}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p0

    :goto_0
    invoke-static {p0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public static final o00oOOoO(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;)V
    .locals 3
    .param p0    # Lo0OO0Ooo/o0O0000O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o0O0000O<",
            "-TR;-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;)V"
        }
    .end annotation

    const-string v0, "completion"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    invoke-interface {p2}, Lkotlin/coroutines/o00oOo0O;->getContext()Lkotlin/coroutines/o00oo0;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOo00(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v2, 0x2

    :try_start_1
    invoke-static {p0, v2}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lo0OO0Ooo/o0O0000O;

    invoke-interface {p0, p1, p2}, Lo0OO0Ooo/o0O0000O;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOOo0(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    sget-object p1, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-eq p0, p1, :cond_0

    sget-object p1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    goto :goto_0

    :catchall_0
    move-exception p0

    :try_start_3
    invoke-static {v0, v1}, Lkotlinx/coroutines/internal/o0O0o0;->o00oOOo0(Lkotlin/coroutines/o00oo0;Ljava/lang/Object;)V

    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_1
    move-exception p0

    sget-object p1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p0}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p0

    :goto_0
    invoke-static {p0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {p2, p0}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public static final o00oOo00(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)V
    .locals 1
    .param p0    # Lo0OO0Ooo/o00ooO0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;)V"
        }
    .end annotation

    const-string v0, "completion"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x1

    :try_start_0
    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lo0OO0Ooo/o00ooO0;

    invoke-interface {p0, p1}, Lo0OO0Ooo/o00ooO0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    sget-object v0, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-eq p0, v0, :cond_0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    goto :goto_0

    :catchall_0
    move-exception p0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p0}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p0

    :goto_0
    invoke-static {p0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public static final o00oOo0O(Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00ooO0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    const-string v0, "completion"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    :try_start_0
    invoke-interface {p1, p0}, Lo0OO0Ooo/o00ooO0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    sget-object v0, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-eq p1, v0, :cond_0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p1}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {p0, p1}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p1}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {p0, p1}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    return-void
.end method

.method public static final o00oOo0o(Lkotlinx/coroutines/internal/o0O0OO;Ljava/lang/Object;Lo0OO0Ooo/o0O0000O;)Ljava/lang/Object;
    .locals 3
    .param p0    # Lkotlinx/coroutines/internal/o0O0OO;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lo0OO0Ooo/o0O0000O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            "R:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlinx/coroutines/internal/o0O0OO<",
            "-TT;>;TR;",
            "Lo0OO0Ooo/o0O0000O<",
            "-TR;-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    const/4 v0, 0x2

    :try_start_0
    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lo0OO0Ooo/o0O0000O;

    invoke-interface {p2, p1, p0}, Lo0OO0Ooo/o0O0000O;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    new-instance p2, Lkotlinx/coroutines/o0O00oO0;

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-direct {p2, p1, v1, v0, v2}, Lkotlinx/coroutines/o0O00oO0;-><init>(Ljava/lang/Throwable;ZILkotlin/jvm/internal/o0O00;)V

    move-object p1, p2

    :goto_0
    sget-object p2, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-ne p1, p2, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p0, p1}, Lkotlinx/coroutines/oO000o00;->o0O0Oo0o(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    sget-object p1, Lkotlinx/coroutines/oO0O0OoO;->o00oOOoO:Lkotlinx/coroutines/internal/o0OooO0;

    if-ne p0, p1, :cond_1

    goto :goto_1

    :cond_1
    instance-of p1, p0, Lkotlinx/coroutines/o0O00oO0;

    if-nez p1, :cond_2

    invoke-static {p0}, Lkotlinx/coroutines/oO0O0OoO;->o00oo0OO(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    :goto_1
    return-object p2

    :cond_2
    check-cast p0, Lkotlinx/coroutines/o0O00oO0;

    iget-object p0, p0, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    throw p0
.end method

.method public static final o00oOoO(Lkotlinx/coroutines/internal/o0O0OO;Lo0OO0Ooo/o00ooO0;Lo0OO0Ooo/o00oOOoO;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlinx/coroutines/internal/o0O0OO<",
            "-TT;>;",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Ljava/lang/Throwable;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lo0OO0Ooo/o00oOOoO<",
            "+",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    :try_start_0
    invoke-interface {p2}, Lo0OO0Ooo/o00oOOoO;->invoke()Ljava/lang/Object;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p2

    new-instance v0, Lkotlinx/coroutines/o0O00oO0;

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-direct {v0, p2, v3, v1, v2}, Lkotlinx/coroutines/o0O00oO0;-><init>(Ljava/lang/Throwable;ZILkotlin/jvm/internal/o0O00;)V

    move-object p2, v0

    :goto_0
    sget-object v0, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-ne p2, v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0, p2}, Lkotlinx/coroutines/oO000o00;->o0O0Oo0o(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    sget-object v1, Lkotlinx/coroutines/oO0O0OoO;->o00oOOoO:Lkotlinx/coroutines/internal/o0OooO0;

    if-ne p0, v1, :cond_1

    return-object v0

    :cond_1
    instance-of v0, p0, Lkotlinx/coroutines/o0O00oO0;

    if-eqz v0, :cond_4

    check-cast p0, Lkotlinx/coroutines/o0O00oO0;

    iget-object v0, p0, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    invoke-interface {p1, v0}, Lo0OO0Ooo/o00ooO0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-nez p1, :cond_3

    instance-of p0, p2, Lkotlinx/coroutines/o0O00oO0;

    if-nez p0, :cond_2

    goto :goto_1

    :cond_2
    check-cast p2, Lkotlinx/coroutines/o0O00oO0;

    iget-object p0, p2, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    throw p0

    :cond_3
    iget-object p0, p0, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    throw p0

    :cond_4
    invoke-static {p0}, Lkotlinx/coroutines/oO0O0OoO;->o00oo0OO(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    :goto_1
    return-object p2
.end method

.method public static final o00oOoO0(Lkotlinx/coroutines/internal/o0O0OO;Ljava/lang/Object;Lo0OO0Ooo/o0O0000O;)Ljava/lang/Object;
    .locals 3
    .param p0    # Lkotlinx/coroutines/internal/o0O0OO;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lo0OO0Ooo/o0O0000O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            "R:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlinx/coroutines/internal/o0O0OO<",
            "-TT;>;TR;",
            "Lo0OO0Ooo/o0O0000O<",
            "-TR;-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    const/4 v0, 0x0

    const/4 v1, 0x2

    :try_start_0
    invoke-static {p2, v1}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lo0OO0Ooo/o0O0000O;

    invoke-interface {p2, p1, p0}, Lo0OO0Ooo/o0O0000O;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    new-instance p2, Lkotlinx/coroutines/o0O00oO0;

    const/4 v2, 0x0

    invoke-direct {p2, p1, v0, v1, v2}, Lkotlinx/coroutines/o0O00oO0;-><init>(Ljava/lang/Throwable;ZILkotlin/jvm/internal/o0O00;)V

    move-object p1, p2

    :goto_0
    sget-object p2, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-ne p1, p2, :cond_0

    goto :goto_2

    :cond_0
    invoke-virtual {p0, p1}, Lkotlinx/coroutines/oO000o00;->o0O0Oo0o(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    sget-object v2, Lkotlinx/coroutines/oO0O0OoO;->o00oOOoO:Lkotlinx/coroutines/internal/o0OooO0;

    if-ne v1, v2, :cond_1

    goto :goto_2

    :cond_1
    instance-of p2, v1, Lkotlinx/coroutines/o0O00oO0;

    if-eqz p2, :cond_6

    check-cast v1, Lkotlinx/coroutines/o0O00oO0;

    iget-object p2, v1, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    instance-of v1, p2, Lkotlinx/coroutines/oO0O00;

    if-eqz v1, :cond_2

    move-object v1, p2

    check-cast v1, Lkotlinx/coroutines/oO0O00;

    iget-object v1, v1, Lkotlinx/coroutines/oO0O00;->coroutine:Lkotlinx/coroutines/oO000O;

    if-eq v1, p0, :cond_3

    :cond_2
    const/4 v0, 0x1

    :cond_3
    if-nez v0, :cond_5

    instance-of p0, p1, Lkotlinx/coroutines/o0O00oO0;

    if-nez p0, :cond_4

    goto :goto_1

    :cond_4
    check-cast p1, Lkotlinx/coroutines/o0O00oO0;

    iget-object p0, p1, Lkotlinx/coroutines/o0O00oO0;->o00oOOo0:Ljava/lang/Throwable;

    throw p0

    :cond_5
    throw p2

    :cond_6
    invoke-static {v1}, Lkotlinx/coroutines/oO0O0OoO;->o00oo0OO(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    :goto_1
    move-object p2, p1

    :goto_2
    return-object p2
.end method

.method public static final o00oOooO(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;)V
    .locals 1
    .param p0    # Lo0OO0Ooo/o0O0000O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o0O0000O<",
            "-TR;-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;)V"
        }
    .end annotation

    const-string v0, "completion"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x2

    :try_start_0
    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0OOO0OO;->o00oo0O(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lo0OO0Ooo/o0O0000O;

    invoke-interface {p0, p1, p2}, Lo0OO0Ooo/o0O0000O;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    sget-object p1, Lkotlin/coroutines/intrinsics/o00oOOo0;->COROUTINE_SUSPENDED:Lkotlin/coroutines/intrinsics/o00oOOo0;

    if-eq p0, p1, :cond_0

    sget-object p1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    goto :goto_0

    :catchall_0
    move-exception p0

    sget-object p1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p0}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p0

    :goto_0
    invoke-static {p0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {p2, p0}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
