.class public final Lo0OOo0O/o00oOo00;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokhttp3/o0O0oo0o;
.implements Lo0OOo0O/o00oOoO$o00oOOo0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo0O/o00oOo00$o00oOo00;,
        Lo0OOo0O/o00oOo00$o00oo0;,
        Lo0OOo0O/o00oOo00$o00oOo0O;,
        Lo0OOo0O/o00oOo00$o00oOoO;,
        Lo0OOo0O/o00oOo00$o00oo00O;
    }
.end annotation


# static fields
.field public static final o00oo:J = 0xea60L

.field public static final o00oo0oO:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lokhttp3/o0O00O;",
            ">;"
        }
    .end annotation
.end field

.field public static final synthetic o00ooO00:Z = false

.field public static final o0O0o:J = 0x1000000L


# instance fields
.field public final o00oOOo0:Lokhttp3/o0O00OOO;

.field public final o00oOOoO:Lokhttp3/o0O0O0Oo;

.field public final o00oOo00:Ljava/util/Random;

.field public o00oOo0O:Lokhttp3/o00oOoO;

.field public final o00oOo0o:Ljava/lang/Runnable;

.field public o00oOoO:Lo0OOo0O/o00oo00O;

.field public o00oOoO0:Lo0OOo0O/o00oOoO;

.field public o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

.field public o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

.field public final o00oOoo0:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Lokio/o00oo00O;",
            ">;"
        }
    .end annotation
.end field

.field public final o00oOooO:Ljava/lang/String;

.field public final o00oOooo:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public o00oo0:Z

.field public o00oo00O:J

.field public o00oo0O:Ljava/lang/String;

.field public o00oo0O0:I

.field public o00oo0OO:Ljava/util/concurrent/ScheduledFuture;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ScheduledFuture<",
            "*>;"
        }
    .end annotation
.end field

.field public o00oo0Oo:Z

.field public o00oo0o:I

.field public o00oo0o0:I


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    sget-object v0, Lokhttp3/o0O00O;->HTTP_1_1:Lokhttp3/o0O00O;

    invoke-static {v0}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lo0OOo0O/o00oOo00;->o00oo0oO:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lokhttp3/o0O00OOO;Lokhttp3/o0O0O0Oo;Ljava/util/Random;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    iput-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoo0:Ljava/util/ArrayDeque;

    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    iput-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    const/4 v0, -0x1

    iput v0, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I

    iget-object v0, p1, Lokhttp3/o0O00OOO;->o00oOOoO:Ljava/lang/String;

    const-string v1, "GET"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iput-object p1, p0, Lo0OOo0O/o00oOo00;->o00oOOo0:Lokhttp3/o0O00OOO;

    iput-object p2, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    iput-object p3, p0, Lo0OOo0O/o00oOo00;->o00oOo00:Ljava/util/Random;

    const/16 p1, 0x10

    new-array p1, p1, [B

    invoke-virtual {p3, p1}, Ljava/util/Random;->nextBytes([B)V

    invoke-static {p1}, Lokio/o00oo00O;->of([B)Lokio/o00oo00O;

    move-result-object p1

    invoke-virtual {p1}, Lokio/o00oo00O;->base64()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lo0OOo0O/o00oOo00;->o00oOooO:Ljava/lang/String;

    new-instance p1, Lo0OOo0O/o00oOo00$o00oOOo0;

    invoke-direct {p1, p0}, Lo0OOo0O/o00oOo00$o00oOOo0;-><init>(Lo0OOo0O/o00oOo00;)V

    iput-object p1, p0, Lo0OOo0O/o00oOo00;->o00oOo0o:Ljava/lang/Runnable;

    return-void

    :cond_0
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    const-string v0, "Request must be GET: "

    invoke-direct {p3, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object p1, p1, Lokhttp3/o0O00OOO;->o00oOOoO:Ljava/lang/String;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2
.end method


# virtual methods
.method public cancel()V
    .locals 1

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOo0O:Lokhttp3/o00oOoO;

    invoke-interface {v0}, Lokhttp3/o00oOoO;->cancel()V

    return-void
.end method

.method public o00oOOo0()Lokhttp3/o0O00OOO;
    .locals 1

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOo0:Lokhttp3/o0O00OOO;

    return-object v0
.end method

.method public o00oOOoO(Lokio/o00oo00O;)Z
    .locals 1

    if-eqz p1, :cond_0

    const/4 v0, 0x2

    invoke-virtual {p0, p1, v0}, Lo0OOo0O/o00oOo00;->o00oo(Lokio/o00oo00O;I)Z

    move-result p1

    return p1

    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "bytes == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOo00(Ljava/lang/String;)Z
    .locals 1

    if-eqz p1, :cond_0

    invoke-static {p1}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object p1

    const/4 v0, 0x1

    invoke-virtual {p0, p1, v0}, Lo0OOo0O/o00oOo00;->o00oo(Lokio/o00oo00O;I)Z

    move-result p1

    return p1

    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "text == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOo0O(Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v0, p0, p1}, Lokhttp3/o0O0O0Oo;->o00oOooO(Lokhttp3/o0O0oo0o;Ljava/lang/String;)V

    return-void
.end method

.method public declared-synchronized o00oOo0o(Lokio/o00oo00O;)V
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoo0:Ljava/util/ArrayDeque;

    invoke-virtual {v0, p1}, Ljava/util/ArrayDeque;->add(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Lo0OOo0O/o00oOo00;->o0O0o()V

    iget p1, p0, Lo0OOo0O/o00oOo00;->o00oo0o0:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lo0OOo0O/o00oOo00;->o00oo0o0:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :cond_1
    :goto_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized o00oOoO(Lokio/o00oo00O;)V
    .locals 0

    monitor-enter p0

    :try_start_0
    iget p1, p0, Lo0OOo0O/o00oOo00;->o00oo0o:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lo0OOo0O/o00oOo00;->o00oo0o:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized o00oOoO0()J
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-wide v0, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-wide v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public o00oOoOO(ILjava/lang/String;)Z
    .locals 2

    const-wide/32 v0, 0xea60

    invoke-virtual {p0, p1, p2, v0, v1}, Lo0OOo0O/o00oOo00;->o00oo00O(ILjava/lang/String;J)Z

    move-result p1

    return p1
.end method

.method public o00oOoOo(ILjava/lang/String;)V
    .locals 3

    const/4 v0, -0x1

    if-eq p1, v0, :cond_4

    monitor-enter p0

    :try_start_0
    iget v1, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I

    if-ne v1, v0, :cond_3

    iput p1, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I

    iput-object p2, p0, Lo0OOo0O/o00oOo00;->o00oo0O:Ljava/lang/String;

    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    iput-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oo0OO:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v1, :cond_0

    const/4 v2, 0x0

    invoke-interface {v1, v2}, Ljava/util/concurrent/Future;->cancel(Z)Z

    :cond_0
    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    invoke-interface {v1}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    move-object v1, v0

    :cond_1
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v0, p0, p1, p2}, Lokhttp3/o0O0O0Oo;->o00oOOoO(Lokhttp3/o0O0oo0o;ILjava/lang/String;)V

    if-eqz v1, :cond_2

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v0, p0, p1, p2}, Lokhttp3/o0O0O0Oo;->o00oOOo0(Lokhttp3/o0O0oo0o;ILjava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_2
    invoke-static {v1}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    return-void

    :catchall_0
    move-exception p1

    invoke-static {v1}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    throw p1

    :cond_3
    :try_start_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "already closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_1
    move-exception p1

    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1

    :cond_4
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1
.end method

.method public o00oOoo0(ILjava/util/concurrent/TimeUnit;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    int-to-long v1, p1

    invoke-interface {v0, v1, v2, p2}, Ljava/util/concurrent/ExecutorService;->awaitTermination(JLjava/util/concurrent/TimeUnit;)Z

    return-void
.end method

.method public o00oOooO(Lokio/o00oo00O;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v0, p0, p1}, Lokhttp3/o0O0O0Oo;->o00oOo0O(Lokhttp3/o0O0oo0o;Lokio/o00oo00O;)V

    return-void
.end method

.method public o00oOooo(Lokhttp3/o0O00o00;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/net/ProtocolException;
        }
    .end annotation

    iget v0, p1, Lokhttp3/o0O00o00;->o00oo0Oo:I

    const/16 v1, 0x65

    const-string v2, "\'"

    if-ne v0, v1, :cond_3

    const-string v0, "Connection"

    const/4 v1, 0x0

    invoke-virtual {p1, v0, v1}, Lokhttp3/o0O00o00;->o00oo00O(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v3, "Upgrade"

    invoke-virtual {v3, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-virtual {p1, v3, v1}, Lokhttp3/o0O00o00;->o00oo00O(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v3, "websocket"

    invoke-virtual {v3, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_1

    const-string v0, "Sec-WebSocket-Accept"

    invoke-virtual {p1, v0, v1}, Lokhttp3/o0O00o00;->o00oo00O(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOooO:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v0

    invoke-virtual {v0}, Lokio/o00oo00O;->sha1()Lokio/o00oo00O;

    move-result-object v0

    invoke-virtual {v0}, Lokio/o00oo00O;->base64()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    return-void

    :cond_0
    new-instance v1, Ljava/net/ProtocolException;

    new-instance v3, Ljava/lang/StringBuilder;

    const-string v4, "Expected \'Sec-WebSocket-Accept\' header value \'"

    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "\' but was \'"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_1
    new-instance p1, Ljava/net/ProtocolException;

    const-string v1, "Expected \'Upgrade\' header value \'websocket\' but was \'"

    invoke-static {v1, v0, v2}, Landroid/support/v4/media/o00oOo0O;->o00oOOo0(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    new-instance p1, Ljava/net/ProtocolException;

    const-string v1, "Expected \'Connection\' header value \'Upgrade\' but was \'"

    invoke-static {v1, v0, v2}, Landroid/support/v4/media/o00oOo0O;->o00oOOo0(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    new-instance v0, Ljava/net/ProtocolException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v3, "Expected HTTP 101 response but was \'"

    invoke-direct {v1, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v3, p1, Lokhttp3/o0O00o00;->o00oo0Oo:I

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, " "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p1, Lokhttp3/o0O00o00;->o00oo0o0:Ljava/lang/String;

    invoke-static {v1, p1, v2}, Landroid/support/v4/media/o00oOOoO;->o00oOOo0(Ljava/lang/StringBuilder;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final declared-synchronized o00oo(Lokio/o00oo00O;I)Z
    .locals 6

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    const/4 v1, 0x0

    if-nez v0, :cond_2

    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-wide v2, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J

    invoke-virtual {p1}, Lokio/o00oo00O;->size()I

    move-result v0

    int-to-long v4, v0

    add-long/2addr v2, v4

    const-wide/32 v4, 0x1000000

    cmp-long v0, v2, v4

    if-lez v0, :cond_1

    const/16 p1, 0x3e9

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2}, Lo0OOo0O/o00oOo00;->o00oOoOO(ILjava/lang/String;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v1

    :cond_1
    :try_start_1
    iget-wide v0, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J

    invoke-virtual {p1}, Lokio/o00oo00O;->size()I

    move-result v2

    int-to-long v2, v2

    add-long/2addr v0, v2

    iput-wide v0, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    new-instance v1, Lo0OOo0O/o00oOo00$o00oOoO;

    invoke-direct {v1, p2, p1}, Lo0OOo0O/o00oOo00$o00oOoO;-><init>(ILokio/o00oo00O;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayDeque;->add(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Lo0OOo0O/o00oOo00;->o0O0o()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    const/4 p1, 0x1

    return p1

    :cond_2
    :goto_0
    monitor-exit p0

    return v1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o00oo0(Lokhttp3/o0O00O0o;)V
    .locals 5

    invoke-virtual {p1}, Lokhttp3/o0O00O0o;->o00oo()Lokhttp3/o0O00O0o$o00oOOoO;

    move-result-object p1

    sget-object v0, Lo0OOo0O/o00oOo00;->o00oo0oO:Ljava/util/List;

    invoke-virtual {p1, v0}, Lokhttp3/o0O00O0o$o00oOOoO;->o0O0o(Ljava/util/List;)Lokhttp3/o0O00O0o$o00oOOoO;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    new-instance v0, Lokhttp3/o0O00O0o;

    invoke-direct {v0, p1}, Lokhttp3/o0O00O0o;-><init>(Lokhttp3/o0O00O0o$o00oOOoO;)V

    iget p1, v0, Lokhttp3/o0O00O0o;->o00oooOo:I

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOOo0:Lokhttp3/o0O00OOO;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    new-instance v2, Lokhttp3/o0O00OOO$o00oOOo0;

    invoke-direct {v2, v1}, Lokhttp3/o0O00OOO$o00oOOo0;-><init>(Lokhttp3/o0O00OOO;)V

    iget-object v1, v2, Lokhttp3/o0O00OOO$o00oOOo0;->o00oOo00:Lokhttp3/o0O000Oo$o00oOOo0;

    const-string v3, "Upgrade"

    const-string v4, "websocket"

    invoke-virtual {v1, v3, v4}, Lokhttp3/o0O000Oo$o00oOOo0;->o00oOoOO(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/o0O000Oo$o00oOOo0;

    iget-object v1, v2, Lokhttp3/o0O00OOO$o00oOOo0;->o00oOo00:Lokhttp3/o0O000Oo$o00oOOo0;

    const-string v4, "Connection"

    invoke-virtual {v1, v4, v3}, Lokhttp3/o0O000Oo$o00oOOo0;->o00oOoOO(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/o0O000Oo$o00oOOo0;

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOooO:Ljava/lang/String;

    iget-object v3, v2, Lokhttp3/o0O00OOO$o00oOOo0;->o00oOo00:Lokhttp3/o0O000Oo$o00oOOo0;

    const-string v4, "Sec-WebSocket-Key"

    invoke-virtual {v3, v4, v1}, Lokhttp3/o0O000Oo$o00oOOo0;->o00oOoOO(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/o0O000Oo$o00oOOo0;

    iget-object v1, v2, Lokhttp3/o0O00OOO$o00oOOo0;->o00oOo00:Lokhttp3/o0O000Oo$o00oOOo0;

    const-string v3, "Sec-WebSocket-Version"

    const-string v4, "13"

    invoke-virtual {v1, v3, v4}, Lokhttp3/o0O000Oo$o00oOOo0;->o00oOoOO(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/o0O000Oo$o00oOOo0;

    invoke-virtual {v2}, Lokhttp3/o0O00OOO$o00oOOo0;->o00oOOoO()Lokhttp3/o0O00OOO;

    move-result-object v1

    sget-object v2, Lo0OOOoOo/o0;->o00oOOo0:Lo0OOOoOo/o0;

    invoke-virtual {v2, v0, v1}, Lo0OOOoOo/o0;->o00oOoo0(Lokhttp3/o0O00O0o;Lokhttp3/o0O00OOO;)Lokhttp3/o00oOoO;

    move-result-object v0

    iput-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOo0O:Lokhttp3/o00oOoO;

    new-instance v2, Lo0OOo0O/o00oOo00$o00oOOoO;

    invoke-direct {v2, p0, v1, p1}, Lo0OOo0O/o00oOo00$o00oOOoO;-><init>(Lo0OOo0O/o00oOo00;Lokhttp3/o0O00OOO;I)V

    invoke-interface {v0, v2}, Lokhttp3/o00oOoO;->o00oOo0O(Lokhttp3/o00oo00O;)V

    return-void
.end method

.method public declared-synchronized o00oo00O(ILjava/lang/String;J)Z
    .locals 6

    const-string v0, "reason.size() > 123: "

    monitor-enter p0

    :try_start_0
    invoke-static {p1}, Lo0OOo0O/o00oOo0O;->o00oOooO(I)V

    if-eqz p2, :cond_1

    invoke-static {p2}, Lokio/o00oo00O;->encodeUtf8(Ljava/lang/String;)Lokio/o00oo00O;

    move-result-object v1

    invoke-virtual {v1}, Lokio/o00oo00O;->size()I

    move-result v2

    int-to-long v2, v2

    const-wide/16 v4, 0x7b

    cmp-long v2, v2, v4

    if-gtz v2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-virtual {v0, p2}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    const/4 v1, 0x0

    :goto_0
    iget-boolean p2, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    if-nez p2, :cond_3

    iget-boolean p2, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    if-eqz p2, :cond_2

    goto :goto_1

    :cond_2
    const/4 p2, 0x1

    iput-boolean p2, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    new-instance v2, Lo0OOo0O/o00oOo00$o00oOo0O;

    invoke-direct {v2, p1, v1, p3, p4}, Lo0OOo0O/o00oOo00$o00oOo0O;-><init>(ILokio/o00oo00O;J)V

    invoke-virtual {v0, v2}, Ljava/util/ArrayDeque;->add(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Lo0OOo0O/o00oOo00;->o0O0o()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return p2

    :cond_3
    :goto_1
    monitor-exit p0

    const/4 p1, 0x0

    return p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o00oo0O()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    iget v0, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoO0:Lo0OOo0O/o00oOoO;

    invoke-virtual {v0}, Lo0OOo0O/o00oOoO;->o00oOOo0()V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public o00oo0O0(Ljava/lang/String;JLo0OOo0O/o00oOo00$o00oo0;)V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iput-object p4, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    new-instance v0, Lo0OOo0O/o00oo00O;

    iget-boolean v1, p4, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0O0:Z

    iget-object v2, p4, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0Oo:Lokio/o00oOo0O;

    iget-object v3, p0, Lo0OOo0O/o00oOo00;->o00oOo00:Ljava/util/Random;

    invoke-direct {v0, v1, v2, v3}, Lo0OOo0O/o00oo00O;-><init>(ZLokio/o00oOo0O;Ljava/util/Random;)V

    iput-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoO:Lo0OOo0O/o00oo00O;

    new-instance v4, Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lo0OOOoOo/o0O0000O;->o00ooO0(Ljava/lang/String;Z)Ljava/util/concurrent/ThreadFactory;

    move-result-object p1

    const/4 v0, 0x1

    invoke-direct {v4, v0, p1}, Ljava/util/concurrent/ScheduledThreadPoolExecutor;-><init>(ILjava/util/concurrent/ThreadFactory;)V

    iput-object v4, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    const-wide/16 v0, 0x0

    cmp-long p1, p2, v0

    if-eqz p1, :cond_0

    new-instance v5, Lo0OOo0O/o00oOo00$o00oo00O;

    invoke-direct {v5, p0}, Lo0OOo0O/o00oOo00$o00oo00O;-><init>(Lo0OOo0O/o00oOo00;)V

    sget-object v10, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    move-wide v6, p2

    move-wide v8, p2

    invoke-interface/range {v4 .. v10}, Ljava/util/concurrent/ScheduledExecutorService;->scheduleAtFixedRate(Ljava/lang/Runnable;JJLjava/util/concurrent/TimeUnit;)Ljava/util/concurrent/ScheduledFuture;

    :cond_0
    iget-object p1, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    invoke-virtual {p1}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_1

    invoke-virtual {p0}, Lo0OOo0O/o00oOo00;->o0O0o()V

    :cond_1
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    new-instance p1, Lo0OOo0O/o00oOoO;

    iget-boolean p2, p4, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0O0:Z

    iget-object p3, p4, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0O:Lokio/o00oOoO;

    invoke-direct {p1, p2, p3, p0}, Lo0OOo0O/o00oOoO;-><init>(ZLokio/o00oOoO;Lo0OOo0O/o00oOoO$o00oOOo0;)V

    iput-object p1, p0, Lo0OOo0O/o00oOo00;->o00oOoO0:Lo0OOo0O/o00oOoO;

    return-void

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V
    .locals 3

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    if-eqz v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    const/4 v1, 0x0

    iput-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oo0OO:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v1, :cond_1

    const/4 v2, 0x0

    invoke-interface {v1, v2}, Ljava/util/concurrent/Future;->cancel(Z)Z

    :cond_1
    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    if-eqz v1, :cond_2

    invoke-interface {v1}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    :cond_2
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v1, p0, p1, p2}, Lokhttp3/o0O0O0Oo;->o00oOo00(Lokhttp3/o0O0oo0o;Ljava/lang/Throwable;Lokhttp3/o0O00o00;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    invoke-static {v0}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    return-void

    :catchall_0
    move-exception p1

    invoke-static {v0}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    throw p1

    :catchall_1
    move-exception p1

    :try_start_2
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1
.end method

.method public declared-synchronized o00oo0Oo()I
    .locals 1

    monitor-enter p0

    :try_start_0
    iget v0, p0, Lo0OOo0O/o00oOo00;->o00oo0o0:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized o00oo0o()I
    .locals 1

    monitor-enter p0

    :try_start_0
    iget v0, p0, Lo0OOo0O/o00oOo00;->o00oo0o:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized o00oo0o0(Lokio/o00oo00O;)Z
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoo0:Ljava/util/ArrayDeque;

    invoke-virtual {v0, p1}, Ljava/util/ArrayDeque;->add(Ljava/lang/Object;)Z

    invoke-virtual {p0}, Lo0OOo0O/o00oOo00;->o0O0o()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    const/4 p1, 0x1

    return p1

    :cond_1
    :goto_0
    monitor-exit p0

    const/4 p1, 0x0

    return p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public o00oo0oO()Z
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOoO0:Lo0OOo0O/o00oOoO;

    invoke-virtual {v1}, Lo0OOo0O/o00oOoO;->o00oOOo0()V

    iget v1, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    const/4 v0, 0x1

    :cond_0
    return v0

    :catch_0
    move-exception v1

    const/4 v2, 0x0

    invoke-virtual {p0, v1, v2}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    return v0
.end method

.method public o00ooO0()Z
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    monitor-exit p0

    return v1

    :cond_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoO:Lo0OOo0O/o00oo00O;

    iget-object v2, p0, Lo0OOo0O/o00oOo00;->o00oOoo0:Ljava/util/ArrayDeque;

    invoke-virtual {v2}, Ljava/util/ArrayDeque;->poll()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lokio/o00oo00O;

    const/4 v3, 0x0

    const/4 v4, -0x1

    if-nez v2, :cond_4

    iget-object v5, p0, Lo0OOo0O/o00oOo00;->o00oOooo:Ljava/util/ArrayDeque;

    invoke-virtual {v5}, Ljava/util/ArrayDeque;->poll()Ljava/lang/Object;

    move-result-object v5

    instance-of v6, v5, Lo0OOo0O/o00oOo00$o00oOo0O;

    if-eqz v6, :cond_2

    iget v1, p0, Lo0OOo0O/o00oOo00;->o00oo0O0:I

    iget-object v6, p0, Lo0OOo0O/o00oOo00;->o00oo0O:Ljava/lang/String;

    if-eq v1, v4, :cond_1

    iget-object v4, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    iput-object v3, p0, Lo0OOo0O/o00oOo00;->o00oOoOo:Lo0OOo0O/o00oOo00$o00oo0;

    iget-object v3, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    invoke-interface {v3}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    goto :goto_0

    :cond_1
    iget-object v4, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    new-instance v7, Lo0OOo0O/o00oOo00$o00oOo00;

    invoke-direct {v7, p0}, Lo0OOo0O/o00oOo00$o00oOo00;-><init>(Lo0OOo0O/o00oOo00;)V

    move-object v8, v5

    check-cast v8, Lo0OOo0O/o00oOo00$o00oOo0O;

    iget-wide v8, v8, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOo00:J

    sget-object v10, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-interface {v4, v7, v8, v9, v10}, Ljava/util/concurrent/ScheduledExecutorService;->schedule(Ljava/lang/Runnable;JLjava/util/concurrent/TimeUnit;)Ljava/util/concurrent/ScheduledFuture;

    move-result-object v4

    iput-object v4, p0, Lo0OOo0O/o00oOo00;->o00oo0OO:Ljava/util/concurrent/ScheduledFuture;

    move-object v4, v3

    goto :goto_0

    :cond_2
    if-nez v5, :cond_3

    monitor-exit p0

    return v1

    :cond_3
    move-object v6, v3

    move v1, v4

    move-object v4, v6

    :goto_0
    move-object v3, v5

    goto :goto_1

    :cond_4
    move-object v6, v3

    move v1, v4

    move-object v4, v6

    :goto_1
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    if-eqz v2, :cond_5

    :try_start_1
    invoke-virtual {v0, v2}, Lo0OOo0O/o00oo00O;->o00oOo0o(Lokio/o00oo00O;)V

    goto :goto_2

    :cond_5
    instance-of v2, v3, Lo0OOo0O/o00oOo00$o00oOoO;

    if-eqz v2, :cond_6

    move-object v1, v3

    check-cast v1, Lo0OOo0O/o00oOo00$o00oOoO;

    iget-object v1, v1, Lo0OOo0O/o00oOo00$o00oOoO;->o00oOOoO:Lokio/o00oo00O;

    check-cast v3, Lo0OOo0O/o00oOo00$o00oOoO;

    iget v2, v3, Lo0OOo0O/o00oOo00$o00oOoO;->o00oOOo0:I

    invoke-virtual {v1}, Lokio/o00oo00O;->size()I

    move-result v3

    int-to-long v5, v3

    invoke-virtual {v0, v2, v5, v6}, Lo0OOo0O/o00oo00O;->o00oOOo0(IJ)Lokio/o0O00O0;

    move-result-object v0

    invoke-static {v0}, Lokio/o0O00000;->o00oOo00(Lokio/o0O00O0;)Lokio/o00oOo0O;

    move-result-object v0

    invoke-interface {v0, v1}, Lokio/o00oOo0O;->o0oOo0O0(Lokio/o00oo00O;)Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o0O00O0;->close()V

    monitor-enter p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :try_start_2
    iget-wide v2, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J

    invoke-virtual {v1}, Lokio/o00oo00O;->size()I

    move-result v0

    int-to-long v0, v0

    sub-long/2addr v2, v0

    iput-wide v2, p0, Lo0OOo0O/o00oOo00;->o00oo00O:J

    monitor-exit p0

    goto :goto_2

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    throw v0

    :cond_6
    instance-of v2, v3, Lo0OOo0O/o00oOo00$o00oOo0O;

    if-eqz v2, :cond_8

    check-cast v3, Lo0OOo0O/o00oOo00$o00oOo0O;

    iget v2, v3, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOOo0:I

    iget-object v3, v3, Lo0OOo0O/o00oOo00$o00oOo0O;->o00oOOoO:Lokio/o00oo00O;

    invoke-virtual {v0, v2, v3}, Lo0OOo0O/o00oo00O;->o00oOOoO(ILokio/o00oo00O;)V

    if-eqz v4, :cond_7

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOOoO:Lokhttp3/o0O0O0Oo;

    invoke-virtual {v0, p0, v1, v6}, Lokhttp3/o0O0O0Oo;->o00oOOo0(Lokhttp3/o0O0oo0o;ILjava/lang/String;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :cond_7
    :goto_2
    invoke-static {v4}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    const/4 v0, 0x1

    return v0

    :cond_8
    :try_start_4
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    :catchall_1
    move-exception v0

    invoke-static {v4}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    throw v0

    :catchall_2
    move-exception v0

    :try_start_5
    monitor-exit p0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_2

    throw v0
.end method

.method public o00ooO00()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oo0OO:Ljava/util/concurrent/ScheduledFuture;

    if-eqz v0, :cond_0

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/concurrent/Future;->cancel(Z)Z

    :cond_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    invoke-interface {v0}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    const-wide/16 v1, 0xa

    sget-object v3, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-interface {v0, v1, v2, v3}, Ljava/util/concurrent/ExecutorService;->awaitTermination(JLjava/util/concurrent/TimeUnit;)Z

    return-void
.end method

.method public o00ooO0O()V
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOo00;->o00oo0Oo:Z

    if-eqz v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoO:Lo0OOo0O/o00oo00O;

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    sget-object v1, Lokio/o00oo00O;->EMPTY:Lokio/o00oo00O;

    invoke-virtual {v0, v1}, Lo0OOo0O/o00oo00O;->o00oOo0O(Lokio/o00oo00O;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    :goto_0
    return-void

    :catchall_0
    move-exception v0

    :try_start_2
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw v0
.end method

.method public final o0O0o()V
    .locals 2

    iget-object v0, p0, Lo0OOo0O/o00oOo00;->o00oOoOO:Ljava/util/concurrent/ScheduledExecutorService;

    if-eqz v0, :cond_0

    iget-object v1, p0, Lo0OOo0O/o00oOo00;->o00oOo0o:Ljava/lang/Runnable;

    invoke-interface {v0, v1}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    :cond_0
    return-void
.end method
