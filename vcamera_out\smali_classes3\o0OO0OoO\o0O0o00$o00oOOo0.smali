.class public final Lo0OO0oOo/o0O0o00$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OO0oOo/o0O0o00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o00oOOo0"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007J\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007R\u0011\u0010\n\u001a\u00020\u00048F\u00a2\u0006\u0006\u001a\u0004\u0008\u0008\u0010\tR\u001a\u0010\u000b\u001a\u00020\u00048\u0000X\u0081\u0004\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\u000c\u0012\u0004\u0008\r\u0010\u000e\u00a8\u0006\u0010"
    }
    d2 = {
        "Lo0OO0oOo/o0O0o00$o00oOOo0;",
        "",
        "Lo0OO0oOo/oo0OOoo;",
        "type",
        "Lo0OO0oOo/o0O0o00;",
        "o00oOo0O",
        "o00oOOo0",
        "o00oOOoO",
        "o00oOo00",
        "()Lo0OO0oOo/o0O0o00;",
        "STAR",
        "star",
        "Lo0OO0oOo/o0O0o00;",
        "getStar$annotations",
        "()V",
        "<init>",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Lkotlin/jvm/internal/o0O00;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic o00oOooO()V
    .locals 0
    .annotation build Lo0O0oooo/oO0OoOO0;
    .end annotation

    return-void
.end method


# virtual methods
.method public final o00oOOo0(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 2
    .param p1    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "type"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lo0OO0oOo/o0O0o00;

    sget-object v1, Lo0OO0oOo/o0O0o00O;->IN:Lo0OO0oOo/o0O0o00O;

    invoke-direct {v0, v1, p1}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    return-object v0
.end method

.method public final o00oOOoO(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 2
    .param p1    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "type"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lo0OO0oOo/o0O0o00;

    sget-object v1, Lo0OO0oOo/o0O0o00O;->OUT:Lo0OO0oOo/o0O0o00O;

    invoke-direct {v0, v1, p1}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    return-object v0
.end method

.method public final o00oOo00()Lo0OO0oOo/o0O0o00;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lo0OO0oOo/o0O0o00;->o00oOooO:Lo0OO0oOo/o0O0o00;

    return-object v0
.end method

.method public final o00oOo0O(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 2
    .param p1    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "type"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lo0OO0oOo/o0O0o00;

    sget-object v1, Lo0OO0oOo/o0O0o00O;->INVARIANT:Lo0OO0oOo/o0O0o00O;

    invoke-direct {v0, v1, p1}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    return-object v0
.end method
