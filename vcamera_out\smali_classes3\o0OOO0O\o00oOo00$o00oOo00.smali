.class public final Lo0OOo0O/o00oOo00$o00oOo00;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oOo00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "o00oOo00"
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOo0O/o00oOo00;


# direct methods
.method public constructor <init>(Lo0OOo0O/o00oOo00;)V
    .locals 0

    iput-object p1, p0, Lo0OOo0O/o00oOo00$o00oOo00;->o00oo0O0:Lo0OOo0O/o00oOo00;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lo0OOo0O/o00oOo00$o00oOo00;->o00oo0O0:Lo0OOo0O/o00oOo00;

    invoke-virtual {v0}, Lo0OOo0O/o00oOo00;->cancel()V

    return-void
.end method
