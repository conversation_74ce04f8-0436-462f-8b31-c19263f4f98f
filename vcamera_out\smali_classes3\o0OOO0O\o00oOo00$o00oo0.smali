.class public abstract Lo0OOo0O/o00oOo00$o00oo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oOo00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "o00oo0"
.end annotation


# instance fields
.field public final o00oo0O:Lokio/o00oOoO;

.field public final o00oo0O0:Z

.field public final o00oo0Oo:Lokio/o00oOo0O;


# direct methods
.method public constructor <init>(ZLokio/o00oOoO;Lokio/o00oOo0O;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0O0:Z

    iput-object p2, p0, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0O:Lokio/o00oOoO;

    iput-object p3, p0, Lo0OOo0O/o00oOo00$o00oo0;->o00oo0Oo:Lokio/o00oOo0O;

    return-void
.end method
