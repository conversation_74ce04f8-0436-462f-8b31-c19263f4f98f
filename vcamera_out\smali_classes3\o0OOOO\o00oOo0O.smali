.class public final Lo0OOOo/o00oOo0O;
.super Landroidx/lifecycle/o0OO0o00$o00oOo00;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u000b\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\'\u0010\u0006\u001a\u00028\u0000\"\u0008\u0008\u0000\u0010\u0003*\u00020\u00022\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0004H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0014\u0010\u000b\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\n\u00a8\u0006\u000e"
    }
    d2 = {
        "Lo0OOOo/o00oOo0O;",
        "Landroidx/lifecycle/o0OO0o00$o00oOo00;",
        "Landroidx/lifecycle/o0OO0;",
        "T",
        "Ljava/lang/Class;",
        "modelClass",
        "o00oOo00",
        "(Ljava/lang/Class;)Landroidx/lifecycle/o0OO0;",
        "Lo0OOOO00/o00oOo00;",
        "o00oOo0O",
        "Lo0OOOO00/o00oOo00;",
        "repo",
        "<init>",
        "(Lo0OOOO00/o00oOo00;)V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# instance fields
.field public final o00oOo0O:Lo0OOOO00/o00oOo00;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lo0OOOO00/o00oOo00;)V
    .locals 2
    .param p1    # Lo0OOOO00/o00oOo00;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x4

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Landroidx/lifecycle/o0OO0o00$o00oOo00;-><init>()V

    iput-object p1, p0, Lo0OOOo/o00oOo0O;->o00oOo0O:Lo0OOOO00/o00oOo00;

    return-void

    :array_0
    .array-data 1
        -0x26t
        0x0t
        0x15t
        -0x5at
    .end array-data

    :array_1
    .array-data 1
        -0x58t
        0x65t
        0x65t
        -0x37t
        -0x7et
        0x33t
        -0xet
        0xct
    .end array-data
.end method


# virtual methods
.method public o00oOo00(Ljava/lang/Class;)Landroidx/lifecycle/o0OO0;
    .locals 2
    .param p1    # Ljava/lang/Class;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Landroidx/lifecycle/o0OO0;",
            ">(",
            "Ljava/lang/Class<",
            "TT;>;)TT;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const/16 v0, 0xa

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance p1, Lo0OOOo/o00oOoO;

    iget-object v0, p0, Lo0OOOo/o00oOo0O;->o00oOo0O:Lo0OOOO00/o00oOo00;

    invoke-direct {p1, v0}, Lo0OOOo/o00oOoO;-><init>(Lo0OOOO00/o00oOo00;)V

    return-object p1

    nop

    :array_0
    .array-data 1
        -0x3at
        0x53t
        0x2et
        -0x8t
        0x2et
        0x4et
        0x1t
        -0x4ft
        -0x28t
        0x4ft
    .end array-data

    nop

    :array_1
    .array-data 1
        -0x55t
        0x3ct
        0x4at
        -0x63t
        0x42t
        0xdt
        0x6dt
        -0x30t
    .end array-data
.end method
