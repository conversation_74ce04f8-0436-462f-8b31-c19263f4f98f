.class public final Lo0OO0oOo/o0O00oO0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\"\u001f\u0010\u0004\u001a\u0004\u0018\u00010\u0001*\u0006\u0012\u0002\u0008\u00030\u00008\u00c0\u0002X\u0080\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0005"
    }
    d2 = {
        "Lo0OO0oOo/o0oO0Ooo;",
        "",
        "o00oOOo0",
        "(Lo0OO0oOo/o0oO0Ooo;)Ljava/lang/String;",
        "qualifiedOrSimpleName",
        "kotlin-stdlib"
    }
    k = 0x2
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation


# direct methods
.method public static final o00oOOo0(Lo0OO0oOo/o0oO0Ooo;)Ljava/lang/String;
    .locals 1
    .param p0    # Lo0OO0oOo/o0oO0Ooo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lo0OO0oOo/o0oO0Ooo<",
            "*>;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0}, Lo0OO0oOo/o0oO0Ooo;->o00oo0O()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method
