.class public final Lo0OOo0O/o00oOo00$o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo0O/o00oOo00;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o00oOoO"
.end annotation


# instance fields
.field public final o00oOOo0:I

.field public final o00oOOoO:Lokio/o00oo00O;


# direct methods
.method public constructor <init>(ILokio/o00oo00O;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lo0OOo0O/o00oOo00$o00oOoO;->o00oOOo0:I

    iput-object p2, p0, Lo0OOo0O/o00oOo00$o00oOoO;->o00oOOoO:Lokio/o00oo00O;

    return-void
.end method
