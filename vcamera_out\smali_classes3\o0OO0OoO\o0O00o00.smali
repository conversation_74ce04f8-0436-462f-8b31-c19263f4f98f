.class public final Lo0OO0oOo/o0O00o00;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a-\u0010\u0004\u001a\u00028\u0000\"\u0008\u0008\u0000\u0010\u0001*\u00020\u0000*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0000H\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a/\u0010\u0006\u001a\u0004\u0018\u00018\u0000\"\u0008\u0008\u0000\u0010\u0001*\u00020\u0000*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0000H\u0007\u00a2\u0006\u0004\u0008\u0006\u0010\u0005\u00a8\u0006\u0007"
    }
    d2 = {
        "",
        "T",
        "Lo0OO0oOo/o0oO0Ooo;",
        "value",
        "o00oOOo0",
        "(Lo0OO0oOo/o0oO0Ooo;Ljava/lang/Object;)Ljava/lang/Object;",
        "o00oOOoO",
        "kotlin-stdlib"
    }
    k = 0x2
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation

.annotation build Lkotlin/jvm/internal/o0OOO00;
    value = {
        "SMAP\nKClasses.kt\nKotlin\n*S Kotlin\n*F\n+ 1 KClasses.kt\nkotlin/reflect/KClasses\n+ 2 KClassesImpl.kt\nkotlin/reflect/KClassesImplKt\n*L\n1#1,48:1\n9#2:49\n*S KotlinDebug\n*F\n+ 1 KClasses.kt\nkotlin/reflect/KClasses\n*L\n26#1:49\n*E\n"
    }
.end annotation

.annotation build Lo0OOooO0/o0O000;
    name = "KClasses"
.end annotation


# direct methods
.method public static final o00oOOo0(Lo0OO0oOo/o0oO0Ooo;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .param p0    # Lo0OO0oOo/o0oO0Ooo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0oOo/o0oO0Ooo<",
            "TT;>;",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .annotation build Lo0O0oooo/oO000Oo;
        version = "1.4"
    .end annotation

    .annotation build Lo0O0oooo/oO0O0O00;
        markerClass = {
            Lo0O0oooo/o0OO00o0;
        }
    .end annotation

    .annotation build Lo0OoOoO/o0O0000O;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0, p1}, Lo0OO0oOo/o0oO0Ooo;->o00oo0OO(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p0, "null cannot be cast to non-null type T of kotlin.reflect.KClasses.cast"

    invoke-static {p1, p0}, Lkotlin/jvm/internal/o0ooO;->o00oo0(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/ClassCastException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Value cannot be cast to "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-interface {p0}, Lo0OO0oOo/o0oO0Ooo;->o00oo0O()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/ClassCastException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public static final o00oOOoO(Lo0OO0oOo/o0oO0Ooo;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .param p0    # Lo0OO0oOo/o0oO0Ooo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0oOo/o0oO0Ooo<",
            "TT;>;",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .annotation build Lo0O0oooo/oO000Oo;
        version = "1.4"
    .end annotation

    .annotation build Lo0O0oooo/oO0O0O00;
        markerClass = {
            Lo0O0oooo/o0OO00o0;
        }
    .end annotation

    .annotation build Lo0OoOoO/o0O0000O;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0, p1}, Lo0OO0oOo/o0oO0Ooo;->o00oo0OO(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    const-string p0, "null cannot be cast to non-null type T of kotlin.reflect.KClasses.safeCast"

    invoke-static {p1, p0}, Lkotlin/jvm/internal/o0ooO;->o00oo0(Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method
