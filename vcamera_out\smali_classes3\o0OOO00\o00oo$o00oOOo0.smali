.class public final Lo0OOo00/o00oo$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o00oOOo0"
.end annotation


# instance fields
.field public final o00oOOo0:[Lo0OOo00/o00oo$o00oOOo0;

.field public final o00oOOoO:I

.field public final o00oOo00:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x100

    new-array v0, v0, [Lo0OOo00/o00oo$o00oOOo0;

    iput-object v0, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOOo0:[Lo0OOo00/o00oo$o00oOOo0;

    const/4 v0, 0x0

    iput v0, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOOoO:I

    iput v0, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOo00:I

    return-void
.end method

.method public constructor <init>(II)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOOo0:[Lo0OOo00/o00oo$o00oOOo0;

    iput p1, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOOoO:I

    and-int/lit8 p1, p2, 0x7

    if-nez p1, :cond_0

    const/16 p1, 0x8

    :cond_0
    iput p1, p0, Lo0OOo00/o00oo$o00oOOo0;->o00oOo00:I

    return-void
.end method
