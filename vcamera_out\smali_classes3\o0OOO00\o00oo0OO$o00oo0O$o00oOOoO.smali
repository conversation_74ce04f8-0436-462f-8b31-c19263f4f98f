.class public Lo0OOo00/o00oo0OO$o00oo0O$o00oOOoO;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO$o00oo0O;->o00oOOoO(ZLo0OOo00/o0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOoO;->o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 2

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOoO;->o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v1, v0, Lo0OOo00/o00oo0OO;->o00oo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    invoke-virtual {v1, v0}, Lo0OOo00/o00oo0OO$o00oo0O0;->o00oOo0O(Lo0OOo00/o00oo0OO;)V

    return-void
.end method
