.class public final Lo0OOo000/o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lo0OOOooo/o0O00OO;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo000/o00oOOo0$o00oo0;,
        Lo0OOo000/o00oOOo0$o00oOo0O;,
        Lo0OOo000/o00oOOo0$o00oo00O;,
        Lo0OOo000/o00oOOo0$o00oOOoO;,
        Lo0OOo000/o00oOOo0$o00oOo00;,
        Lo0OOo000/o00oOOo0$o00oOoO;
    }
.end annotation


# static fields
.field public static final o00oOoO:I = 0x1

.field public static final o00oOoO0:I = 0x0

.field public static final o00oOoOO:I = 0x2

.field public static final o00oOoOo:I = 0x3

.field public static final o00oOoo0:I = 0x4

.field public static final o00oOooo:I = 0x5

.field public static final o00oo00O:I = 0x6


# instance fields
.field public final o00oOOoO:Lokhttp3/o0O00O0o;

.field public final o00oOo00:Lo0OOOooO/o0OoO00O;

.field public final o00oOo0O:Lokio/o00oOo0O;

.field public o00oOo0o:I

.field public final o00oOooO:Lokio/o00oOoO;


# direct methods
.method public constructor <init>(Lokhttp3/o0O00O0o;Lo0OOOooO/o0OoO00O;Lokio/o00oOoO;Lokio/o00oOo0O;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    iput-object p1, p0, Lo0OOo000/o00oOOo0;->o00oOOoO:Lokhttp3/o0O00O0o;

    iput-object p2, p0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    iput-object p3, p0, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    iput-object p4, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    return-void
.end method


# virtual methods
.method public cancel()V
    .locals 1

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    invoke-virtual {v0}, Lo0OOOooO/o0OoO00O;->o00oOooO()Lo0OOOooO/o0O000Oo;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lo0OOOooO/o0O000Oo;->o00oOoO0()V

    :cond_0
    return-void
.end method

.method public o00oOOo0()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o00oOo0O;->flush()V

    return-void
.end method

.method public o00oOOoO(Lokhttp3/o0O00OOO;J)Lokio/o0O00O0;
    .locals 2

    const-string v0, "Transfer-Encoding"

    invoke-virtual {p1, v0}, Lokhttp3/o0O00OOO;->o00oOo00(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "chunked"

    invoke-virtual {v0, p1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lo0OOo000/o00oOOo0;->o00oOoOo()Lokio/o0O00O0;

    move-result-object p1

    return-object p1

    :cond_0
    const-wide/16 v0, -0x1

    cmp-long p1, p2, v0

    if-eqz p1, :cond_1

    invoke-virtual {p0, p2, p3}, Lo0OOo000/o00oOOo0;->o00oOooo(J)Lokio/o0O00O0;

    move-result-object p1

    return-object p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Cannot stream a request body without chunked encoding or a known content length!"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOo00(Lokhttp3/o0O00OOO;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    invoke-virtual {v0}, Lo0OOOooO/o0OoO00O;->o00oOooO()Lo0OOOooO/o0O000Oo;

    move-result-object v0

    iget-object v0, v0, Lo0OOOooO/o0O000Oo;->o00oOo00:Lokhttp3/o0O0O0O;

    iget-object v0, v0, Lokhttp3/o0O0O0O;->o00oOOoO:Ljava/net/Proxy;

    invoke-virtual {v0}, Ljava/net/Proxy;->type()Ljava/net/Proxy$Type;

    move-result-object v0

    invoke-static {p1, v0}, Lo0OOOooo/o0oO0O0o;->o00oOOo0(Lokhttp3/o0O00OOO;Ljava/net/Proxy$Type;)Ljava/lang/String;

    move-result-object v0

    iget-object p1, p1, Lokhttp3/o0O00OOO;->o00oOo00:Lokhttp3/o0O000Oo;

    invoke-virtual {p0, p1, v0}, Lo0OOo000/o00oOOo0;->o00oo0O0(Lokhttp3/o0O000Oo;Ljava/lang/String;)V

    return-void
.end method

.method public o00oOo0O(Z)Lokhttp3/o0O00o00$o00oOOo0;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "state: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v1, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    :goto_0
    :try_start_0
    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->o0O000oo()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lo0OOOooo/o0O0O0Oo;->o00oOOoO(Ljava/lang/String;)Lo0OOOooo/o0O0O0Oo;

    move-result-object v0

    new-instance v1, Lokhttp3/o0O00o00$o00oOOo0;

    invoke-direct {v1}, Lokhttp3/o0O00o00$o00oOOo0;-><init>()V

    iget-object v2, v0, Lo0OOOooo/o0O0O0Oo;->o00oOOo0:Lokhttp3/o0O00O;

    iput-object v2, v1, Lokhttp3/o0O00o00$o00oOOo0;->o00oOOoO:Lokhttp3/o0O00O;

    iget v2, v0, Lo0OOOooo/o0O0O0Oo;->o00oOOoO:I

    iput v2, v1, Lokhttp3/o0O00o00$o00oOOo0;->o00oOo00:I

    iget-object v2, v0, Lo0OOOooo/o0O0O0Oo;->o00oOo00:Ljava/lang/String;

    iput-object v2, v1, Lokhttp3/o0O00o00$o00oOOo0;->o00oOooO:Ljava/lang/String;

    invoke-virtual {p0}, Lo0OOo000/o00oOOo0;->o00oo0OO()Lokhttp3/o0O000Oo;

    move-result-object v2

    invoke-virtual {v1, v2}, Lokhttp3/o0O00o00$o00oOOo0;->o00oOoOo(Lokhttp3/o0O000Oo;)Lokhttp3/o0O00o00$o00oOOo0;

    move-result-object v1

    if-eqz p1, :cond_2

    iget p1, v0, Lo0OOOooo/o0O0O0Oo;->o00oOOoO:I

    const/16 v0, 0x64

    if-ne p1, v0, :cond_2

    const/4 p1, 0x0

    return-object p1

    :cond_2
    const/4 p1, 0x4

    iput p1, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v1

    :catch_0
    move-exception p1

    new-instance v0, Ljava/io/IOException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "unexpected end of stream on "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v2, p0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, p1}, Ljava/lang/Throwable;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    throw v0
.end method

.method public o00oOo0o()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0}, Lokio/o00oOo0O;->flush()V

    return-void
.end method

.method public final o00oOoO(Lokhttp3/o0O00o00;)Lokio/o0OoO00O;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Lo0OOOooo/o0oO0Ooo;->o00oOo00(Lokhttp3/o0O00o00;)Z

    move-result v0

    if-nez v0, :cond_0

    const-wide/16 v0, 0x0

    invoke-virtual {p0, v0, v1}, Lo0OOo000/o00oOOo0;->o00oo00O(J)Lokio/o0OoO00O;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 v0, 0x0

    const-string v1, "Transfer-Encoding"

    invoke-virtual {p1, v1, v0}, Lokhttp3/o0O00o00;->o00oo00O(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "chunked"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object p1, p1, Lokhttp3/o0O00o00;->o00oo0O0:Lokhttp3/o0O00OOO;

    iget-object p1, p1, Lokhttp3/o0O00OOO;->o00oOOo0:Lokhttp3/o0O000o0;

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0;->o00oOoo0(Lokhttp3/o0O000o0;)Lokio/o0OoO00O;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-static {p1}, Lo0OOOooo/o0oO0Ooo;->o00oOOoO(Lokhttp3/o0O00o00;)J

    move-result-wide v0

    const-wide/16 v2, -0x1

    cmp-long p1, v0, v2

    if-eqz p1, :cond_2

    invoke-virtual {p0, v0, v1}, Lo0OOo000/o00oOOo0;->o00oo00O(J)Lokio/o0OoO00O;

    move-result-object p1

    return-object p1

    :cond_2
    invoke-virtual {p0}, Lo0OOo000/o00oOOo0;->o00oo0()Lokio/o0OoO00O;

    move-result-object p1

    return-object p1
.end method

.method public o00oOoO0(Lokio/o00oo0O;)V
    .locals 2

    invoke-virtual {p1}, Lokio/o00oo0O;->o00oOoo0()Lokio/o0O00O0o;

    move-result-object v0

    sget-object v1, Lokio/o0O00O0o;->o00oOooO:Lokio/o0O00O0o;

    invoke-virtual {p1, v1}, Lokio/o00oo0O;->o00oOooo(Lokio/o0O00O0o;)Lokio/o00oo0O;

    invoke-virtual {v0}, Lokio/o0O00O0o;->o00oOOo0()Lokio/o0O00O0o;

    invoke-virtual {v0}, Lokio/o0O00O0o;->o00oOOoO()Lokio/o0O00O0o;

    return-void
.end method

.method public o00oOoOO()Z
    .locals 2

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x6

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public o00oOoOo()Lokio/o0O00O0;
    .locals 3

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x2

    iput v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    new-instance v0, Lo0OOo000/o00oOOo0$o00oOo00;

    invoke-direct {v0, p0}, Lo0OOo000/o00oOOo0$o00oOo00;-><init>(Lo0OOo000/o00oOOo0;)V

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "state: "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v2, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public o00oOoo0(Lokhttp3/o0O000o0;)Lokio/o0OoO00O;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const/4 v0, 0x5

    iput v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    new-instance v0, Lo0OOo000/o00oOOo0$o00oOo0O;

    invoke-direct {v0, p0, p1}, Lo0OOo000/o00oOOo0$o00oOo0O;-><init>(Lo0OOo000/o00oOOo0;Lokhttp3/o0O000o0;)V

    return-object v0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "state: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v1, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOooO(Lokhttp3/o0O00o00;)Lokhttp3/o0O00oO0;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0;->o00oOoO(Lokhttp3/o0O00o00;)Lokio/o0OoO00O;

    move-result-object v0

    new-instance v1, Lo0OOOooo/o0O0O0O;

    iget-object p1, p1, Lokhttp3/o0O00o00;->o00oo0oO:Lokhttp3/o0O000Oo;

    invoke-static {v0}, Lokio/o0O00000;->o00oOooO(Lokio/o0OoO00O;)Lokio/o00oOoO;

    move-result-object v0

    invoke-direct {v1, p1, v0}, Lo0OOOooo/o0O0O0O;-><init>(Lokhttp3/o0O000Oo;Lokio/o00oOoO;)V

    return-object v1
.end method

.method public o00oOooo(J)Lokio/o0O00O0;
    .locals 2

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x2

    iput v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    new-instance v0, Lo0OOo000/o00oOOo0$o00oOoO;

    invoke-direct {v0, p0, p1, p2}, Lo0OOo000/o00oOOo0$o00oOoO;-><init>(Lo0OOo000/o00oOOo0;J)V

    return-object v0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance p2, Ljava/lang/StringBuilder;

    const-string v0, "state: "

    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oo0()Lokio/o0OoO00O;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x4

    if-ne v0, v1, :cond_1

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    if-eqz v0, :cond_0

    const/4 v1, 0x5

    iput v1, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v0}, Lo0OOOooO/o0OoO00O;->o00oOoOo()V

    new-instance v0, Lo0OOo000/o00oOOo0$o00oo0;

    invoke-direct {v0, p0}, Lo0OOo000/o00oOOo0$o00oo0;-><init>(Lo0OOo000/o00oOOo0;)V

    return-object v0

    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "streamAllocation == null"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "state: "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v2, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public o00oo00O(J)Lokio/o0OoO00O;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const/4 v0, 0x5

    iput v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    new-instance v0, Lo0OOo000/o00oOOo0$o00oo00O;

    invoke-direct {v0, p0, p1, p2}, Lo0OOo000/o00oOOo0$o00oo00O;-><init>(Lo0OOo000/o00oOOo0;J)V

    return-object v0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance p2, Ljava/lang/StringBuilder;

    const-string v0, "state: "

    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oo0O0(Lokhttp3/o0O000Oo;Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    if-nez v0, :cond_1

    iget-object v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {v0, p2}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    move-result-object p2

    const-string v0, "\r\n"

    invoke-interface {p2, v0}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    iget-object p2, p1, Lokhttp3/o0O000Oo;->o00oOOo0:[Ljava/lang/String;

    array-length p2, p2

    div-int/lit8 p2, p2, 0x2

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p2, :cond_0

    iget-object v2, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-virtual {p1, v1}, Lokhttp3/o0O000Oo;->o00oOooO(I)Ljava/lang/String;

    move-result-object v3

    invoke-interface {v2, v3}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    move-result-object v2

    const-string v3, ": "

    invoke-interface {v2, v3}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    move-result-object v2

    invoke-virtual {p1, v1}, Lokhttp3/o0O000Oo;->o00oOoo0(I)Ljava/lang/String;

    move-result-object v3

    invoke-interface {v2, v3}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    move-result-object v2

    invoke-interface {v2, v0}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lo0OOo000/o00oOOo0;->o00oOo0O:Lokio/o00oOo0O;

    invoke-interface {p1, v0}, Lokio/o00oOo0O;->o00oooo(Ljava/lang/String;)Lokio/o00oOo0O;

    const/4 p1, 0x1

    iput p1, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    return-void

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance p2, Ljava/lang/StringBuilder;

    const-string v0, "state: "

    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v0, p0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oo0OO()Lokhttp3/o0O000Oo;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lokhttp3/o0O000Oo$o00oOOo0;

    invoke-direct {v0}, Lokhttp3/o0O000Oo$o00oOOo0;-><init>()V

    :goto_0
    iget-object v1, p0, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v1}, Lokio/o00oOoO;->o0O000oo()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    if-eqz v2, :cond_0

    sget-object v2, Lo0OOOoOo/o0;->o00oOOo0:Lo0OOOoOo/o0;

    invoke-virtual {v2, v0, v1}, Lo0OOOoOo/o0;->o00oOOo0(Lokhttp3/o0O000Oo$o00oOOo0;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    new-instance v1, Lokhttp3/o0O000Oo;

    invoke-direct {v1, v0}, Lokhttp3/o0O000Oo;-><init>(Lokhttp3/o0O000Oo$o00oOOo0;)V

    return-object v1
.end method
