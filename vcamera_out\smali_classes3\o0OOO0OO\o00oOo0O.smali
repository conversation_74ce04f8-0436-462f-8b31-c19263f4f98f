.class public Lo0OOO0Oo/o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static o00oOOo0:Lo0OOO0Oo/o00oOo0O;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOO0Oo/o00oOo0O;

    invoke-direct {v0}, Lo0OOO0Oo/o00oOo0O;-><init>()V

    sput-object v0, Lo0OOO0Oo/o00oOo0O;->o00oOOo0:Lo0OOO0Oo/o00oOo0O;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Landroid/content/Context;)V
    .locals 1

    new-instance v0, Lo0OOO0Oo/o00oOo0O$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oOo0O$o00oOOo0;-><init>(Lo0OOO0Oo/o00oOo0O;)V

    invoke-static {p1, v0}, Lcom/google/android/gms/ads/MobileAds;->initialize(Landroid/content/Context;Lcom/google/android/gms/ads/initialization/OnInitializationCompleteListener;)V

    return-void
.end method
