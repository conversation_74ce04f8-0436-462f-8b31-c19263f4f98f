.class public Lo0OOo00/o00oo0OO$o00oo0;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO;->o00oooOo(ILo0OOo00/o00oOo00;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:I

.field public final synthetic o00oo0Oo:Lo0OOo00/o00oOo00;

.field public final synthetic o00oo0o0:Lo0OOo00/o00oo0OO;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ILo0OOo00/o00oOo00;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iput p4, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0O:I

    iput-object p5, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0Oo:Lo0OOo00/o00oOo00;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 3

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00ooO0:Lo0OOo00/o00ooO;

    iget v1, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0O:I

    iget-object v2, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0Oo:Lo0OOo00/o00oOo00;

    invoke-interface {v0, v1, v2}, Lo0OOo00/o00ooO;->o00oOOo0(ILo0OOo00/o00oOo00;)V

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0o0:Lo0OOo00/o00oo0OO;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0o0:Lo0OOo00/o00oo0OO;

    iget-object v1, v1, Lo0OOo00/o00oo0OO;->o00ooOoo:Ljava/util/Set;

    iget v2, p0, Lo0OOo00/o00oo0OO$o00oo0;->o00oo0O:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method
