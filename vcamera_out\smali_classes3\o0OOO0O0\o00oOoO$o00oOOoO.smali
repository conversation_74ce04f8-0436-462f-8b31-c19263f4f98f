.class public Lo0OOO0o0/o00oOoO$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/app/Application$ActivityLifecycleCallbacks;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0o0/o00oOoO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oOOoO"
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOO0o0/o00oOoO;


# direct methods
.method public constructor <init>(Lo0OOO0o0/o00oOoO;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0o0/o00oOoO$o00oOOoO;->o00oo0O0:Lo0OOO0o0/o00oOoO;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lo0OOO0o0/o00oOoO;Lo0OOO0o0/o00oOoO$o00oOOo0;)V
    .locals 0

    invoke-direct {p0, p1}, Lo0OOO0o0/o00oOoO$o00oOOoO;-><init>(Lo0OOO0o0/o00oOoO;)V

    return-void
.end method


# virtual methods
.method public onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param
    .param p2    # Landroid/os/Bundle;
        .annotation build Lo00oOo00/o0O0OOOo;
        .end annotation
    .end param

    return-void
.end method

.method public onActivityDestroyed(Landroid/app/Activity;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    return-void
.end method

.method public onActivityPaused(Landroid/app/Activity;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    return-void
.end method

.method public onActivityResumed(Landroid/app/Activity;)V
    .locals 3
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    sput-object p1, Lo0OOO0o0/o00oOoO;->o00oOOoO:Landroid/app/Activity;

    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOo00()Z

    move-result v0

    if-eqz v0, :cond_1

    sget-boolean v0, Lo0OOO0o0/o00oOoO;->o00oOo00:Z

    if-eqz v0, :cond_1

    :try_start_0
    invoke-static {}, Lmultispace/multiapp/clone/fc/o00oo00O;->o00oOOo0()Lmultispace/multiapp/clone/fc/o00oOoO;

    move-result-object v0

    iget-object v0, v0, Lmultispace/multiapp/clone/fc/o00oOoO;->o00oo0O:Lmultispace/multiapp/clone/fc/o00oo0;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/fc/o00oo0;->o00oOo00()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    :try_start_1
    invoke-static {}, Lmultispace/multiapp/clone/fc/o00oo00O;->o00oOOo0()Lmultispace/multiapp/clone/fc/o00oOoO;

    move-result-object v0

    invoke-virtual {v0}, Lmultispace/multiapp/clone/fc/o00oOoO;->o00ooOO()Lmultispace/multiapp/clone/fc/o0O0O0o0;

    move-result-object v0

    if-eqz v0, :cond_0

    sget-object v0, Lmultispace/multiapp/clone/fc/o00oo00O;->o00oOOo0:Lmultispace/multiapp/clone/fc/o00oOoO;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/fc/o00oOoO;->o00ooOO()Lmultispace/multiapp/clone/fc/o0O0O0o0;

    move-result-object v0

    iget-object v0, v0, Lmultispace/multiapp/clone/fc/o0O0O0o0;->o00oOOo0:Lmultispace/multiapp/clone/fc/o0O0O0o0$o00oOoO;

    if-eqz v0, :cond_0

    sget-object v0, Lmultispace/multiapp/clone/fc/o00oo00O;->o00oOOo0:Lmultispace/multiapp/clone/fc/o00oOoO;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/fc/o00oOoO;->o00ooOO()Lmultispace/multiapp/clone/fc/o0O0O0o0;

    move-result-object v0

    iget-object v0, v0, Lmultispace/multiapp/clone/fc/o0O0O0o0;->o00oOOo0:Lmultispace/multiapp/clone/fc/o0O0O0o0$o00oOoO;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/fc/o0O0O0o0$o00oOoO;->o00oo0O()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :catchall_1
    :cond_0
    new-instance v0, Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;

    invoke-direct {v0, p0, p1}, Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;-><init>(Lo0OOO0o0/o00oOoO$o00oOOoO;Landroid/app/Activity;)V

    const-wide/16 v1, 0x3e8

    invoke-static {v0, v1, v2}, Lmultispace/multiapp/clone/util/o0O00oO0;->o00oOo0o(Ljava/lang/Runnable;J)V

    const/4 p1, 0x0

    sput-boolean p1, Lo0OOO0o0/o00oOoO;->o00oOo00:Z

    :cond_1
    return-void
.end method

.method public onActivitySaveInstanceState(Landroid/app/Activity;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param
    .param p2    # Landroid/os/Bundle;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    return-void
.end method

.method public onActivityStarted(Landroid/app/Activity;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    return-void
.end method

.method public onActivityStopped(Landroid/app/Activity;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    return-void
.end method
