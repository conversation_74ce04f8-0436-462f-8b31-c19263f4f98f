.class public final Lo0OOo00/o00ooO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lo0OOo00/o00ooO;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00ooO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(ILo0OOo00/o00oOo00;)V
    .locals 0

    return-void
.end method

.method public o00oOOoO(ILjava/util/List;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)Z"
        }
    .end annotation

    const/4 p1, 0x1

    return p1
.end method

.method public o00oOo00(ILjava/util/List;Z)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)Z"
        }
    .end annotation

    const/4 p1, 0x1

    return p1
.end method

.method public o00oOooO(ILokio/o00oOoO;IZ)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    int-to-long p3, p3

    invoke-interface {p2, p3, p4}, Lokio/o00oOoO;->skip(J)V

    const/4 p1, 0x1

    return p1
.end method
