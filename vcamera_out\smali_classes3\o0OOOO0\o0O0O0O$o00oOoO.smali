.class public Lo0OOOo0/o0O0O0O$o00oOoO;
.super Lde/robv/android/xposed/XC_MethodHook;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOOo0/o0O0O0O;->o00oOo00(Ljava/lang/ClassLoader;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:[D


# direct methods
.method public constructor <init>([D)V
    .locals 0

    iput-object p1, p0, Lo0OOOo0/o0O0O0O$o00oOoO;->o00oo0O0:[D

    invoke-direct {p0}, Lde/robv/android/xposed/XC_MethodHook;-><init>()V

    return-void
.end method


# virtual methods
.method public afterHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    .locals 20
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    const/16 v3, 0xa

    const/4 v4, 0x6

    const/4 v5, 0x5

    const/16 v6, 0x8

    :try_start_0
    iget-object v0, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    new-instance v7, Lmultispace/multiapp/clone/util/o0O00O;

    invoke-direct {v7, v0}, Lmultispace/multiapp/clone/util/o0O00O;-><init>(Ljava/lang/Object;)V

    const/16 v0, 0xb

    new-array v8, v0, [B

    const/16 v9, 0x2a

    const/4 v10, 0x0

    aput-byte v9, v8, v10

    const/16 v9, 0x3d

    const/4 v11, 0x1

    aput-byte v9, v8, v11

    const/16 v12, 0x2c

    const/4 v13, 0x2

    aput-byte v12, v8, v13

    const/16 v14, -0x66

    const/4 v15, 0x3

    aput-byte v14, v8, v15

    const/16 v14, 0x52

    const/16 v16, 0x4

    aput-byte v14, v8, v16

    const/16 v14, 0x10

    aput-byte v14, v8, v5

    const/16 v14, 0x23

    aput-byte v14, v8, v4

    const/16 v14, -0x6a

    const/16 v17, 0x7

    aput-byte v14, v8, v17

    aput-byte v12, v8, v6

    const/16 v12, 0x3c

    const/16 v14, 0x9

    aput-byte v12, v8, v14

    aput-byte v9, v8, v3

    new-array v9, v6, [B

    const/16 v12, 0x59

    aput-byte v12, v9, v10

    const/16 v12, 0x58

    aput-byte v12, v9, v11

    aput-byte v12, v9, v13

    const/16 v12, -0x2a

    aput-byte v12, v9, v15

    const/16 v12, 0x33

    aput-byte v12, v9, v16

    const/16 v12, 0x64

    aput-byte v12, v9, v5

    const/16 v12, 0x4a

    aput-byte v12, v9, v4

    const/16 v12, -0x1e

    aput-byte v12, v9, v17

    invoke-static {v8, v9}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v8

    new-array v9, v11, [Ljava/lang/Object;

    iget-object v12, v1, Lo0OOOo0/o0O0O0O$o00oOoO;->o00oo0O0:[D

    aget-wide v18, v12, v10

    invoke-static/range {v18 .. v19}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v12

    aput-object v12, v9, v10

    invoke-virtual {v7, v8, v9}, Lmultispace/multiapp/clone/util/o0O00O;->o00oOo0o(Ljava/lang/String;[Ljava/lang/Object;)Lmultispace/multiapp/clone/util/o0O00O;

    iget-object v7, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    new-instance v8, Lmultispace/multiapp/clone/util/o0O00O;

    invoke-direct {v8, v7}, Lmultispace/multiapp/clone/util/o0O00O;-><init>(Ljava/lang/Object;)V

    const/16 v7, 0xc

    new-array v7, v7, [B

    const/16 v9, -0x31

    aput-byte v9, v7, v10

    const/16 v9, -0x6e

    aput-byte v9, v7, v11

    const/16 v12, -0x54

    aput-byte v12, v7, v13

    aput-byte v9, v7, v15

    const/16 v9, -0x50

    aput-byte v9, v7, v16

    const/16 v9, -0x7a

    aput-byte v9, v7, v5

    const/16 v9, -0x15

    aput-byte v9, v7, v4

    const/16 v9, 0x46

    aput-byte v9, v7, v17

    const/16 v9, -0x38

    aput-byte v9, v7, v6

    const/16 v9, -0x7e

    aput-byte v9, v7, v14

    const/16 v9, -0x44

    aput-byte v9, v7, v3

    const/16 v12, -0x45

    aput-byte v12, v7, v0

    new-array v0, v6, [B

    aput-byte v9, v0, v10

    const/16 v9, -0x9

    aput-byte v9, v0, v11

    const/16 v9, -0x28

    aput-byte v9, v0, v13

    const/16 v9, -0x22

    aput-byte v9, v0, v15

    const/16 v9, -0x21

    aput-byte v9, v0, v16

    const/16 v9, -0x18

    aput-byte v9, v0, v5

    const/16 v9, -0x74

    aput-byte v9, v0, v4

    const/16 v9, 0x2f

    aput-byte v9, v0, v17

    invoke-static {v7, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    new-array v7, v11, [Ljava/lang/Object;

    iget-object v9, v1, Lo0OOOo0/o0O0O0O$o00oOoO;->o00oo0O0:[D

    aget-wide v11, v9, v11

    invoke-static {v11, v12}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v9

    aput-object v9, v7, v10

    invoke-virtual {v8, v0, v7}, Lmultispace/multiapp/clone/util/o0O00O;->o00oOo0o(Ljava/lang/String;[Ljava/lang/Object;)Lmultispace/multiapp/clone/util/o0O00O;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    new-array v5, v5, [B

    fill-array-data v5, :array_0

    new-array v7, v6, [B

    fill-array-data v7, :array_1

    invoke-static {v5, v7}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    new-array v3, v3, [B

    fill-array-data v3, :array_2

    new-array v5, v6, [B

    fill-array-data v5, :array_3

    invoke-static {v3, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    new-array v0, v4, [B

    fill-array-data v0, :array_4

    new-array v3, v6, [B

    fill-array-data v3, :array_5

    invoke-static {v0, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object v0, v2, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->thisObject:Ljava/lang/Object;

    invoke-static {v0}, Ljava/util/Objects;->toString(Ljava/lang/Object;)Ljava/lang/String;

    :goto_0
    return-void

    :array_0
    .array-data 1
        -0x6dt
        0x13t
        -0x2ft
        -0x5et
        -0x51t
    .end array-data

    nop

    :array_1
    .array-data 1
        -0x5et
        0x22t
        -0x20t
        -0x6dt
        -0x62t
        0x78t
        0x3ct
        -0x33t
    .end array-data

    :array_2
    .array-data 1
        -0x40t
        0x34t
        -0x27t
        -0x6ft
        -0x2ct
        -0x10t
        -0x73t
        0x51t
        -0x35t
        0x76t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x5bt
        0x4ct
        -0x46t
        -0xct
        -0x5ct
        -0x7ct
        -0x1ct
        0x3et
    .end array-data

    :array_4
    .array-data 1
        -0x1et
        -0x10t
        -0x4et
        -0x6ct
        0x21t
        -0x12t
    .end array-data

    nop

    :array_5
    .array-data 1
        -0x32t
        -0x7ct
        -0x26t
        -0x3t
        0x52t
        -0x2ct
        -0x51t
        0x2t
    .end array-data
.end method
