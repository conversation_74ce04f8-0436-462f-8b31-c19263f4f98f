.class public Lo0OOO0o0/o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoooOO0/o00oOo0O$o00oOOoO;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOO0o0/o00oOoO$o00oOOoO;
    }
.end annotation


# static fields
.field public static o00oOOo0:Landroid/app/Application;

.field public static o00oOOoO:Landroid/app/Activity;

.field public static o00oOo00:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Landroid/app/Application;)V
    .locals 0

    return-void
.end method

.method public o00oOOoO(Landroid/app/Application;)V
    .locals 0

    sput-object p1, Lo0OOO0o0/o00oOoO;->o00oOOo0:Landroid/app/Application;

    return-void
.end method

.method public o00oOo00(Landroid/app/Application;)V
    .locals 1

    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOOo0()Z

    move-result v0

    if-eqz v0, :cond_0

    :try_start_0
    new-instance v0, Lo0OOOo0/o0O00o00;

    invoke-direct {v0}, Lo0OOOo0/o0O00o00;-><init>()V

    invoke-virtual {v0, p1}, Lo0OOOo0/o0oO0Ooo;->o00oOOo0(Landroid/app/Application;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    :try_start_1
    new-instance v0, Lo0OOOo0/o0oO0O0o;

    invoke-direct {v0}, Lo0OOOo0/o0oO0O0o;-><init>()V

    invoke-virtual {v0, p1}, Lo0OOOo0/o0oO0Ooo;->o00oOOo0(Landroid/app/Application;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_1

    :cond_0
    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOo00()Z

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Lo0OOO0o0/o00oOoO$o00oOOoO;

    invoke-direct {v0, p0}, Lo0OOO0o0/o00oOoO$o00oOOoO;-><init>(Lo0OOO0o0/o00oOoO;)V

    invoke-virtual {p1, v0}, Landroid/app/Application;->registerActivityLifecycleCallbacks(Landroid/app/Application$ActivityLifecycleCallbacks;)V

    invoke-static {}, Lmultispace/multiapp/clone/fc/o00oo00O;->o00oOo00()V

    :try_start_2
    new-instance v0, Lo0OOOo0/o0O00o00;

    invoke-direct {v0}, Lo0OOOo0/o0O00o00;-><init>()V

    invoke-virtual {v0, p1}, Lo0OOOo0/o0oO0Ooo;->o00oOOo0(Landroid/app/Application;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    goto :goto_1

    :catchall_1
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :cond_1
    :goto_1
    invoke-static {}, Lo0OOOO0o/o00oOo00;->o00oOOo0()V

    return-void
.end method

.method public final o00oOo0O(Landroid/app/Application;)Z
    .locals 12

    const/4 v0, 0x1

    const/16 v1, 0x8

    :try_start_0
    new-array v2, v1, [B

    const/16 v3, -0x3a

    const/4 v4, 0x0

    aput-byte v3, v2, v4

    const/16 v3, -0x6d

    aput-byte v3, v2, v0

    const/16 v3, -0xe

    const/4 v5, 0x2

    aput-byte v3, v2, v5

    const/4 v3, 0x3

    aput-byte v3, v2, v3

    const/16 v6, -0x46

    const/4 v7, 0x4

    aput-byte v6, v2, v7

    const/16 v6, 0x61

    const/4 v8, 0x5

    aput-byte v6, v2, v8

    const/16 v6, -0x4a

    const/4 v9, 0x6

    aput-byte v6, v2, v9

    const/16 v6, -0x60

    const/4 v10, 0x7

    aput-byte v6, v2, v10

    new-array v6, v1, [B

    const/16 v11, -0x59

    aput-byte v11, v6, v4

    const/16 v4, -0x10

    aput-byte v4, v6, v0

    const/16 v4, -0x7a

    aput-byte v4, v6, v5

    const/16 v4, 0x6a

    aput-byte v4, v6, v3

    const/16 v3, -0x34

    aput-byte v3, v6, v7

    aput-byte v1, v6, v8

    const/16 v1, -0x3e

    aput-byte v1, v6, v9

    const/16 v1, -0x27

    aput-byte v1, v6, v10

    invoke-static {v2, v6}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/app/ActivityManager;

    invoke-virtual {v1}, Landroid/app/ActivityManager;->getRunningAppProcesses()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/app/ActivityManager$RunningAppProcessInfo;

    iget v3, v2, Landroid/app/ActivityManager$RunningAppProcessInfo;->pid:I

    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v4

    if-ne v3, v4, :cond_0

    iget-object v1, v2, Landroid/app/ActivityManager$RunningAppProcessInfo;->processName:Ljava/lang/String;

    invoke-virtual {p1}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    :catch_0
    :cond_1
    return v0
.end method

.method public o00oOooO(Ljava/lang/Object;Landroid/content/Context;)V
    .locals 0

    return-void
.end method
