.class public final Lo0OOO0oo/o00ooO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0017\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0008\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0004\u0012\u0006\u0010\n\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\t\u0010\u0003\u001a\u00020\u0002H\u00c6\u0003J\t\u0010\u0005\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u0007\u001a\u00020\u0006H\u00c6\u0003J\'\u0010\u000b\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00022\u0008\u0008\u0002\u0010\t\u001a\u00020\u00042\u0008\u0008\u0002\u0010\n\u001a\u00020\u0006H\u00c6\u0001J\t\u0010\u000c\u001a\u00020\u0004H\u00d6\u0001J\t\u0010\r\u001a\u00020\u0002H\u00d6\u0001J\u0013\u0010\u000f\u001a\u00020\u00062\u0008\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003R\u0017\u0010\u0008\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012R\u0017\u0010\t\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015R\"\u0010\n\u001a\u00020\u00068\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0007\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018\"\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001d"
    }
    d2 = {
        "Lo0OOO0oo/o00ooO;",
        "",
        "",
        "o00oOOo0",
        "",
        "o00oOOoO",
        "",
        "o00oOo00",
        "userID",
        "userName",
        "isInstalledGms",
        "o00oOooO",
        "toString",
        "hashCode",
        "other",
        "equals",
        "I",
        "o00oOo0o",
        "()I",
        "Ljava/lang/String;",
        "o00oOoO0",
        "()Ljava/lang/String;",
        "Z",
        "o00oOoO",
        "()Z",
        "o00oOoOO",
        "(Z)V",
        "<init>",
        "(ILjava/lang/String;Z)V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# instance fields
.field public final o00oOOo0:I

.field public final o00oOOoO:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o00oOo00:Z


# direct methods
.method public constructor <init>(ILjava/lang/String;Z)V
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_0

    new-array v0, v0, [B

    fill-array-data v0, :array_1

    invoke-static {v1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    iput-object p2, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    iput-boolean p3, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    return-void

    nop

    :array_0
    .array-data 1
        -0x62t
        -0x76t
        0x59t
        -0x2at
        -0x4bt
        -0x42t
        0x20t
        -0x61t
    .end array-data

    :array_1
    .array-data 1
        -0x15t
        -0x7t
        0x3ct
        -0x5ct
        -0x5t
        -0x21t
        0x4dt
        -0x6t
    .end array-data
.end method

.method public static synthetic o00oOo0O(Lo0OOO0oo/o00ooO;ILjava/lang/String;ZILjava/lang/Object;)Lo0OOO0oo/o00ooO;
    .locals 0

    and-int/lit8 p5, p4, 0x1

    if-eqz p5, :cond_0

    iget p1, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    :cond_0
    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_1

    iget-object p2, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    :cond_1
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_2

    iget-boolean p3, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    :cond_2
    invoke-virtual {p0, p1, p2, p3}, Lo0OOO0oo/o00ooO;->o00oOooO(ILjava/lang/String;Z)Lo0OOO0oo/o00ooO;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lo0OOO0oo/o00ooO;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lo0OOO0oo/o00ooO;

    iget v1, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    iget v3, p1, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    iget-object v3, p1, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/o0ooO;->o00oOoO0(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-boolean v1, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    iget-boolean p1, p1, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    if-eq v1, p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget v0, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    invoke-static {v0}, Ljava/lang/Integer;->hashCode(I)I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    const/16 v2, 0x1f

    invoke-static {v1, v0, v2}, Lo0O0O0o/o0O00O0;->o00oOOo0(Ljava/lang/String;II)I

    move-result v0

    iget-boolean v1, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    :cond_0
    add-int/2addr v0, v1

    return v0
.end method

.method public final o00oOOo0()I
    .locals 1

    iget v0, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    return v0
.end method

.method public final o00oOOoO()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    return-object v0
.end method

.method public final o00oOo00()Z
    .locals 1

    iget-boolean v0, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    return v0
.end method

.method public final o00oOo0o()I
    .locals 1

    iget v0, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    return v0
.end method

.method public final o00oOoO()Z
    .locals 1

    iget-boolean v0, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    return v0
.end method

.method public final o00oOoO0()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    return-object v0
.end method

.method public final o00oOoOO(Z)V
    .locals 0

    iput-boolean p1, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    return-void
.end method

.method public final o00oOooO(ILjava/lang/String;Z)Lo0OOO0oo/o00ooO;
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_0

    new-array v0, v0, [B

    fill-array-data v0, :array_1

    invoke-static {v1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lkotlin/jvm/internal/o0ooO;->o00oo0O0(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Lo0OOO0oo/o00ooO;

    invoke-direct {v0, p1, p2, p3}, Lo0OOO0oo/o00ooO;-><init>(ILjava/lang/String;Z)V

    return-object v0

    nop

    :array_0
    .array-data 1
        -0x44t
        0x43t
        0x6ct
        0x39t
        0x49t
        0xet
        0x34t
        -0x1t
    .end array-data

    :array_1
    .array-data 1
        -0x37t
        0x30t
        0x9t
        0x4bt
        0x7t
        0x6ft
        0x59t
        -0x66t
    .end array-data
.end method

.method public toString()Ljava/lang/String;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v1, 0xf

    new-array v1, v1, [B

    fill-array-data v1, :array_0

    const/16 v2, 0x8

    new-array v3, v2, [B

    fill-array-data v3, :array_1

    invoke-static {v1, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lo0OOO0oo/o00ooO;->o00oOOo0:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v1, 0xb

    new-array v1, v1, [B

    fill-array-data v1, :array_2

    new-array v3, v2, [B

    fill-array-data v3, :array_3

    invoke-static {v1, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lo0OOO0oo/o00ooO;->o00oOOoO:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x11

    new-array v1, v1, [B

    fill-array-data v1, :array_4

    new-array v2, v2, [B

    fill-array-data v2, :array_5

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Lo0OOO0oo/o00ooO;->o00oOo00:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    nop

    :array_0
    .array-data 1
        -0x31t
        -0x1at
        0x7ct
        0x5ft
        0x1dt
        -0x6dt
        0x64t
        0x2at
        -0x3t
        -0x8t
        0x6at
        0x6ft
        0x31t
        -0x4at
        0x37t
    .end array-data

    :array_1
    .array-data 1
        -0x78t
        -0x75t
        0xft
        0x1dt
        0x78t
        -0xet
        0xat
        0x2t
    .end array-data

    :array_2
    .array-data 1
        -0x66t
        0x2ct
        0x6at
        0x41t
        -0x3at
        -0x66t
        -0x12t
        0x40t
        -0x25t
        0x69t
        0x22t
    .end array-data

    :array_3
    .array-data 1
        -0x4at
        0xct
        0x1ft
        0x32t
        -0x5dt
        -0x18t
        -0x60t
        0x21t
    .end array-data

    :array_4
    .array-data 1
        -0x75t
        -0x4et
        0x68t
        0x22t
        -0x43t
        0x68t
        -0x1et
        0x3et
        -0x3at
        -0x2t
        0x6dt
        0x34t
        -0x70t
        0x41t
        -0x4t
        0x39t
        -0x66t
    .end array-data

    nop

    :array_5
    .array-data 1
        -0x59t
        -0x6et
        0x1t
        0x51t
        -0xct
        0x6t
        -0x6ft
        0x4at
    .end array-data
.end method
