.class public Lo0OOo00/o00oo0OO$o00oOo00;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO;->o0O0OO0O(ZIILo0OOo00/o00ooO0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:Z

.field public final synthetic o00oo0Oo:I

.field public final synthetic o00oo0o:Lo0OOo00/o00ooO0;

.field public final synthetic o00oo0o0:I

.field public final synthetic o00oo0oO:Lo0OOo00/o00oo0OO;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO;Ljava/lang/String;[Ljava/lang/Object;ZIILo0OOo00/o00ooO0;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0oO:Lo0OOo00/o00oo0OO;

    iput-boolean p4, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0O:Z

    iput p5, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0Oo:I

    iput p6, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0o0:I

    iput-object p7, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0o:Lo0OOo00/o00ooO0;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 5

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0oO:Lo0OOo00/o00oo0OO;

    iget-boolean v1, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0O:Z

    iget v2, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0Oo:I

    iget v3, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0o0:I

    iget-object v4, p0, Lo0OOo00/o00oo0OO$o00oOo00;->o00oo0o:Lo0OOo00/o00ooO0;

    invoke-virtual {v0, v1, v2, v3, v4}, Lo0OOo00/o00oo0OO;->o0ooO(ZIILo0OOo00/o00ooO0;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method
