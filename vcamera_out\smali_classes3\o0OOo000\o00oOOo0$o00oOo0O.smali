.class public Lo0OOo000/o00oOOo0$o00oOo0O;
.super Lo0OOo000/o00oOOo0$o00oOOoO;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oOo0O"
.end annotation


# static fields
.field public static final o00oo:J = -0x1L


# instance fields
.field public o00oo0o:J

.field public final o00oo0o0:Lokhttp3/o0O000o0;

.field public o00oo0oO:Z

.field public final synthetic o0O0o:Lo0OOo000/o00oOOo0;


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;Lokhttp3/o0O000o0;)V
    .locals 2

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    invoke-direct {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;-><init>(Lo0OOo000/o00oOOo0;)V

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    const/4 p1, 0x1

    iput-boolean p1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0oO:Z

    iput-object p2, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o0:Lokhttp3/o0O000o0;

    return-void
.end method


# virtual methods
.method public close()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0oO:Z

    if-eqz v0, :cond_1

    const/16 v0, 0x64

    sget-object v1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-static {p0, v0, v1}, Lo0OOOoOo/o0O0000O;->o00oOoOo(Lokio/o0OoO00O;ILjava/util/concurrent/TimeUnit;)Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    return-void
.end method

.method public final o00oOOoO()V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "expected chunk size and optional extensions but was \""

    iget-wide v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    const-wide/16 v3, -0x1

    cmp-long v1, v1, v3

    if-eqz v1, :cond_0

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    iget-object v1, v1, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v1}, Lokio/o00oOoO;->o0O000oo()Ljava/lang/String;

    :cond_0
    :try_start_0
    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    iget-object v1, v1, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v1}, Lokio/o00oOoO;->o0O0oOO()J

    move-result-wide v1

    iput-wide v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    iget-object v1, v1, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v1}, Lokio/o00oOoO;->o0O000oo()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v1

    iget-wide v2, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    const-wide/16 v4, 0x0

    cmp-long v2, v2, v4

    if-ltz v2, :cond_3

    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_1

    const-string v2, ";"

    invoke-virtual {v1, v2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz v2, :cond_3

    :cond_1
    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    cmp-long v0, v0, v4

    if-nez v0, :cond_2

    const/4 v0, 0x0

    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0oO:Z

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOOoO:Lokhttp3/o0O00O0o;

    invoke-virtual {v0}, Lokhttp3/o0O00O0o;->o00oo00O()Lokhttp3/o00ooO;

    move-result-object v0

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o0:Lokhttp3/o0O000o0;

    iget-object v2, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    invoke-virtual {v2}, Lo0OOo000/o00oOOo0;->o00oo0OO()Lokhttp3/o0O000Oo;

    move-result-object v2

    invoke-static {v0, v1, v2}, Lo0OOOooo/o0oO0Ooo;->o00oOoO(Lokhttp3/o00ooO;Lokhttp3/o0O000o0;Lokhttp3/o0O000Oo;)V

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_2
    return-void

    :cond_3
    :try_start_1
    new-instance v2, Ljava/net/ProtocolException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-wide v4, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    invoke-virtual {v3, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "\""

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v2, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v2
    :try_end_1
    .catch Ljava/lang/NumberFormatException; {:try_start_1 .. :try_end_1} :catch_0

    :catch_0
    move-exception v0

    new-instance v1, Ljava/net/ProtocolException;

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method public o0O0o0oO(Lokio/o00oOo00;J)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-ltz v2, :cond_5

    iget-boolean v2, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-nez v2, :cond_4

    iget-boolean v2, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0oO:Z

    const-wide/16 v3, -0x1

    if-nez v2, :cond_0

    return-wide v3

    :cond_0
    iget-wide v5, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    cmp-long v0, v5, v0

    if-eqz v0, :cond_1

    cmp-long v0, v5, v3

    if-nez v0, :cond_2

    :cond_1
    invoke-virtual {p0}, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oOOoO()V

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0oO:Z

    if-nez v0, :cond_2

    return-wide v3

    :cond_2
    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o0O0o:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    iget-wide v1, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    invoke-static {p2, p3, v1, v2}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p2

    invoke-interface {v0, p1, p2, p3}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide p1

    cmp-long p3, p1, v3

    if-eqz p3, :cond_3

    iget-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    sub-long/2addr v0, p1

    iput-wide v0, p0, Lo0OOo000/o00oOOo0$o00oOo0O;->o00oo0o:J

    return-wide p1

    :cond_3
    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    new-instance p1, Ljava/net/ProtocolException;

    const-string p2, "unexpected end of stream"

    invoke-direct {p1, p2}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_5
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "byteCount < 0: "

    invoke-static {v0, p2, p3}, Lo0O0O0O/o00oOo0O;->o00oOOo0(Ljava/lang/String;J)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
