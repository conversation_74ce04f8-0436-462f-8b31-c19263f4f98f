.class public Lo0OOO0o0/o00oOo00;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static o00oOOo0:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static o00oOOo0()V
    .locals 2

    sget-boolean v0, Lo0OOO0o0/o00oOo00;->o00oOOo0:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    sput-boolean v0, Lo0OOO0o0/o00oOo00;->o00oOOo0:Z

    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOOo0()Z

    move-result v0

    if-eqz v0, :cond_1

    :goto_0
    invoke-static {}, Lo0OOOOoo/o00ooO0;->o00oOoO()Lo0OOOOoo/o00ooO0;

    move-result-object v0

    invoke-virtual {v0}, Lo0OOOOoo/o00ooO0;->o00oOooo()V

    goto :goto_1

    :cond_1
    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOo00()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    :cond_2
    invoke-static {}, Lo0OOOOoO/o00oOoO;->o00oOooO()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    :cond_3
    :goto_1
    invoke-static {}, Lo0OOOOoo/o00ooO0;->o00oOoO()Lo0OOOOoo/o00ooO0;

    move-result-object v0

    invoke-virtual {v0}, Lo0OOOOoo/o00ooO0;->o00oOoo0()Z

    move-result v0

    if-nez v0, :cond_4

    sget-object v0, Lo0OOO0Oo/o00oOo0O;->o00oOOo0:Lo0OOO0Oo/o00oOo0O;

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lo0OOO0Oo/o00oOo0O;->o00oOOo0(Landroid/content/Context;)V

    :cond_4
    return-void
.end method
