.class public interface abstract annotation Lo0OOO0/o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        "P::",
        "Lo0OOO0/o00oOOoO<",
        "-TT;>;>",
        "Ljava/lang/Object;",
        "Ljava/lang/annotation/Annotation;"
    }
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u001b\n\u0000\u0008\u0087\u0002\u0018\u0000*\u0004\u0008\u0000\u0010\u0001*\u0010\u0008\u0001\u0010\u0003*\n\u0012\u0006\u0008\u0000\u0012\u00028\u00000\u00022\u00020\u0004B\u0000\u00a8\u0006\u0005"
    }
    d2 = {
        "Lo0OOO0/o00oOoO;",
        "T",
        "Lo0OOO0/o00oOOoO;",
        "P",
        "",
        "kotlin-android-extensions-runtime"
    }
    k = 0x1
    mv = {
        0x1,
        0x4,
        0x1
    }
.end annotation

.annotation runtime Lo0O/o00oOo0O;
.end annotation

.annotation runtime Lo0O/o00oOoO;
    value = .enum Lo0O/o00oOOo0;->SOURCE:Lo0O/o00oOOo0;
.end annotation

.annotation runtime Lo0O/o00oo00O;
    allowedTargets = {
        .enum Lo0O/o00oOOoO;->CLASS:Lo0O/o00oOOoO;,
        .enum Lo0O/o00oOOoO;->PROPERTY:Lo0O/o00oOOoO;
    }
.end annotation
