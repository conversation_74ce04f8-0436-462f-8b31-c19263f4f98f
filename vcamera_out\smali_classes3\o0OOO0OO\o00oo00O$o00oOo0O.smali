.class public Lo0OOO0Oo/o00oo00O$o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/ads/OnUserEarnedRewardListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oo00O;->o00oOoo0(Landroid/app/Activity;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oo00O;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo00O;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOo0O;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onUserEarnedReward(Lcom/google/android/gms/ads/rewarded/RewardItem;)V
    .locals 3

    invoke-interface {p1}, Lcom/google/android/gms/ads/rewarded/RewardItem;->getAmount()I

    invoke-interface {p1}, Lcom/google/android/gms/ads/rewarded/RewardItem;->getType()Ljava/lang/String;

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x26

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v2, v1, [B

    fill-array-data v2, :array_3

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    const/16 p1, 0xe

    new-array p1, p1, [B

    fill-array-data p1, :array_4

    new-array v0, v1, [B

    fill-array-data v0, :array_5

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    return-void

    nop

    :array_0
    .array-data 1
        0x10t
        -0x71t
        -0x2et
        -0xct
        -0x26t
        -0x78t
        -0x14t
        -0x55t
        0x23t
        -0x5et
        -0xft
        -0x1at
        -0x2ft
        -0x65t
        -0x1t
        -0x68t
        0x38t
        -0x71t
        -0x6t
        -0x6t
    .end array-data

    :array_1
    .array-data 1
        0x51t
        -0x15t
        -0x61t
        -0x6bt
        -0x4ct
        -0x17t
        -0x75t
        -0x32t
    .end array-data

    :array_2
    .array-data 1
        -0x4at
        -0x35t
        -0xft
        0x24t
        -0x26t
        0x67t
        0x46t
        0x4t
        -0x3et
        -0x3at
        -0xbt
        0x76t
        -0x3ft
        0x71t
        0x47t
        0x56t
        -0x6at
        -0x35t
        -0xft
        0x24t
        -0x23t
        0x71t
        0x54t
        0x17t
        -0x70t
        -0x39t
        -0x46t
        0x76t
        -0x36t
        0x63t
        0x42t
        0x4t
        -0x7at
        -0x9t
        -0x13t
        0x74t
        -0x36t
        0x2et
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x1et
        -0x5dt
        -0x6ct
        0x4t
        -0x51t
        0x14t
        0x23t
        0x76t
    .end array-data

    :array_4
    .array-data 1
        0x9t
        -0x4t
        -0x26t
        -0x30t
        -0x2bt
        -0x7ft
        -0x64t
        0x1ft
        0x48t
        -0x1ft
        -0x36t
        -0x37t
        -0x40t
        -0x37t
    .end array-data

    nop

    :array_5
    .array-data 1
        0x25t
        -0x72t
        -0x41t
        -0x59t
        -0x4ct
        -0xdt
        -0x8t
        0x5et
    .end array-data
.end method
