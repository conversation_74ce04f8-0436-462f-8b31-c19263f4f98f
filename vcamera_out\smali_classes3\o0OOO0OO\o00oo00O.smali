.class public Lo0OOO0Oo/o00oo00O;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final o00oOo0o:Ljava/lang/String;

.field public static o00oOoO:Lo0OOO0Oo/o00oo00O; = null

.field public static final o00oOoO0:J = 0x6ddd00L


# instance fields
.field public o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

.field public o00oOOoO:Z

.field public o00oOo00:J

.field public o00oOo0O:Ljava/lang/Runnable;

.field public o00oOooO:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    const/16 v0, 0x14

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v1, v1, [B

    fill-array-data v1, :array_1

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lo0OOO0Oo/o00oo00O;->o00oOo0o:Ljava/lang/String;

    const/4 v0, 0x0

    sput-object v0, Lo0OOO0Oo/o00oo00O;->o00oOoO:Lo0OOO0Oo/o00oo00O;

    return-void

    :array_0
    .array-data 1
        -0x13t
        -0x7et
        0x1bt
        0x5et
        -0x37t
        0x6ft
        0x6dt
        -0xat
        -0x22t
        -0x51t
        0x38t
        0x4ct
        -0x3et
        0x7ct
        0x7et
        -0x3bt
        -0x3bt
        -0x7et
        0x33t
        0x50t
    .end array-data

    :array_1
    .array-data 1
        -0x54t
        -0x1at
        0x56t
        0x3ft
        -0x59t
        0xet
        0xat
        -0x6dt
    .end array-data
.end method

.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOo00:J

    const/4 v0, 0x0

    iput-boolean v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOooO:Z

    new-instance v0, Lo0OOO0Oo/o00oo00O$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oo00O$o00oOOo0;-><init>(Lo0OOO0Oo/o00oo00O;)V

    iput-object v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOo0O:Ljava/lang/Runnable;

    return-void
.end method

.method public static synthetic o00oOOo0(Lo0OOO0Oo/o00oo00O;)J
    .locals 2

    iget-wide v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOo00:J

    return-wide v0
.end method

.method public static synthetic o00oOOoO(Lo0OOO0Oo/o00oo00O;J)J
    .locals 0

    iput-wide p1, p0, Lo0OOO0Oo/o00oo00O;->o00oOo00:J

    return-wide p1
.end method

.method public static synthetic o00oOo00(Lo0OOO0Oo/o00oo00O;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    return-object p0
.end method

.method public static synthetic o00oOo0O(Lo0OOO0Oo/o00oo00O;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Lo0OOO0Oo/o00oo00O;->o00oOo0O:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static o00oOo0o()Lo0OOO0Oo/o00oo00O;
    .locals 2

    sget-object v0, Lo0OOO0Oo/o00oo00O;->o00oOoO:Lo0OOO0Oo/o00oo00O;

    if-nez v0, :cond_1

    const-class v0, Lo0OOO0Oo/o00oo00O;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lo0OOO0Oo/o00oo00O;->o00oOoO:Lo0OOO0Oo/o00oo00O;

    if-nez v1, :cond_0

    new-instance v1, Lo0OOO0Oo/o00oo00O;

    invoke-direct {v1}, Lo0OOO0Oo/o00oo00O;-><init>()V

    sput-object v1, Lo0OOO0Oo/o00oo00O;->o00oOoO:Lo0OOO0Oo/o00oo00O;

    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    :cond_1
    :goto_0
    sget-object v0, Lo0OOO0Oo/o00oo00O;->o00oOoO:Lo0OOO0Oo/o00oo00O;

    return-object v0
.end method

.method public static synthetic o00oOooO(Lo0OOO0Oo/o00oo00O;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    return-object p1
.end method


# virtual methods
.method public declared-synchronized o00oOoO(Landroid/content/Context;)V
    .locals 22

    move-object/from16 v1, p0

    monitor-enter p0

    const/16 v0, 0x17

    :try_start_0
    new-array v2, v0, [B

    const/16 v3, -0x50

    const/4 v4, 0x0

    aput-byte v3, v2, v4

    const/16 v5, 0x40

    const/4 v6, 0x1

    aput-byte v5, v2, v6

    const/16 v5, -0x5f

    const/4 v7, 0x2

    aput-byte v5, v2, v7

    const/16 v5, 0x72

    const/4 v8, 0x3

    aput-byte v5, v2, v8

    const/16 v9, -0xb

    const/4 v10, 0x4

    aput-byte v9, v2, v10

    const/16 v9, 0x64

    const/4 v11, 0x5

    aput-byte v9, v2, v11

    const/16 v9, 0x62

    const/4 v12, 0x6

    aput-byte v9, v2, v12

    const/16 v9, -0x1a

    const/4 v13, 0x7

    aput-byte v9, v2, v13

    const/16 v9, -0x4c

    const/16 v14, 0x8

    aput-byte v9, v2, v14

    const/16 v9, 0x4a

    const/16 v15, 0x9

    aput-byte v9, v2, v15

    const/16 v16, 0xa

    const/16 v17, -0x79

    aput-byte v17, v2, v16

    const/16 v18, 0xb

    aput-byte v5, v2, v18

    const/16 v5, -0x10

    const/16 v19, 0xc

    aput-byte v5, v2, v19

    const/16 v5, 0x25

    const/16 v15, 0xd

    aput-byte v5, v2, v15

    const/16 v5, 0x7c

    const/16 v20, 0xe

    aput-byte v5, v2, v20

    const/16 v5, 0xf

    const/16 v21, -0x13

    aput-byte v21, v2, v5

    const/16 v5, 0x10

    aput-byte v3, v2, v5

    const/16 v3, 0x11

    aput-byte v9, v2, v3

    const/16 v3, 0x12

    const/16 v5, -0x6c

    aput-byte v5, v2, v3

    const/16 v3, 0x13

    const/16 v5, 0x73

    aput-byte v5, v2, v3

    const/16 v3, 0x14

    const/16 v5, -0x57

    aput-byte v5, v2, v3

    const/16 v3, 0x15

    const/16 v9, 0x6a

    aput-byte v9, v2, v3

    const/16 v3, 0x16

    const/16 v9, 0x20

    aput-byte v9, v2, v3

    new-array v3, v14, [B

    const/16 v9, -0x2b

    aput-byte v9, v3, v4

    const/16 v21, 0x2e

    aput-byte v21, v3, v6

    aput-byte v9, v3, v7

    aput-byte v0, v3, v8

    aput-byte v17, v3, v10

    const/16 v0, 0x44

    aput-byte v0, v3, v11

    aput-byte v20, v3, v12

    const/16 v0, -0x77

    aput-byte v0, v3, v13

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object v0, v1, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    if-nez v0, :cond_0

    iget-boolean v0, v1, Lo0OOO0Oo/o00oo00O;->o00oOOoO:Z

    if-nez v0, :cond_0

    iput-boolean v6, v1, Lo0OOO0Oo/o00oo00O;->o00oOOoO:Z

    new-instance v0, Lcom/google/android/gms/ads/AdRequest$Builder;

    invoke-direct {v0}, Lcom/google/android/gms/ads/AdRequest$Builder;-><init>()V

    invoke-virtual {v0}, Lcom/google/android/gms/ads/AdRequest$Builder;->build()Lcom/google/android/gms/ads/AdRequest;

    move-result-object v0

    new-array v2, v15, [B

    const/16 v3, 0x2c

    aput-byte v3, v2, v4

    const/16 v3, 0x42

    aput-byte v3, v2, v6

    const/16 v3, -0x38

    aput-byte v3, v2, v7

    aput-byte v19, v2, v8

    aput-byte v5, v2, v10

    const/16 v3, -0x62

    aput-byte v3, v2, v11

    const/16 v3, 0x36

    aput-byte v3, v2, v12

    const/16 v3, -0x72

    aput-byte v3, v2, v13

    const/16 v3, 0x3e

    aput-byte v3, v2, v14

    const/16 v3, 0x52

    const/16 v9, 0x9

    aput-byte v3, v2, v9

    aput-byte v17, v2, v16

    const/16 v3, 0x50

    aput-byte v3, v2, v18

    const/16 v3, -0xd

    aput-byte v3, v2, v19

    new-array v3, v14, [B

    const/16 v9, 0x5f

    aput-byte v9, v3, v4

    const/16 v4, 0x36

    aput-byte v4, v3, v6

    aput-byte v5, v3, v7

    const/16 v4, 0x7e

    aput-byte v4, v3, v8

    const/16 v4, -0x23

    aput-byte v4, v3, v10

    const/16 v4, -0x42

    aput-byte v4, v3, v11

    const/16 v4, 0x5a

    aput-byte v4, v3, v12

    const/16 v4, -0x1f

    aput-byte v4, v3, v13

    invoke-static {v2, v3}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOOoO()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Lo0OOO0Oo/o00oo00O$o00oOOoO;

    invoke-direct {v3, v1}, Lo0OOO0Oo/o00oo00O$o00oOOoO;-><init>(Lo0OOO0Oo/o00oo00O;)V

    move-object/from16 v4, p1

    invoke-static {v4, v2, v0, v3}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;->load(Landroid/content/Context;Ljava/lang/String;Lcom/google/android/gms/ads/AdRequest;Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAdLoadCallback;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public o00oOoO0()Z
    .locals 1

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOOoO:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public o00oOoOO(Lcom/google/android/gms/ads/AdValue;)V
    .locals 16

    :try_start_0
    invoke-static {}, Lo0OOOo00/o00oo0O0;->o00oOooO()Lo0OOOo00/o00oo0O0;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    move-object/from16 v1, p0

    :try_start_1
    iget-object v2, v1, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    invoke-virtual {v2}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;->getAdUnitId()Ljava/lang/String;

    move-result-object v2

    const-string v3, ""

    const/16 v4, 0xb

    new-array v4, v4, [B

    const/16 v5, 0x1a

    const/4 v6, 0x0

    aput-byte v5, v4, v6

    const/16 v5, 0x2d

    const/4 v7, 0x1

    aput-byte v5, v4, v7

    const/16 v5, 0xe

    const/4 v8, 0x2

    aput-byte v5, v4, v8

    const/16 v5, 0x1d

    const/4 v9, 0x3

    aput-byte v5, v4, v9

    const/16 v5, 0x49

    const/4 v10, 0x4

    aput-byte v5, v4, v10

    const/16 v5, 0x42

    const/4 v11, 0x5

    aput-byte v5, v4, v11

    const/16 v5, 0x51

    const/4 v12, 0x6

    aput-byte v5, v4, v12

    const/16 v5, -0x33

    const/4 v13, 0x7

    aput-byte v5, v4, v13

    const/16 v5, 0x37

    const/16 v14, 0x8

    aput-byte v5, v4, v14

    const/16 v5, 0x9

    const/16 v15, 0x26

    aput-byte v15, v4, v5

    const/16 v5, 0xa

    const/16 v15, 0x12

    aput-byte v15, v4, v5

    new-array v5, v14, [B

    const/16 v14, 0x53

    aput-byte v14, v5, v6

    const/16 v6, 0x43

    aput-byte v6, v5, v7

    const/16 v6, 0x7d

    aput-byte v6, v5, v8

    const/16 v6, 0x78

    aput-byte v6, v5, v9

    const/16 v6, 0x3b

    aput-byte v6, v5, v10

    const/16 v6, 0x36

    aput-byte v6, v5, v11

    aput-byte v13, v5, v12

    const/16 v6, -0x5c

    aput-byte v6, v5, v13

    invoke-static {v4, v5}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v4

    move-object/from16 v5, p1

    invoke-virtual {v0, v5, v2, v3, v4}, Lo0OOOo00/o00oo0O0;->o00oOoO0(Lcom/google/android/gms/ads/AdValue;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_0

    :catchall_1
    move-exception v0

    move-object/from16 v1, p0

    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method

.method public o00oOoOo()V
    .locals 4

    sget-object v0, Lmultispace/multiapp/clone/util/o0O00000;->o00oOOo0:Landroid/os/Handler;

    iget-object v1, p0, Lo0OOO0Oo/o00oo00O;->o00oOo0O:Ljava/lang/Runnable;

    const-wide/16 v2, 0x7530

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0, v0}, Lo0OOO0Oo/o00oo00O;->o00oOoO(Landroid/content/Context;)V

    return-void
.end method

.method public o00oOoo0(Landroid/app/Activity;)Z
    .locals 3

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    const/16 v1, 0x8

    if-nez v0, :cond_0

    const/16 p1, 0x21

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    new-array v0, v1, [B

    fill-array-data v0, :array_1

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/4 p1, 0x0

    return p1

    :cond_0
    new-instance v2, Lo0OOO0Oo/o00oo00O$o00oOo00;

    invoke-direct {v2, p0, p1}, Lo0OOO0Oo/o00oo00O$o00oOo00;-><init>(Lo0OOO0Oo/o00oo00O;Landroid/app/Activity;)V

    invoke-virtual {v0, v2}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;->setFullScreenContentCallback(Lcom/google/android/gms/ads/FullScreenContentCallback;)V

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O;->o00oOOo0:Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;

    new-instance v2, Lo0OOO0Oo/o00oo00O$o00oOo0O;

    invoke-direct {v2, p0}, Lo0OOO0Oo/o00oo00O$o00oOo0O;-><init>(Lo0OOO0Oo/o00oo00O;)V

    invoke-virtual {v0, p1, v2}, Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;->show(Landroid/app/Activity;Lcom/google/android/gms/ads/OnUserEarnedRewardListener;)V

    const/16 p1, 0xe

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v1, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/4 p1, 0x1

    return p1

    :array_0
    .array-data 1
        0x9t
        0x70t
        0x1t
        0x3at
        0x72t
        0x6dt
        0x2ft
        0x75t
        0x2ft
        0x6ct
        0x44t
        0x6ct
        0x72t
        0x67t
        0x39t
        0x7ft
        0x7dt
        0x70t
        0x5t
        0x69t
        0x3bt
        0x6dt
        0x33t
        0x64t
        0x7dt
        0x6at
        0x1t
        0x7bt
        0x7ft
        0x7at
        0x72t
        0x3et
        0x73t
    .end array-data

    nop

    :array_1
    .array-data 1
        0x5dt
        0x18t
        0x64t
        0x1at
        0x1bt
        0x3t
        0x5ct
        0x10t
    .end array-data

    :array_2
    .array-data 1
        -0x13t
        0xft
        -0x52t
        0x52t
        0x73t
        0x1t
        -0x1dt
        0x6t
        -0x5t
        0x8t
        -0x11t
        0xbt
        0x7dt
        0x59t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x62t
        0x67t
        -0x3ft
        0x25t
        0x53t
        0x77t
        -0x76t
        0x62t
    .end array-data
.end method
