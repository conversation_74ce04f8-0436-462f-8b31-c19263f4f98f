<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010011" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010017" />
    <public type="anim" name="decelerate_cubic" id="0x7f010018" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f010019" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01001a" />
    <public type="anim" name="design_snackbar_in" id="0x7f01001b" />
    <public type="anim" name="design_snackbar_out" id="0x7f01001c" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f01001d" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f01001e" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f01001f" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f010020" />
    <public type="anim" name="popup_enter" id="0x7f010021" />
    <public type="anim" name="popup_exit" id="0x7f010022" />
    <public type="anim" name="powerspinner_dispose_center" id="0x7f010023" />
    <public type="anim" name="powerspinner_elastic_down" id="0x7f010024" />
    <public type="anim" name="powerspinner_elastic_up" id="0x7f010025" />
    <public type="anim" name="powerspinner_fade_in" id="0x7f010026" />
    <public type="anim" name="powerspinner_fade_out" id="0x7f010027" />
    <public type="anim" name="powerspinner_show_down_center" id="0x7f010028" />
    <public type="anim" name="powerspinner_show_up_center" id="0x7f010029" />
    <public type="anim" name="rotation" id="0x7f01002a" />
    <public type="anim" name="scale_down" id="0x7f01002b" />
    <public type="anim" name="scale_up" id="0x7f01002c" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="linear_indeterminate_line1_head_interpolator" id="0x7f020009" />
    <public type="animator" name="linear_indeterminate_line1_tail_interpolator" id="0x7f02000a" />
    <public type="animator" name="linear_indeterminate_line2_head_interpolator" id="0x7f02000b" />
    <public type="animator" name="linear_indeterminate_line2_tail_interpolator" id="0x7f02000c" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f02000d" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f02000e" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f02000f" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f020010" />
    <public type="animator" name="mtrl_extended_fab_change_size_collapse_motion_spec" id="0x7f020011" />
    <public type="animator" name="mtrl_extended_fab_change_size_expand_motion_spec" id="0x7f020012" />
    <public type="animator" name="mtrl_extended_fab_hide_motion_spec" id="0x7f020013" />
    <public type="animator" name="mtrl_extended_fab_show_motion_spec" id="0x7f020014" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f020015" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020016" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020017" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f020018" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f020019" />
    <public type="array" name="channel_settings_1" id="0x7f030000" />
    <public type="array" name="channel_settings_2" id="0x7f030001" />
    <public type="array" name="exo_controls_playback_speeds" id="0x7f030002" />
    <public type="array" name="flip_settings" id="0x7f030003" />
    <public type="attr" name="actionBarDivider" id="0x7f040000" />
    <public type="attr" name="actionBarItemBackground" id="0x7f040001" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f040002" />
    <public type="attr" name="actionBarSize" id="0x7f040003" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f040004" />
    <public type="attr" name="actionBarStyle" id="0x7f040005" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f040006" />
    <public type="attr" name="actionBarTabStyle" id="0x7f040007" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f040008" />
    <public type="attr" name="actionBarTheme" id="0x7f040009" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f04000a" />
    <public type="attr" name="actionButtonStyle" id="0x7f04000b" />
    <public type="attr" name="actionDropDownStyle" id="0x7f04000c" />
    <public type="attr" name="actionLayout" id="0x7f04000d" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f04000e" />
    <public type="attr" name="actionMenuTextColor" id="0x7f04000f" />
    <public type="attr" name="actionModeBackground" id="0x7f040010" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f040011" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f040012" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f040013" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f040014" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f040015" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f040016" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f040017" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f040018" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f040019" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f04001a" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f04001b" />
    <public type="attr" name="actionModeStyle" id="0x7f04001c" />
    <public type="attr" name="actionModeTheme" id="0x7f04001d" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f04001e" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f04001f" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f040020" />
    <public type="attr" name="actionProviderClass" id="0x7f040021" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f040022" />
    <public type="attr" name="actionViewClass" id="0x7f040023" />
    <public type="attr" name="activityAction" id="0x7f040024" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f040025" />
    <public type="attr" name="activityName" id="0x7f040026" />
    <public type="attr" name="adSize" id="0x7f040027" />
    <public type="attr" name="adSizes" id="0x7f040028" />
    <public type="attr" name="adUnitId" id="0x7f040029" />
    <public type="attr" name="ad_marker_color" id="0x7f04002a" />
    <public type="attr" name="ad_marker_width" id="0x7f04002b" />
    <public type="attr" name="adjustable" id="0x7f04002c" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f04002d" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f04002e" />
    <public type="attr" name="alertDialogStyle" id="0x7f04002f" />
    <public type="attr" name="alertDialogTheme" id="0x7f040030" />
    <public type="attr" name="allowDividerAbove" id="0x7f040031" />
    <public type="attr" name="allowDividerAfterLastItem" id="0x7f040032" />
    <public type="attr" name="allowDividerBelow" id="0x7f040033" />
    <public type="attr" name="allowStacking" id="0x7f040034" />
    <public type="attr" name="alpha" id="0x7f040035" />
    <public type="attr" name="alphabeticModifiers" id="0x7f040036" />
    <public type="attr" name="altSrc" id="0x7f040037" />
    <public type="attr" name="alwaysExpand" id="0x7f040038" />
    <public type="attr" name="ambientEnabled" id="0x7f040039" />
    <public type="attr" name="animate_relativeTo" id="0x7f04003a" />
    <public type="attr" name="animationMode" id="0x7f04003b" />
    <public type="attr" name="animation_enabled" id="0x7f04003c" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f04003d" />
    <public type="attr" name="applyMotionScene" id="0x7f04003e" />
    <public type="attr" name="arcMode" id="0x7f04003f" />
    <public type="attr" name="arrowHeadLength" id="0x7f040040" />
    <public type="attr" name="arrowShaftLength" id="0x7f040041" />
    <public type="attr" name="attributeName" id="0x7f040042" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f040043" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f040044" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f040045" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f040046" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f040047" />
    <public type="attr" name="autoSizeTextType" id="0x7f040048" />
    <public type="attr" name="autoTransition" id="0x7f040049" />
    <public type="attr" name="auto_show" id="0x7f04004a" />
    <public type="attr" name="backIconAlpha" id="0x7f04004b" />
    <public type="attr" name="backIconTint" id="0x7f04004c" />
    <public type="attr" name="background" id="0x7f04004d" />
    <public type="attr" name="backgroundColor" id="0x7f04004e" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f04004f" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f040050" />
    <public type="attr" name="backgroundInsetStart" id="0x7f040051" />
    <public type="attr" name="backgroundInsetTop" id="0x7f040052" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f040053" />
    <public type="attr" name="backgroundSplit" id="0x7f040054" />
    <public type="attr" name="backgroundStacked" id="0x7f040055" />
    <public type="attr" name="backgroundTint" id="0x7f040056" />
    <public type="attr" name="backgroundTintMode" id="0x7f040057" />
    <public type="attr" name="badgeGravity" id="0x7f040058" />
    <public type="attr" name="badgeStyle" id="0x7f040059" />
    <public type="attr" name="badgeTextColor" id="0x7f04005a" />
    <public type="attr" name="barLength" id="0x7f04005b" />
    <public type="attr" name="bar_gravity" id="0x7f04005c" />
    <public type="attr" name="bar_height" id="0x7f04005d" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f04005e" />
    <public type="attr" name="barrierDirection" id="0x7f04005f" />
    <public type="attr" name="barrierMargin" id="0x7f040060" />
    <public type="attr" name="behavior_autoHide" id="0x7f040061" />
    <public type="attr" name="behavior_autoShrink" id="0x7f040062" />
    <public type="attr" name="behavior_draggable" id="0x7f040063" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f040064" />
    <public type="attr" name="behavior_fitToContents" id="0x7f040065" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f040066" />
    <public type="attr" name="behavior_hideable" id="0x7f040067" />
    <public type="attr" name="behavior_overlapTop" id="0x7f040068" />
    <public type="attr" name="behavior_peekHeight" id="0x7f040069" />
    <public type="attr" name="behavior_saveFlags" id="0x7f04006a" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f04006b" />
    <public type="attr" name="bg_color" id="0x7f04006c" />
    <public type="attr" name="borderWidth" id="0x7f04006d" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f04006e" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f04006f" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f040070" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f040071" />
    <public type="attr" name="bottomSheetStyle" id="0x7f040072" />
    <public type="attr" name="boxBackgroundColor" id="0x7f040073" />
    <public type="attr" name="boxBackgroundMode" id="0x7f040074" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f040075" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f040076" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f040077" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f040078" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f040079" />
    <public type="attr" name="boxStrokeColor" id="0x7f04007a" />
    <public type="attr" name="boxStrokeErrorColor" id="0x7f04007b" />
    <public type="attr" name="boxStrokeWidth" id="0x7f04007c" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f04007d" />
    <public type="attr" name="brightness" id="0x7f04007e" />
    <public type="attr" name="buffered_color" id="0x7f04007f" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f040080" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f040081" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f040082" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f040083" />
    <public type="attr" name="buttonBarStyle" id="0x7f040084" />
    <public type="attr" name="buttonCompat" id="0x7f040085" />
    <public type="attr" name="buttonGravity" id="0x7f040086" />
    <public type="attr" name="buttonIconDimen" id="0x7f040087" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f040088" />
    <public type="attr" name="buttonSize" id="0x7f040089" />
    <public type="attr" name="buttonStyle" id="0x7f04008a" />
    <public type="attr" name="buttonStyleSmall" id="0x7f04008b" />
    <public type="attr" name="buttonTint" id="0x7f04008c" />
    <public type="attr" name="buttonTintMode" id="0x7f04008d" />
    <public type="attr" name="cameraBearing" id="0x7f04008e" />
    <public type="attr" name="cameraMaxZoomPreference" id="0x7f04008f" />
    <public type="attr" name="cameraMinZoomPreference" id="0x7f040090" />
    <public type="attr" name="cameraTargetLat" id="0x7f040091" />
    <public type="attr" name="cameraTargetLng" id="0x7f040092" />
    <public type="attr" name="cameraTilt" id="0x7f040093" />
    <public type="attr" name="cameraZoom" id="0x7f040094" />
    <public type="attr" name="cardBackgroundColor" id="0x7f040095" />
    <public type="attr" name="cardCornerRadius" id="0x7f040096" />
    <public type="attr" name="cardElevation" id="0x7f040097" />
    <public type="attr" name="cardForegroundColor" id="0x7f040098" />
    <public type="attr" name="cardMaxElevation" id="0x7f040099" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f04009a" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f04009b" />
    <public type="attr" name="cardViewStyle" id="0x7f04009c" />
    <public type="attr" name="chainUseRtl" id="0x7f04009d" />
    <public type="attr" name="checkBoxPreferenceStyle" id="0x7f04009e" />
    <public type="attr" name="checkMarkCompat" id="0x7f04009f" />
    <public type="attr" name="checkMarkTint" id="0x7f0400a0" />
    <public type="attr" name="checkMarkTintMode" id="0x7f0400a1" />
    <public type="attr" name="checkboxStyle" id="0x7f0400a2" />
    <public type="attr" name="checkedButton" id="0x7f0400a3" />
    <public type="attr" name="checkedChip" id="0x7f0400a4" />
    <public type="attr" name="checkedIcon" id="0x7f0400a5" />
    <public type="attr" name="checkedIconEnabled" id="0x7f0400a6" />
    <public type="attr" name="checkedIconMargin" id="0x7f0400a7" />
    <public type="attr" name="checkedIconSize" id="0x7f0400a8" />
    <public type="attr" name="checkedIconTint" id="0x7f0400a9" />
    <public type="attr" name="checkedIconVisible" id="0x7f0400aa" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f0400ab" />
    <public type="attr" name="chipBackgroundColor" id="0x7f0400ac" />
    <public type="attr" name="chipCornerRadius" id="0x7f0400ad" />
    <public type="attr" name="chipEndPadding" id="0x7f0400ae" />
    <public type="attr" name="chipGroupStyle" id="0x7f0400af" />
    <public type="attr" name="chipIcon" id="0x7f0400b0" />
    <public type="attr" name="chipIconEnabled" id="0x7f0400b1" />
    <public type="attr" name="chipIconSize" id="0x7f0400b2" />
    <public type="attr" name="chipIconTint" id="0x7f0400b3" />
    <public type="attr" name="chipIconVisible" id="0x7f0400b4" />
    <public type="attr" name="chipMinHeight" id="0x7f0400b5" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f0400b6" />
    <public type="attr" name="chipSpacing" id="0x7f0400b7" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f0400b8" />
    <public type="attr" name="chipSpacingVertical" id="0x7f0400b9" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f0400ba" />
    <public type="attr" name="chipStartPadding" id="0x7f0400bb" />
    <public type="attr" name="chipStrokeColor" id="0x7f0400bc" />
    <public type="attr" name="chipStrokeWidth" id="0x7f0400bd" />
    <public type="attr" name="chipStyle" id="0x7f0400be" />
    <public type="attr" name="chipSurfaceColor" id="0x7f0400bf" />
    <public type="attr" name="circleCrop" id="0x7f0400c0" />
    <public type="attr" name="circleRadius" id="0x7f0400c1" />
    <public type="attr" name="circularProgressIndicatorStyle" id="0x7f0400c2" />
    <public type="attr" name="clearTop" id="0x7f0400c3" />
    <public type="attr" name="clickAction" id="0x7f0400c4" />
    <public type="attr" name="clockFaceBackgroundColor" id="0x7f0400c5" />
    <public type="attr" name="clockHandColor" id="0x7f0400c6" />
    <public type="attr" name="clockIcon" id="0x7f0400c7" />
    <public type="attr" name="clockNumberTextColor" id="0x7f0400c8" />
    <public type="attr" name="closeIcon" id="0x7f0400c9" />
    <public type="attr" name="closeIconEnabled" id="0x7f0400ca" />
    <public type="attr" name="closeIconEndPadding" id="0x7f0400cb" />
    <public type="attr" name="closeIconSize" id="0x7f0400cc" />
    <public type="attr" name="closeIconStartPadding" id="0x7f0400cd" />
    <public type="attr" name="closeIconTint" id="0x7f0400ce" />
    <public type="attr" name="closeIconVisible" id="0x7f0400cf" />
    <public type="attr" name="closeItemLayout" id="0x7f0400d0" />
    <public type="attr" name="collapseContentDescription" id="0x7f0400d1" />
    <public type="attr" name="collapseIcon" id="0x7f0400d2" />
    <public type="attr" name="collapsedSize" id="0x7f0400d3" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0400d4" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0400d5" />
    <public type="attr" name="collapsingToolbarLayoutStyle" id="0x7f0400d6" />
    <public type="attr" name="color" id="0x7f0400d7" />
    <public type="attr" name="colorAccent" id="0x7f0400d8" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0400d9" />
    <public type="attr" name="colorButtonNormal" id="0x7f0400da" />
    <public type="attr" name="colorControlActivated" id="0x7f0400db" />
    <public type="attr" name="colorControlHighlight" id="0x7f0400dc" />
    <public type="attr" name="colorControlNormal" id="0x7f0400dd" />
    <public type="attr" name="colorError" id="0x7f0400de" />
    <public type="attr" name="colorOnBackground" id="0x7f0400df" />
    <public type="attr" name="colorOnError" id="0x7f0400e0" />
    <public type="attr" name="colorOnPrimary" id="0x7f0400e1" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f0400e2" />
    <public type="attr" name="colorOnSecondary" id="0x7f0400e3" />
    <public type="attr" name="colorOnSurface" id="0x7f0400e4" />
    <public type="attr" name="colorPrimary" id="0x7f0400e5" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0400e6" />
    <public type="attr" name="colorPrimarySurface" id="0x7f0400e7" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f0400e8" />
    <public type="attr" name="colorScheme" id="0x7f0400e9" />
    <public type="attr" name="colorSecondary" id="0x7f0400ea" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f0400eb" />
    <public type="attr" name="colorSurface" id="0x7f0400ec" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f0400ed" />
    <public type="attr" name="commitIcon" id="0x7f0400ee" />
    <public type="attr" name="constraintSet" id="0x7f0400ef" />
    <public type="attr" name="constraintSetEnd" id="0x7f0400f0" />
    <public type="attr" name="constraintSetStart" id="0x7f0400f1" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f0400f2" />
    <public type="attr" name="constraint_referenced_tags" id="0x7f0400f3" />
    <public type="attr" name="constraints" id="0x7f0400f4" />
    <public type="attr" name="content" id="0x7f0400f5" />
    <public type="attr" name="contentDescription" id="0x7f0400f6" />
    <public type="attr" name="contentInsetEnd" id="0x7f0400f7" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f0400f8" />
    <public type="attr" name="contentInsetLeft" id="0x7f0400f9" />
    <public type="attr" name="contentInsetRight" id="0x7f0400fa" />
    <public type="attr" name="contentInsetStart" id="0x7f0400fb" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f0400fc" />
    <public type="attr" name="contentPadding" id="0x7f0400fd" />
    <public type="attr" name="contentPaddingBottom" id="0x7f0400fe" />
    <public type="attr" name="contentPaddingEnd" id="0x7f0400ff" />
    <public type="attr" name="contentPaddingLeft" id="0x7f040100" />
    <public type="attr" name="contentPaddingRight" id="0x7f040101" />
    <public type="attr" name="contentPaddingStart" id="0x7f040102" />
    <public type="attr" name="contentPaddingTop" id="0x7f040103" />
    <public type="attr" name="contentScrim" id="0x7f040104" />
    <public type="attr" name="contrast" id="0x7f040105" />
    <public type="attr" name="controlBackground" id="0x7f040106" />
    <public type="attr" name="controller_layout_id" id="0x7f040107" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f040108" />
    <public type="attr" name="cornerFamily" id="0x7f040109" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f04010a" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f04010b" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f04010c" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f04010d" />
    <public type="attr" name="cornerRadius" id="0x7f04010e" />
    <public type="attr" name="cornerSize" id="0x7f04010f" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f040110" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f040111" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f040112" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f040113" />
    <public type="attr" name="counterEnabled" id="0x7f040114" />
    <public type="attr" name="counterMaxLength" id="0x7f040115" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f040116" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f040117" />
    <public type="attr" name="counterTextAppearance" id="0x7f040118" />
    <public type="attr" name="counterTextColor" id="0x7f040119" />
    <public type="attr" name="crossfade" id="0x7f04011a" />
    <public type="attr" name="currentState" id="0x7f04011b" />
    <public type="attr" name="cursorColor" id="0x7f04011c" />
    <public type="attr" name="curveFit" id="0x7f04011d" />
    <public type="attr" name="customBoolean" id="0x7f04011e" />
    <public type="attr" name="customColorDrawableValue" id="0x7f04011f" />
    <public type="attr" name="customColorValue" id="0x7f040120" />
    <public type="attr" name="customDimension" id="0x7f040121" />
    <public type="attr" name="customFloatValue" id="0x7f040122" />
    <public type="attr" name="customIntegerValue" id="0x7f040123" />
    <public type="attr" name="customNavigationLayout" id="0x7f040124" />
    <public type="attr" name="customPixelDimension" id="0x7f040125" />
    <public type="attr" name="customStringValue" id="0x7f040126" />
    <public type="attr" name="dampingRatio" id="0x7f040127" />
    <public type="attr" name="dayInvalidStyle" id="0x7f040128" />
    <public type="attr" name="daySelectedStyle" id="0x7f040129" />
    <public type="attr" name="dayStyle" id="0x7f04012a" />
    <public type="attr" name="dayTodayStyle" id="0x7f04012b" />
    <public type="attr" name="defaultDuration" id="0x7f04012c" />
    <public type="attr" name="defaultQueryHint" id="0x7f04012d" />
    <public type="attr" name="defaultState" id="0x7f04012e" />
    <public type="attr" name="defaultValue" id="0x7f04012f" />
    <public type="attr" name="default_artwork" id="0x7f040130" />
    <public type="attr" name="deltaPolarAngle" id="0x7f040131" />
    <public type="attr" name="deltaPolarRadius" id="0x7f040132" />
    <public type="attr" name="dependency" id="0x7f040133" />
    <public type="attr" name="deriveConstraintsFrom" id="0x7f040134" />
    <public type="attr" name="dialogCornerRadius" id="0x7f040135" />
    <public type="attr" name="dialogIcon" id="0x7f040136" />
    <public type="attr" name="dialogLayout" id="0x7f040137" />
    <public type="attr" name="dialogMessage" id="0x7f040138" />
    <public type="attr" name="dialogPreferenceStyle" id="0x7f040139" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f04013a" />
    <public type="attr" name="dialogTheme" id="0x7f04013b" />
    <public type="attr" name="dialogTitle" id="0x7f04013c" />
    <public type="attr" name="disableDependentsState" id="0x7f04013d" />
    <public type="attr" name="displayOptions" id="0x7f04013e" />
    <public type="attr" name="divider" id="0x7f04013f" />
    <public type="attr" name="dividerHorizontal" id="0x7f040140" />
    <public type="attr" name="dividerPadding" id="0x7f040141" />
    <public type="attr" name="dividerVertical" id="0x7f040142" />
    <public type="attr" name="dotsClickable" id="0x7f040143" />
    <public type="attr" name="dotsColor" id="0x7f040144" />
    <public type="attr" name="dotsCornerRadius" id="0x7f040145" />
    <public type="attr" name="dotsElevation" id="0x7f040146" />
    <public type="attr" name="dotsSize" id="0x7f040147" />
    <public type="attr" name="dotsSpacing" id="0x7f040148" />
    <public type="attr" name="dotsStrokeColor" id="0x7f040149" />
    <public type="attr" name="dotsStrokeWidth" id="0x7f04014a" />
    <public type="attr" name="dotsWidthFactor" id="0x7f04014b" />
    <public type="attr" name="dragDirection" id="0x7f04014c" />
    <public type="attr" name="dragScale" id="0x7f04014d" />
    <public type="attr" name="dragThreshold" id="0x7f04014e" />
    <public type="attr" name="drawPath" id="0x7f04014f" />
    <public type="attr" name="drawableBottomCompat" id="0x7f040150" />
    <public type="attr" name="drawableEndCompat" id="0x7f040151" />
    <public type="attr" name="drawableLeftCompat" id="0x7f040152" />
    <public type="attr" name="drawableRightCompat" id="0x7f040153" />
    <public type="attr" name="drawableSize" id="0x7f040154" />
    <public type="attr" name="drawableStartCompat" id="0x7f040155" />
    <public type="attr" name="drawableTint" id="0x7f040156" />
    <public type="attr" name="drawableTintMode" id="0x7f040157" />
    <public type="attr" name="drawableTopCompat" id="0x7f040158" />
    <public type="attr" name="drawerArrowStyle" id="0x7f040159" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f04015a" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f04015b" />
    <public type="attr" name="dropdownPreferenceStyle" id="0x7f04015c" />
    <public type="attr" name="duration" id="0x7f04015d" />
    <public type="attr" name="editTextBackground" id="0x7f04015e" />
    <public type="attr" name="editTextColor" id="0x7f04015f" />
    <public type="attr" name="editTextPreferenceStyle" id="0x7f040160" />
    <public type="attr" name="editTextStyle" id="0x7f040161" />
    <public type="attr" name="elevation" id="0x7f040162" />
    <public type="attr" name="elevationOverlayColor" id="0x7f040163" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f040164" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f040165" />
    <public type="attr" name="emptyResource" id="0x7f040166" />
    <public type="attr" name="enableCopying" id="0x7f040167" />
    <public type="attr" name="enabled" id="0x7f040168" />
    <public type="attr" name="endIconCheckable" id="0x7f040169" />
    <public type="attr" name="endIconContentDescription" id="0x7f04016a" />
    <public type="attr" name="endIconDrawable" id="0x7f04016b" />
    <public type="attr" name="endIconMode" id="0x7f04016c" />
    <public type="attr" name="endIconTint" id="0x7f04016d" />
    <public type="attr" name="endIconTintMode" id="0x7f04016e" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f04016f" />
    <public type="attr" name="enforceTextAppearance" id="0x7f040170" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f040171" />
    <public type="attr" name="entries" id="0x7f040172" />
    <public type="attr" name="entryValues" id="0x7f040173" />
    <public type="attr" name="errorContentDescription" id="0x7f040174" />
    <public type="attr" name="errorEnabled" id="0x7f040175" />
    <public type="attr" name="errorIconDrawable" id="0x7f040176" />
    <public type="attr" name="errorIconTint" id="0x7f040177" />
    <public type="attr" name="errorIconTintMode" id="0x7f040178" />
    <public type="attr" name="errorTextAppearance" id="0x7f040179" />
    <public type="attr" name="errorTextColor" id="0x7f04017a" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f04017b" />
    <public type="attr" name="expanded" id="0x7f04017c" />
    <public type="attr" name="expandedHintEnabled" id="0x7f04017d" />
    <public type="attr" name="expandedTitleGravity" id="0x7f04017e" />
    <public type="attr" name="expandedTitleMargin" id="0x7f04017f" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f040180" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f040181" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f040182" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f040183" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f040184" />
    <public type="attr" name="extendMotionSpec" id="0x7f040185" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f040186" />
    <public type="attr" name="fabAlignmentMode" id="0x7f040187" />
    <public type="attr" name="fabAnimationMode" id="0x7f040188" />
    <public type="attr" name="fabCradleMargin" id="0x7f040189" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f04018a" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f04018b" />
    <public type="attr" name="fabCustomSize" id="0x7f04018c" />
    <public type="attr" name="fabSize" id="0x7f04018d" />
    <public type="attr" name="fastScrollEnabled" id="0x7f04018e" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f04018f" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f040190" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f040191" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f040192" />
    <public type="attr" name="finishPrimaryWithSecondary" id="0x7f040193" />
    <public type="attr" name="finishSecondaryWithPrimary" id="0x7f040194" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f040195" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f040196" />
    <public type="attr" name="flow_firstHorizontalBias" id="0x7f040197" />
    <public type="attr" name="flow_firstHorizontalStyle" id="0x7f040198" />
    <public type="attr" name="flow_firstVerticalBias" id="0x7f040199" />
    <public type="attr" name="flow_firstVerticalStyle" id="0x7f04019a" />
    <public type="attr" name="flow_horizontalAlign" id="0x7f04019b" />
    <public type="attr" name="flow_horizontalBias" id="0x7f04019c" />
    <public type="attr" name="flow_horizontalGap" id="0x7f04019d" />
    <public type="attr" name="flow_horizontalStyle" id="0x7f04019e" />
    <public type="attr" name="flow_lastHorizontalBias" id="0x7f04019f" />
    <public type="attr" name="flow_lastHorizontalStyle" id="0x7f0401a0" />
    <public type="attr" name="flow_lastVerticalBias" id="0x7f0401a1" />
    <public type="attr" name="flow_lastVerticalStyle" id="0x7f0401a2" />
    <public type="attr" name="flow_maxElementsWrap" id="0x7f0401a3" />
    <public type="attr" name="flow_padding" id="0x7f0401a4" />
    <public type="attr" name="flow_verticalAlign" id="0x7f0401a5" />
    <public type="attr" name="flow_verticalBias" id="0x7f0401a6" />
    <public type="attr" name="flow_verticalGap" id="0x7f0401a7" />
    <public type="attr" name="flow_verticalStyle" id="0x7f0401a8" />
    <public type="attr" name="flow_wrapMode" id="0x7f0401a9" />
    <public type="attr" name="font" id="0x7f0401aa" />
    <public type="attr" name="fontFamily" id="0x7f0401ab" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0401ac" />
    <public type="attr" name="fontProviderCerts" id="0x7f0401ad" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0401ae" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0401af" />
    <public type="attr" name="fontProviderPackage" id="0x7f0401b0" />
    <public type="attr" name="fontProviderQuery" id="0x7f0401b1" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f0401b2" />
    <public type="attr" name="fontStyle" id="0x7f0401b3" />
    <public type="attr" name="fontVariationSettings" id="0x7f0401b4" />
    <public type="attr" name="fontWeight" id="0x7f0401b5" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f0401b6" />
    <public type="attr" name="fragment" id="0x7f0401b7" />
    <public type="attr" name="framePosition" id="0x7f0401b8" />
    <public type="attr" name="gapBetweenBars" id="0x7f0401b9" />
    <public type="attr" name="gestureInsetBottomIgnored" id="0x7f0401ba" />
    <public type="attr" name="goIcon" id="0x7f0401bb" />
    <public type="attr" name="haloColor" id="0x7f0401bc" />
    <public type="attr" name="haloRadius" id="0x7f0401bd" />
    <public type="attr" name="headerLayout" id="0x7f0401be" />
    <public type="attr" name="height" id="0x7f0401bf" />
    <public type="attr" name="helperText" id="0x7f0401c0" />
    <public type="attr" name="helperTextEnabled" id="0x7f0401c1" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f0401c2" />
    <public type="attr" name="helperTextTextColor" id="0x7f0401c3" />
    <public type="attr" name="hideAnimationBehavior" id="0x7f0401c4" />
    <public type="attr" name="hideMotionSpec" id="0x7f0401c5" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0401c6" />
    <public type="attr" name="hideOnScroll" id="0x7f0401c7" />
    <public type="attr" name="hide_during_ads" id="0x7f0401c8" />
    <public type="attr" name="hide_on_touch" id="0x7f0401c9" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f0401ca" />
    <public type="attr" name="hintColor" id="0x7f0401cb" />
    <public type="attr" name="hintEnabled" id="0x7f0401cc" />
    <public type="attr" name="hintTextAppearance" id="0x7f0401cd" />
    <public type="attr" name="hintTextColor" id="0x7f0401ce" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0401cf" />
    <public type="attr" name="homeLayout" id="0x7f0401d0" />
    <public type="attr" name="horizontalOffset" id="0x7f0401d1" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f0401d2" />
    <public type="attr" name="icon" id="0x7f0401d3" />
    <public type="attr" name="iconEndPadding" id="0x7f0401d4" />
    <public type="attr" name="iconGravity" id="0x7f0401d5" />
    <public type="attr" name="iconPadding" id="0x7f0401d6" />
    <public type="attr" name="iconSize" id="0x7f0401d7" />
    <public type="attr" name="iconSpaceReserved" id="0x7f0401d8" />
    <public type="attr" name="iconStartPadding" id="0x7f0401d9" />
    <public type="attr" name="iconTint" id="0x7f0401da" />
    <public type="attr" name="iconTintMode" id="0x7f0401db" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0401dc" />
    <public type="attr" name="iconsAlpha" id="0x7f0401dd" />
    <public type="attr" name="iconsTint" id="0x7f0401de" />
    <public type="attr" name="imageAspectRatio" id="0x7f0401df" />
    <public type="attr" name="imageAspectRatioAdjust" id="0x7f0401e0" />
    <public type="attr" name="imageButtonStyle" id="0x7f0401e1" />
    <public type="attr" name="indeterminateAnimationType" id="0x7f0401e2" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f0401e3" />
    <public type="attr" name="indicatorColor" id="0x7f0401e4" />
    <public type="attr" name="indicatorDirectionCircular" id="0x7f0401e5" />
    <public type="attr" name="indicatorDirectionLinear" id="0x7f0401e6" />
    <public type="attr" name="indicatorInset" id="0x7f0401e7" />
    <public type="attr" name="indicatorSize" id="0x7f0401e8" />
    <public type="attr" name="initialActivityCount" id="0x7f0401e9" />
    <public type="attr" name="initialExpandedChildrenCount" id="0x7f0401ea" />
    <public type="attr" name="insetForeground" id="0x7f0401eb" />
    <public type="attr" name="isLightTheme" id="0x7f0401ec" />
    <public type="attr" name="isMaterialTheme" id="0x7f0401ed" />
    <public type="attr" name="isPreferenceVisible" id="0x7f0401ee" />
    <public type="attr" name="itemBackground" id="0x7f0401ef" />
    <public type="attr" name="itemFillColor" id="0x7f0401f0" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f0401f1" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f0401f2" />
    <public type="attr" name="itemIconPadding" id="0x7f0401f3" />
    <public type="attr" name="itemIconSize" id="0x7f0401f4" />
    <public type="attr" name="itemIconTint" id="0x7f0401f5" />
    <public type="attr" name="itemMaxLines" id="0x7f0401f6" />
    <public type="attr" name="itemPadding" id="0x7f0401f7" />
    <public type="attr" name="itemRippleColor" id="0x7f0401f8" />
    <public type="attr" name="itemShapeAppearance" id="0x7f0401f9" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f0401fa" />
    <public type="attr" name="itemShapeFillColor" id="0x7f0401fb" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f0401fc" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f0401fd" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f0401fe" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f0401ff" />
    <public type="attr" name="itemSpacing" id="0x7f040200" />
    <public type="attr" name="itemStrokeColor" id="0x7f040201" />
    <public type="attr" name="itemStrokeWidth" id="0x7f040202" />
    <public type="attr" name="itemTextAppearance" id="0x7f040203" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f040204" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f040205" />
    <public type="attr" name="itemTextColor" id="0x7f040206" />
    <public type="attr" name="keep_content_on_player_reset" id="0x7f040207" />
    <public type="attr" name="key" id="0x7f040208" />
    <public type="attr" name="keyPositionType" id="0x7f040209" />
    <public type="attr" name="keyboardIcon" id="0x7f04020a" />
    <public type="attr" name="keylines" id="0x7f04020b" />
    <public type="attr" name="lStar" id="0x7f04020c" />
    <public type="attr" name="labelBehavior" id="0x7f04020d" />
    <public type="attr" name="labelStyle" id="0x7f04020e" />
    <public type="attr" name="labelVisibilityMode" id="0x7f04020f" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f040210" />
    <public type="attr" name="latLngBoundsNorthEastLatitude" id="0x7f040211" />
    <public type="attr" name="latLngBoundsNorthEastLongitude" id="0x7f040212" />
    <public type="attr" name="latLngBoundsSouthWestLatitude" id="0x7f040213" />
    <public type="attr" name="latLngBoundsSouthWestLongitude" id="0x7f040214" />
    <public type="attr" name="layout" id="0x7f040215" />
    <public type="attr" name="layoutDescription" id="0x7f040216" />
    <public type="attr" name="layoutDuringTransition" id="0x7f040217" />
    <public type="attr" name="layoutManager" id="0x7f040218" />
    <public type="attr" name="layout_anchor" id="0x7f040219" />
    <public type="attr" name="layout_anchorGravity" id="0x7f04021a" />
    <public type="attr" name="layout_behavior" id="0x7f04021b" />
    <public type="attr" name="layout_collapseMode" id="0x7f04021c" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f04021d" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f04021e" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f04021f" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f040220" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f040221" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f040222" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f040223" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f040224" />
    <public type="attr" name="layout_constraintCircle" id="0x7f040225" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f040226" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f040227" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f040228" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f040229" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f04022a" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f04022b" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f04022c" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f04022d" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f04022e" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f04022f" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f040230" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f040231" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f040232" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f040233" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f040234" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f040235" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f040236" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f040237" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f040238" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f040239" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f04023a" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f04023b" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f04023c" />
    <public type="attr" name="layout_constraintTag" id="0x7f04023d" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f04023e" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f04023f" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f040240" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f040241" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f040242" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f040243" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f040244" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f040245" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f040246" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f040247" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f040248" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f040249" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f04024a" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f04024b" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f04024c" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f04024d" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f04024e" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f04024f" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f040250" />
    <public type="attr" name="layout_insetEdge" id="0x7f040251" />
    <public type="attr" name="layout_keyline" id="0x7f040252" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f040253" />
    <public type="attr" name="layout_scrollFlags" id="0x7f040254" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f040255" />
    <public type="attr" name="liftOnScroll" id="0x7f040256" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f040257" />
    <public type="attr" name="limitBoundsTo" id="0x7f040258" />
    <public type="attr" name="lineHeight" id="0x7f040259" />
    <public type="attr" name="lineSpacing" id="0x7f04025a" />
    <public type="attr" name="linearProgressIndicatorStyle" id="0x7f04025b" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f04025c" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f04025d" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f04025e" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f04025f" />
    <public type="attr" name="listItemLayout" id="0x7f040260" />
    <public type="attr" name="listLayout" id="0x7f040261" />
    <public type="attr" name="listMenuViewStyle" id="0x7f040262" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f040263" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f040264" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f040265" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f040266" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f040267" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f040268" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f040269" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f04026a" />
    <public type="attr" name="liteMode" id="0x7f04026b" />
    <public type="attr" name="loadingResource" id="0x7f04026c" />
    <public type="attr" name="logo" id="0x7f04026d" />
    <public type="attr" name="logoDescription" id="0x7f04026e" />
    <public type="attr" name="mapId" id="0x7f04026f" />
    <public type="attr" name="mapType" id="0x7f040270" />
    <public type="attr" name="margin_lean_side" id="0x7f040271" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f040272" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f040273" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f040274" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f040275" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f040276" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f040277" />
    <public type="attr" name="materialButtonStyle" id="0x7f040278" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f040279" />
    <public type="attr" name="materialCalendarDay" id="0x7f04027a" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f04027b" />
    <public type="attr" name="materialCalendarHeaderCancelButton" id="0x7f04027c" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f04027d" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f04027e" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f04027f" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f040280" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f040281" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f040282" />
    <public type="attr" name="materialCalendarMonth" id="0x7f040283" />
    <public type="attr" name="materialCalendarMonthNavigationButton" id="0x7f040284" />
    <public type="attr" name="materialCalendarStyle" id="0x7f040285" />
    <public type="attr" name="materialCalendarTheme" id="0x7f040286" />
    <public type="attr" name="materialCalendarYearNavigationButton" id="0x7f040287" />
    <public type="attr" name="materialCardViewStyle" id="0x7f040288" />
    <public type="attr" name="materialCircleRadius" id="0x7f040289" />
    <public type="attr" name="materialClockStyle" id="0x7f04028a" />
    <public type="attr" name="materialThemeOverlay" id="0x7f04028b" />
    <public type="attr" name="materialTimePickerStyle" id="0x7f04028c" />
    <public type="attr" name="materialTimePickerTheme" id="0x7f04028d" />
    <public type="attr" name="maxAcceleration" id="0x7f04028e" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f04028f" />
    <public type="attr" name="maxButtonHeight" id="0x7f040290" />
    <public type="attr" name="maxCharacterCount" id="0x7f040291" />
    <public type="attr" name="maxHeight" id="0x7f040292" />
    <public type="attr" name="maxImageSize" id="0x7f040293" />
    <public type="attr" name="maxLines" id="0x7f040294" />
    <public type="attr" name="maxVelocity" id="0x7f040295" />
    <public type="attr" name="maxWidth" id="0x7f040296" />
    <public type="attr" name="md_background_color" id="0x7f040297" />
    <public type="attr" name="md_button_casing" id="0x7f040298" />
    <public type="attr" name="md_button_selector" id="0x7f040299" />
    <public type="attr" name="md_color_button_text" id="0x7f04029a" />
    <public type="attr" name="md_color_content" id="0x7f04029b" />
    <public type="attr" name="md_color_hint" id="0x7f04029c" />
    <public type="attr" name="md_color_title" id="0x7f04029d" />
    <public type="attr" name="md_color_widget" id="0x7f04029e" />
    <public type="attr" name="md_color_widget_unchecked" id="0x7f04029f" />
    <public type="attr" name="md_corner_radius" id="0x7f0402a0" />
    <public type="attr" name="md_divider_color" id="0x7f0402a1" />
    <public type="attr" name="md_font_body" id="0x7f0402a2" />
    <public type="attr" name="md_font_button" id="0x7f0402a3" />
    <public type="attr" name="md_font_title" id="0x7f0402a4" />
    <public type="attr" name="md_item_selector" id="0x7f0402a5" />
    <public type="attr" name="md_line_spacing_body" id="0x7f0402a6" />
    <public type="attr" name="md_ripple_color" id="0x7f0402a7" />
    <public type="attr" name="measureWithLargestChild" id="0x7f0402a8" />
    <public type="attr" name="menu" id="0x7f0402a9" />
    <public type="attr" name="min" id="0x7f0402aa" />
    <public type="attr" name="minHeight" id="0x7f0402ab" />
    <public type="attr" name="minHideDelay" id="0x7f0402ac" />
    <public type="attr" name="minSeparation" id="0x7f0402ad" />
    <public type="attr" name="minTouchTargetSize" id="0x7f0402ae" />
    <public type="attr" name="minWidth" id="0x7f0402af" />
    <public type="attr" name="mock_diagonalsColor" id="0x7f0402b0" />
    <public type="attr" name="mock_label" id="0x7f0402b1" />
    <public type="attr" name="mock_labelBackgroundColor" id="0x7f0402b2" />
    <public type="attr" name="mock_labelColor" id="0x7f0402b3" />
    <public type="attr" name="mock_showDiagonals" id="0x7f0402b4" />
    <public type="attr" name="mock_showLabel" id="0x7f0402b5" />
    <public type="attr" name="motionDebug" id="0x7f0402b6" />
    <public type="attr" name="motionInterpolator" id="0x7f0402b7" />
    <public type="attr" name="motionPathRotate" id="0x7f0402b8" />
    <public type="attr" name="motionProgress" id="0x7f0402b9" />
    <public type="attr" name="motionStagger" id="0x7f0402ba" />
    <public type="attr" name="motionTarget" id="0x7f0402bb" />
    <public type="attr" name="motion_postLayoutCollision" id="0x7f0402bc" />
    <public type="attr" name="motion_triggerOnCollision" id="0x7f0402bd" />
    <public type="attr" name="moveWhenScrollAtTop" id="0x7f0402be" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f0402bf" />
    <public type="attr" name="navigationContentDescription" id="0x7f0402c0" />
    <public type="attr" name="navigationIcon" id="0x7f0402c1" />
    <public type="attr" name="navigationIconTint" id="0x7f0402c2" />
    <public type="attr" name="navigationMode" id="0x7f0402c3" />
    <public type="attr" name="navigationViewStyle" id="0x7f0402c4" />
    <public type="attr" name="negativeButtonText" id="0x7f0402c5" />
    <public type="attr" name="nestedScrollFlags" id="0x7f0402c6" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f0402c7" />
    <public type="attr" name="nestedScrollable" id="0x7f0402c8" />
    <public type="attr" name="number" id="0x7f0402c9" />
    <public type="attr" name="numericModifiers" id="0x7f0402ca" />
    <public type="attr" name="onCross" id="0x7f0402cb" />
    <public type="attr" name="onHide" id="0x7f0402cc" />
    <public type="attr" name="onNegativeCross" id="0x7f0402cd" />
    <public type="attr" name="onPositiveCross" id="0x7f0402ce" />
    <public type="attr" name="onShow" id="0x7f0402cf" />
    <public type="attr" name="onTouchUp" id="0x7f0402d0" />
    <public type="attr" name="order" id="0x7f0402d1" />
    <public type="attr" name="orderingFromXml" id="0x7f0402d2" />
    <public type="attr" name="overlapAnchor" id="0x7f0402d3" />
    <public type="attr" name="overlay" id="0x7f0402d4" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0402d5" />
    <public type="attr" name="paddingBottomSystemWindowInsets" id="0x7f0402d6" />
    <public type="attr" name="paddingEnd" id="0x7f0402d7" />
    <public type="attr" name="paddingLeftSystemWindowInsets" id="0x7f0402d8" />
    <public type="attr" name="paddingRightSystemWindowInsets" id="0x7f0402d9" />
    <public type="attr" name="paddingStart" id="0x7f0402da" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0402db" />
    <public type="attr" name="panelBackground" id="0x7f0402dc" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0402dd" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0402de" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0402df" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0402e0" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0402e1" />
    <public type="attr" name="passwordToggleTint" id="0x7f0402e2" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f0402e3" />
    <public type="attr" name="pathMotionArc" id="0x7f0402e4" />
    <public type="attr" name="path_percent" id="0x7f0402e5" />
    <public type="attr" name="percentHeight" id="0x7f0402e6" />
    <public type="attr" name="percentWidth" id="0x7f0402e7" />
    <public type="attr" name="percentX" id="0x7f0402e8" />
    <public type="attr" name="percentY" id="0x7f0402e9" />
    <public type="attr" name="perpendicularPath_percent" id="0x7f0402ea" />
    <public type="attr" name="persistent" id="0x7f0402eb" />
    <public type="attr" name="pivotAnchor" id="0x7f0402ec" />
    <public type="attr" name="placeholderActivityName" id="0x7f0402ed" />
    <public type="attr" name="placeholderText" id="0x7f0402ee" />
    <public type="attr" name="placeholderTextAppearance" id="0x7f0402ef" />
    <public type="attr" name="placeholderTextColor" id="0x7f0402f0" />
    <public type="attr" name="placeholder_emptyVisibility" id="0x7f0402f1" />
    <public type="attr" name="played_ad_marker_color" id="0x7f0402f2" />
    <public type="attr" name="played_color" id="0x7f0402f3" />
    <public type="attr" name="player_layout_id" id="0x7f0402f4" />
    <public type="attr" name="popupMenuBackground" id="0x7f0402f5" />
    <public type="attr" name="popupMenuStyle" id="0x7f0402f6" />
    <public type="attr" name="popupTheme" id="0x7f0402f7" />
    <public type="attr" name="popupWindowStyle" id="0x7f0402f8" />
    <public type="attr" name="position" id="0x7f0402f9" />
    <public type="attr" name="positiveButtonText" id="0x7f0402fa" />
    <public type="attr" name="preferenceCategoryStyle" id="0x7f0402fb" />
    <public type="attr" name="preferenceCategoryTitleTextAppearance" id="0x7f0402fc" />
    <public type="attr" name="preferenceCategoryTitleTextColor" id="0x7f0402fd" />
    <public type="attr" name="preferenceFragmentCompatStyle" id="0x7f0402fe" />
    <public type="attr" name="preferenceFragmentListStyle" id="0x7f0402ff" />
    <public type="attr" name="preferenceFragmentStyle" id="0x7f040300" />
    <public type="attr" name="preferenceInformationStyle" id="0x7f040301" />
    <public type="attr" name="preferenceScreenStyle" id="0x7f040302" />
    <public type="attr" name="preferenceStyle" id="0x7f040303" />
    <public type="attr" name="preferenceTheme" id="0x7f040304" />
    <public type="attr" name="prefixText" id="0x7f040305" />
    <public type="attr" name="prefixTextAppearance" id="0x7f040306" />
    <public type="attr" name="prefixTextColor" id="0x7f040307" />
    <public type="attr" name="preserveIconSpacing" id="0x7f040308" />
    <public type="attr" name="pressedTranslationZ" id="0x7f040309" />
    <public type="attr" name="primaryActivityName" id="0x7f04030a" />
    <public type="attr" name="progressBarPadding" id="0x7f04030b" />
    <public type="attr" name="progressBarStyle" id="0x7f04030c" />
    <public type="attr" name="progressMode" id="0x7f04030d" />
    <public type="attr" name="queryBackground" id="0x7f04030e" />
    <public type="attr" name="queryHint" id="0x7f04030f" />
    <public type="attr" name="queryPatterns" id="0x7f040310" />
    <public type="attr" name="radioButtonStyle" id="0x7f040311" />
    <public type="attr" name="rangeFillColor" id="0x7f040312" />
    <public type="attr" name="ratingBarStyle" id="0x7f040313" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f040314" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f040315" />
    <public type="attr" name="rb_color" id="0x7f040316" />
    <public type="attr" name="rb_duration" id="0x7f040317" />
    <public type="attr" name="rb_radius" id="0x7f040318" />
    <public type="attr" name="rb_rippleAmount" id="0x7f040319" />
    <public type="attr" name="rb_scale" id="0x7f04031a" />
    <public type="attr" name="rb_strokeWidth" id="0x7f04031b" />
    <public type="attr" name="rb_type" id="0x7f04031c" />
    <public type="attr" name="recyclerViewStyle" id="0x7f04031d" />
    <public type="attr" name="region_heightLessThan" id="0x7f04031e" />
    <public type="attr" name="region_heightMoreThan" id="0x7f04031f" />
    <public type="attr" name="region_widthLessThan" id="0x7f040320" />
    <public type="attr" name="region_widthMoreThan" id="0x7f040321" />
    <public type="attr" name="repeat_toggle_modes" id="0x7f040322" />
    <public type="attr" name="resize_mode" id="0x7f040323" />
    <public type="attr" name="retryResource" id="0x7f040324" />
    <public type="attr" name="reverseLayout" id="0x7f040325" />
    <public type="attr" name="rippleColor" id="0x7f040326" />
    <public type="attr" name="round" id="0x7f040327" />
    <public type="attr" name="roundPercent" id="0x7f040328" />
    <public type="attr" name="saturation" id="0x7f040329" />
    <public type="attr" name="scopeUris" id="0x7f04032a" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f04032b" />
    <public type="attr" name="scrimBackground" id="0x7f04032c" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f04032d" />
    <public type="attr" name="scrubber_color" id="0x7f04032e" />
    <public type="attr" name="scrubber_disabled_size" id="0x7f04032f" />
    <public type="attr" name="scrubber_dragged_size" id="0x7f040330" />
    <public type="attr" name="scrubber_drawable" id="0x7f040331" />
    <public type="attr" name="scrubber_enabled_size" id="0x7f040332" />
    <public type="attr" name="searchBackIcon" id="0x7f040333" />
    <public type="attr" name="searchBackground" id="0x7f040334" />
    <public type="attr" name="searchClearIcon" id="0x7f040335" />
    <public type="attr" name="searchHintIcon" id="0x7f040336" />
    <public type="attr" name="searchIcon" id="0x7f040337" />
    <public type="attr" name="searchViewStyle" id="0x7f040338" />
    <public type="attr" name="searchVoiceIcon" id="0x7f040339" />
    <public type="attr" name="secondaryActivityAction" id="0x7f04033a" />
    <public type="attr" name="secondaryActivityName" id="0x7f04033b" />
    <public type="attr" name="seekBarIncrement" id="0x7f04033c" />
    <public type="attr" name="seekBarPreferenceStyle" id="0x7f04033d" />
    <public type="attr" name="seekBarStyle" id="0x7f04033e" />
    <public type="attr" name="selectable" id="0x7f04033f" />
    <public type="attr" name="selectableItemBackground" id="0x7f040340" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f040341" />
    <public type="attr" name="selectedDotColor" id="0x7f040342" />
    <public type="attr" name="selectionRequired" id="0x7f040343" />
    <public type="attr" name="selectorSize" id="0x7f040344" />
    <public type="attr" name="shapeAppearance" id="0x7f040345" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f040346" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f040347" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f040348" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f040349" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f04034a" />
    <public type="attr" name="shouldDisableView" id="0x7f04034b" />
    <public type="attr" name="showAnimationBehavior" id="0x7f04034c" />
    <public type="attr" name="showAsAction" id="0x7f04034d" />
    <public type="attr" name="showDelay" id="0x7f04034e" />
    <public type="attr" name="showDividers" id="0x7f04034f" />
    <public type="attr" name="showMotionSpec" id="0x7f040350" />
    <public type="attr" name="showPaths" id="0x7f040351" />
    <public type="attr" name="showSeekBarValue" id="0x7f040352" />
    <public type="attr" name="showText" id="0x7f040353" />
    <public type="attr" name="showTitle" id="0x7f040354" />
    <public type="attr" name="show_buffering" id="0x7f040355" />
    <public type="attr" name="show_fastforward_button" id="0x7f040356" />
    <public type="attr" name="show_next_button" id="0x7f040357" />
    <public type="attr" name="show_previous_button" id="0x7f040358" />
    <public type="attr" name="show_rewind_button" id="0x7f040359" />
    <public type="attr" name="show_shuffle_button" id="0x7f04035a" />
    <public type="attr" name="show_subtitle_button" id="0x7f04035b" />
    <public type="attr" name="show_timeout" id="0x7f04035c" />
    <public type="attr" name="show_vr_button" id="0x7f04035d" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f04035e" />
    <public type="attr" name="shutter_background_color" id="0x7f04035f" />
    <public type="attr" name="side_length" id="0x7f040360" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f040361" />
    <public type="attr" name="singleLine" id="0x7f040362" />
    <public type="attr" name="singleLineTitle" id="0x7f040363" />
    <public type="attr" name="singleSelection" id="0x7f040364" />
    <public type="attr" name="sizePercent" id="0x7f040365" />
    <public type="attr" name="sliderStyle" id="0x7f040366" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f040367" />
    <public type="attr" name="snackbarStyle" id="0x7f040368" />
    <public type="attr" name="snackbarTextViewStyle" id="0x7f040369" />
    <public type="attr" name="spanCount" id="0x7f04036a" />
    <public type="attr" name="spinBars" id="0x7f04036b" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f04036c" />
    <public type="attr" name="spinnerStyle" id="0x7f04036d" />
    <public type="attr" name="spinner_arrow_animate" id="0x7f04036e" />
    <public type="attr" name="spinner_arrow_animate_duration" id="0x7f04036f" />
    <public type="attr" name="spinner_arrow_drawable" id="0x7f040370" />
    <public type="attr" name="spinner_arrow_gravity" id="0x7f040371" />
    <public type="attr" name="spinner_arrow_padding" id="0x7f040372" />
    <public type="attr" name="spinner_arrow_show" id="0x7f040373" />
    <public type="attr" name="spinner_arrow_tint" id="0x7f040374" />
    <public type="attr" name="spinner_debounce_duration" id="0x7f040375" />
    <public type="attr" name="spinner_dismiss_notified_select" id="0x7f040376" />
    <public type="attr" name="spinner_divider_color" id="0x7f040377" />
    <public type="attr" name="spinner_divider_show" id="0x7f040378" />
    <public type="attr" name="spinner_divider_size" id="0x7f040379" />
    <public type="attr" name="spinner_item_array" id="0x7f04037a" />
    <public type="attr" name="spinner_item_height" id="0x7f04037b" />
    <public type="attr" name="spinner_popup_animation" id="0x7f04037c" />
    <public type="attr" name="spinner_popup_animation_style" id="0x7f04037d" />
    <public type="attr" name="spinner_popup_background" id="0x7f04037e" />
    <public type="attr" name="spinner_popup_bottom_padding" id="0x7f04037f" />
    <public type="attr" name="spinner_popup_elevation" id="0x7f040380" />
    <public type="attr" name="spinner_popup_end_padding" id="0x7f040381" />
    <public type="attr" name="spinner_popup_focusable" id="0x7f040382" />
    <public type="attr" name="spinner_popup_height" id="0x7f040383" />
    <public type="attr" name="spinner_popup_max_height" id="0x7f040384" />
    <public type="attr" name="spinner_popup_padding" id="0x7f040385" />
    <public type="attr" name="spinner_popup_start_padding" id="0x7f040386" />
    <public type="attr" name="spinner_popup_top_padding" id="0x7f040387" />
    <public type="attr" name="spinner_popup_width" id="0x7f040388" />
    <public type="attr" name="spinner_preference_name" id="0x7f040389" />
    <public type="attr" name="spinner_selected_item_background" id="0x7f04038a" />
    <public type="attr" name="splitLayoutDirection" id="0x7f04038b" />
    <public type="attr" name="splitMinSmallestWidth" id="0x7f04038c" />
    <public type="attr" name="splitMinWidth" id="0x7f04038d" />
    <public type="attr" name="splitRatio" id="0x7f04038e" />
    <public type="attr" name="splitTrack" id="0x7f04038f" />
    <public type="attr" name="srb_clearRatingEnabled" id="0x7f040390" />
    <public type="attr" name="srb_clickable" id="0x7f040391" />
    <public type="attr" name="srb_drawableEmpty" id="0x7f040392" />
    <public type="attr" name="srb_drawableFilled" id="0x7f040393" />
    <public type="attr" name="srb_isIndicator" id="0x7f040394" />
    <public type="attr" name="srb_minimumStars" id="0x7f040395" />
    <public type="attr" name="srb_numStars" id="0x7f040396" />
    <public type="attr" name="srb_rating" id="0x7f040397" />
    <public type="attr" name="srb_scrollable" id="0x7f040398" />
    <public type="attr" name="srb_starHeight" id="0x7f040399" />
    <public type="attr" name="srb_starPadding" id="0x7f04039a" />
    <public type="attr" name="srb_starWidth" id="0x7f04039b" />
    <public type="attr" name="srb_stepSize" id="0x7f04039c" />
    <public type="attr" name="srcCompat" id="0x7f04039d" />
    <public type="attr" name="stackFromEnd" id="0x7f04039e" />
    <public type="attr" name="staggered" id="0x7f04039f" />
    <public type="attr" name="startIconCheckable" id="0x7f0403a0" />
    <public type="attr" name="startIconContentDescription" id="0x7f0403a1" />
    <public type="attr" name="startIconDrawable" id="0x7f0403a2" />
    <public type="attr" name="startIconTint" id="0x7f0403a3" />
    <public type="attr" name="startIconTintMode" id="0x7f0403a4" />
    <public type="attr" name="state_above_anchor" id="0x7f0403a5" />
    <public type="attr" name="state_collapsed" id="0x7f0403a6" />
    <public type="attr" name="state_collapsible" id="0x7f0403a7" />
    <public type="attr" name="state_dragged" id="0x7f0403a8" />
    <public type="attr" name="state_liftable" id="0x7f0403a9" />
    <public type="attr" name="state_lifted" id="0x7f0403aa" />
    <public type="attr" name="statusBarBackground" id="0x7f0403ab" />
    <public type="attr" name="statusBarForeground" id="0x7f0403ac" />
    <public type="attr" name="statusBarScrim" id="0x7f0403ad" />
    <public type="attr" name="stiffness" id="0x7f0403ae" />
    <public type="attr" name="strokeColor" id="0x7f0403af" />
    <public type="attr" name="strokeWidth" id="0x7f0403b0" />
    <public type="attr" name="subMenuArrow" id="0x7f0403b1" />
    <public type="attr" name="submitBackground" id="0x7f0403b2" />
    <public type="attr" name="subtitle" id="0x7f0403b3" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f0403b4" />
    <public type="attr" name="subtitleTextColor" id="0x7f0403b5" />
    <public type="attr" name="subtitleTextStyle" id="0x7f0403b6" />
    <public type="attr" name="suffixText" id="0x7f0403b7" />
    <public type="attr" name="suffixTextAppearance" id="0x7f0403b8" />
    <public type="attr" name="suffixTextColor" id="0x7f0403b9" />
    <public type="attr" name="suggestionRowLayout" id="0x7f0403ba" />
    <public type="attr" name="summary" id="0x7f0403bb" />
    <public type="attr" name="summaryOff" id="0x7f0403bc" />
    <public type="attr" name="summaryOn" id="0x7f0403bd" />
    <public type="attr" name="surface_type" id="0x7f0403be" />
    <public type="attr" name="switchMinWidth" id="0x7f0403bf" />
    <public type="attr" name="switchPadding" id="0x7f0403c0" />
    <public type="attr" name="switchPreferenceCompatStyle" id="0x7f0403c1" />
    <public type="attr" name="switchPreferenceStyle" id="0x7f0403c2" />
    <public type="attr" name="switchStyle" id="0x7f0403c3" />
    <public type="attr" name="switchTextAppearance" id="0x7f0403c4" />
    <public type="attr" name="switchTextOff" id="0x7f0403c5" />
    <public type="attr" name="switchTextOn" id="0x7f0403c6" />
    <public type="attr" name="tabBackground" id="0x7f0403c7" />
    <public type="attr" name="tabContentStart" id="0x7f0403c8" />
    <public type="attr" name="tabGravity" id="0x7f0403c9" />
    <public type="attr" name="tabIconTint" id="0x7f0403ca" />
    <public type="attr" name="tabIconTintMode" id="0x7f0403cb" />
    <public type="attr" name="tabIndicator" id="0x7f0403cc" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f0403cd" />
    <public type="attr" name="tabIndicatorAnimationMode" id="0x7f0403ce" />
    <public type="attr" name="tabIndicatorColor" id="0x7f0403cf" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f0403d0" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f0403d1" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f0403d2" />
    <public type="attr" name="tabInlineLabel" id="0x7f0403d3" />
    <public type="attr" name="tabMaxWidth" id="0x7f0403d4" />
    <public type="attr" name="tabMinWidth" id="0x7f0403d5" />
    <public type="attr" name="tabMode" id="0x7f0403d6" />
    <public type="attr" name="tabPadding" id="0x7f0403d7" />
    <public type="attr" name="tabPaddingBottom" id="0x7f0403d8" />
    <public type="attr" name="tabPaddingEnd" id="0x7f0403d9" />
    <public type="attr" name="tabPaddingStart" id="0x7f0403da" />
    <public type="attr" name="tabPaddingTop" id="0x7f0403db" />
    <public type="attr" name="tabRippleColor" id="0x7f0403dc" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f0403dd" />
    <public type="attr" name="tabStyle" id="0x7f0403de" />
    <public type="attr" name="tabTextAppearance" id="0x7f0403df" />
    <public type="attr" name="tabTextColor" id="0x7f0403e0" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f0403e1" />
    <public type="attr" name="targetId" id="0x7f0403e2" />
    <public type="attr" name="telltales_tailColor" id="0x7f0403e3" />
    <public type="attr" name="telltales_tailScale" id="0x7f0403e4" />
    <public type="attr" name="telltales_velocityMode" id="0x7f0403e5" />
    <public type="attr" name="text" id="0x7f0403e6" />
    <public type="attr" name="textAllCaps" id="0x7f0403e7" />
    <public type="attr" name="textAppearanceBody1" id="0x7f0403e8" />
    <public type="attr" name="textAppearanceBody2" id="0x7f0403e9" />
    <public type="attr" name="textAppearanceButton" id="0x7f0403ea" />
    <public type="attr" name="textAppearanceCaption" id="0x7f0403eb" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f0403ec" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f0403ed" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f0403ee" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f0403ef" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f0403f0" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f0403f1" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f0403f2" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f0403f3" />
    <public type="attr" name="textAppearanceListItem" id="0x7f0403f4" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f0403f5" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f0403f6" />
    <public type="attr" name="textAppearanceOverline" id="0x7f0403f7" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f0403f8" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f0403f9" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f0403fa" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f0403fb" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f0403fc" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f0403fd" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f0403fe" />
    <public type="attr" name="textColorSearchUrl" id="0x7f0403ff" />
    <public type="attr" name="textEndPadding" id="0x7f040400" />
    <public type="attr" name="textInputLayoutFocusedRectEnabled" id="0x7f040401" />
    <public type="attr" name="textInputStyle" id="0x7f040402" />
    <public type="attr" name="textLocale" id="0x7f040403" />
    <public type="attr" name="textStartPadding" id="0x7f040404" />
    <public type="attr" name="text_color" id="0x7f040405" />
    <public type="attr" name="text_size" id="0x7f040406" />
    <public type="attr" name="theme" id="0x7f040407" />
    <public type="attr" name="themeLineHeight" id="0x7f040408" />
    <public type="attr" name="thickness" id="0x7f040409" />
    <public type="attr" name="thumbColor" id="0x7f04040a" />
    <public type="attr" name="thumbElevation" id="0x7f04040b" />
    <public type="attr" name="thumbRadius" id="0x7f04040c" />
    <public type="attr" name="thumbStrokeColor" id="0x7f04040d" />
    <public type="attr" name="thumbStrokeWidth" id="0x7f04040e" />
    <public type="attr" name="thumbTextPadding" id="0x7f04040f" />
    <public type="attr" name="thumbTint" id="0x7f040410" />
    <public type="attr" name="thumbTintMode" id="0x7f040411" />
    <public type="attr" name="tickColor" id="0x7f040412" />
    <public type="attr" name="tickColorActive" id="0x7f040413" />
    <public type="attr" name="tickColorInactive" id="0x7f040414" />
    <public type="attr" name="tickMark" id="0x7f040415" />
    <public type="attr" name="tickMarkTint" id="0x7f040416" />
    <public type="attr" name="tickMarkTintMode" id="0x7f040417" />
    <public type="attr" name="tickVisible" id="0x7f040418" />
    <public type="attr" name="time_bar_min_update_interval" id="0x7f040419" />
    <public type="attr" name="tint" id="0x7f04041a" />
    <public type="attr" name="tintMode" id="0x7f04041b" />
    <public type="attr" name="title" id="0x7f04041c" />
    <public type="attr" name="titleEnabled" id="0x7f04041d" />
    <public type="attr" name="titleMargin" id="0x7f04041e" />
    <public type="attr" name="titleMarginBottom" id="0x7f04041f" />
    <public type="attr" name="titleMarginEnd" id="0x7f040420" />
    <public type="attr" name="titleMarginStart" id="0x7f040421" />
    <public type="attr" name="titleMarginTop" id="0x7f040422" />
    <public type="attr" name="titleMargins" id="0x7f040423" />
    <public type="attr" name="titleTextAppearance" id="0x7f040424" />
    <public type="attr" name="titleTextColor" id="0x7f040425" />
    <public type="attr" name="titleTextStyle" id="0x7f040426" />
    <public type="attr" name="toolbarId" id="0x7f040427" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f040428" />
    <public type="attr" name="toolbarStyle" id="0x7f040429" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f04042a" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f04042b" />
    <public type="attr" name="tooltipStyle" id="0x7f04042c" />
    <public type="attr" name="tooltipText" id="0x7f04042d" />
    <public type="attr" name="touchAnchorId" id="0x7f04042e" />
    <public type="attr" name="touchAnchorSide" id="0x7f04042f" />
    <public type="attr" name="touchRegionId" id="0x7f040430" />
    <public type="attr" name="touch_target_height" id="0x7f040431" />
    <public type="attr" name="track" id="0x7f040432" />
    <public type="attr" name="trackColor" id="0x7f040433" />
    <public type="attr" name="trackColorActive" id="0x7f040434" />
    <public type="attr" name="trackColorInactive" id="0x7f040435" />
    <public type="attr" name="trackCornerRadius" id="0x7f040436" />
    <public type="attr" name="trackHeight" id="0x7f040437" />
    <public type="attr" name="trackThickness" id="0x7f040438" />
    <public type="attr" name="trackTint" id="0x7f040439" />
    <public type="attr" name="trackTintMode" id="0x7f04043a" />
    <public type="attr" name="transitionDisable" id="0x7f04043b" />
    <public type="attr" name="transitionEasing" id="0x7f04043c" />
    <public type="attr" name="transitionFlags" id="0x7f04043d" />
    <public type="attr" name="transitionPathRotate" id="0x7f04043e" />
    <public type="attr" name="transitionShapeAppearance" id="0x7f04043f" />
    <public type="attr" name="triggerId" id="0x7f040440" />
    <public type="attr" name="triggerReceiver" id="0x7f040441" />
    <public type="attr" name="triggerSlack" id="0x7f040442" />
    <public type="attr" name="ttcIndex" id="0x7f040443" />
    <public type="attr" name="type" id="0x7f040444" />
    <public type="attr" name="uiCompass" id="0x7f040445" />
    <public type="attr" name="uiMapToolbar" id="0x7f040446" />
    <public type="attr" name="uiRotateGestures" id="0x7f040447" />
    <public type="attr" name="uiScrollGestures" id="0x7f040448" />
    <public type="attr" name="uiScrollGesturesDuringRotateOrZoom" id="0x7f040449" />
    <public type="attr" name="uiTiltGestures" id="0x7f04044a" />
    <public type="attr" name="uiZoomControls" id="0x7f04044b" />
    <public type="attr" name="uiZoomGestures" id="0x7f04044c" />
    <public type="attr" name="unplayed_color" id="0x7f04044d" />
    <public type="attr" name="updatesContinuously" id="0x7f04044e" />
    <public type="attr" name="useCompatPadding" id="0x7f04044f" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f040450" />
    <public type="attr" name="useSimpleSummaryProvider" id="0x7f040451" />
    <public type="attr" name="useViewLifecycle" id="0x7f040452" />
    <public type="attr" name="use_artwork" id="0x7f040453" />
    <public type="attr" name="use_controller" id="0x7f040454" />
    <public type="attr" name="values" id="0x7f040455" />
    <public type="attr" name="verticalOffset" id="0x7f040456" />
    <public type="attr" name="viewInflaterClass" id="0x7f040457" />
    <public type="attr" name="visibilityMode" id="0x7f040458" />
    <public type="attr" name="voiceIcon" id="0x7f040459" />
    <public type="attr" name="voiceSearch" id="0x7f04045a" />
    <public type="attr" name="voiceSearchPrompt" id="0x7f04045b" />
    <public type="attr" name="warmth" id="0x7f04045c" />
    <public type="attr" name="waveDecay" id="0x7f04045d" />
    <public type="attr" name="waveOffset" id="0x7f04045e" />
    <public type="attr" name="wavePeriod" id="0x7f04045f" />
    <public type="attr" name="waveShape" id="0x7f040460" />
    <public type="attr" name="waveVariesBy" id="0x7f040461" />
    <public type="attr" name="widgetLayout" id="0x7f040462" />
    <public type="attr" name="windowActionBar" id="0x7f040463" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f040464" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f040465" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f040466" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f040467" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f040468" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f040469" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f04046a" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f04046b" />
    <public type="attr" name="windowNoTitle" id="0x7f04046c" />
    <public type="attr" name="yearSelectedStyle" id="0x7f04046d" />
    <public type="attr" name="yearStyle" id="0x7f04046e" />
    <public type="attr" name="yearTodayStyle" id="0x7f04046f" />
    <public type="attr" name="zOrderOnTop" id="0x7f040470" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f050000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f050001" />
    <public type="bool" name="config_materialPreferenceIconSpaceReserved" id="0x7f050002" />
    <public type="bool" name="enable_system_alarm_service_default" id="0x7f050003" />
    <public type="bool" name="enable_system_foreground_service_default" id="0x7f050004" />
    <public type="bool" name="enable_system_job_service_default" id="0x7f050005" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f050006" />
    <public type="bool" name="workmanager_test_configuration" id="0x7f050007" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f060000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f060001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f060002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f060003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f060004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f060005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f060006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f060007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f060008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f060009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f06000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f06000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f06000c" />
    <public type="color" name="abc_search_url_text" id="0x7f06000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f06000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f06000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f060010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f060011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f060012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f060013" />
    <public type="color" name="abc_tint_default" id="0x7f060014" />
    <public type="color" name="abc_tint_edittext" id="0x7f060015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f060016" />
    <public type="color" name="abc_tint_spinner" id="0x7f060017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f060018" />
    <public type="color" name="accent" id="0x7f060019" />
    <public type="color" name="accent_material_dark" id="0x7f06001a" />
    <public type="color" name="accent_material_light" id="0x7f06001b" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f06001c" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f06001d" />
    <public type="color" name="background" id="0x7f06001e" />
    <public type="color" name="background_floating_material_dark" id="0x7f06001f" />
    <public type="color" name="background_floating_material_light" id="0x7f060020" />
    <public type="color" name="background_material_dark" id="0x7f060021" />
    <public type="color" name="background_material_light" id="0x7f060022" />
    <public type="color" name="black" id="0x7f060023" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f060024" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f060025" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f060026" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f060027" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f060028" />
    <public type="color" name="bright_foreground_material_light" id="0x7f060029" />
    <public type="color" name="browser_actions_bg_grey" id="0x7f06002a" />
    <public type="color" name="browser_actions_divider_color" id="0x7f06002b" />
    <public type="color" name="browser_actions_text_color" id="0x7f06002c" />
    <public type="color" name="browser_actions_title_color" id="0x7f06002d" />
    <public type="color" name="button_material_dark" id="0x7f06002e" />
    <public type="color" name="button_material_light" id="0x7f06002f" />
    <public type="color" name="cardview_dark_background" id="0x7f060030" />
    <public type="color" name="cardview_light_background" id="0x7f060031" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f060032" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f060033" />
    <public type="color" name="checkbox_themeable_attribute_color" id="0x7f060034" />
    <public type="color" name="common_google_signin_btn_text_dark" id="0x7f060035" />
    <public type="color" name="common_google_signin_btn_text_dark_default" id="0x7f060036" />
    <public type="color" name="common_google_signin_btn_text_dark_disabled" id="0x7f060037" />
    <public type="color" name="common_google_signin_btn_text_dark_focused" id="0x7f060038" />
    <public type="color" name="common_google_signin_btn_text_dark_pressed" id="0x7f060039" />
    <public type="color" name="common_google_signin_btn_text_light" id="0x7f06003a" />
    <public type="color" name="common_google_signin_btn_text_light_default" id="0x7f06003b" />
    <public type="color" name="common_google_signin_btn_text_light_disabled" id="0x7f06003c" />
    <public type="color" name="common_google_signin_btn_text_light_focused" id="0x7f06003d" />
    <public type="color" name="common_google_signin_btn_text_light_pressed" id="0x7f06003e" />
    <public type="color" name="common_google_signin_btn_tint" id="0x7f06003f" />
    <public type="color" name="default_textColor" id="0x7f060040" />
    <public type="color" name="default_textColorHint" id="0x7f060041" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f060042" />
    <public type="color" name="design_box_stroke_color" id="0x7f060043" />
    <public type="color" name="design_dark_default_color_background" id="0x7f060044" />
    <public type="color" name="design_dark_default_color_error" id="0x7f060045" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f060046" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f060047" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f060048" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f060049" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f06004a" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f06004b" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f06004c" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f06004d" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f06004e" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f06004f" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f060050" />
    <public type="color" name="design_default_color_background" id="0x7f060051" />
    <public type="color" name="design_default_color_error" id="0x7f060052" />
    <public type="color" name="design_default_color_on_background" id="0x7f060053" />
    <public type="color" name="design_default_color_on_error" id="0x7f060054" />
    <public type="color" name="design_default_color_on_primary" id="0x7f060055" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f060056" />
    <public type="color" name="design_default_color_on_surface" id="0x7f060057" />
    <public type="color" name="design_default_color_primary" id="0x7f060058" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f060059" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f06005a" />
    <public type="color" name="design_default_color_secondary" id="0x7f06005b" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f06005c" />
    <public type="color" name="design_default_color_surface" id="0x7f06005d" />
    <public type="color" name="design_error" id="0x7f06005e" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f06005f" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f060060" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f060061" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f060062" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f060063" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f060064" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f060065" />
    <public type="color" name="design_icon_tint" id="0x7f060066" />
    <public type="color" name="design_snackbar_background_color" id="0x7f060067" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f060068" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f060069" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f06006a" />
    <public type="color" name="dim_foreground_material_light" id="0x7f06006b" />
    <public type="color" name="error_color_material_dark" id="0x7f06006c" />
    <public type="color" name="error_color_material_light" id="0x7f06006d" />
    <public type="color" name="exo_black_opacity_60" id="0x7f06006e" />
    <public type="color" name="exo_black_opacity_70" id="0x7f06006f" />
    <public type="color" name="exo_bottom_bar_background" id="0x7f060070" />
    <public type="color" name="exo_edit_mode_background_color" id="0x7f060071" />
    <public type="color" name="exo_error_message_background_color" id="0x7f060072" />
    <public type="color" name="exo_styled_error_message_background" id="0x7f060073" />
    <public type="color" name="exo_white" id="0x7f060074" />
    <public type="color" name="exo_white_opacity_70" id="0x7f060075" />
    <public type="color" name="eyelid" id="0x7f060076" />
    <public type="color" name="foreground_material_dark" id="0x7f060077" />
    <public type="color" name="foreground_material_light" id="0x7f060078" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f060079" />
    <public type="color" name="highlighted_text_material_light" id="0x7f06007a" />
    <public type="color" name="material_blue_grey_800" id="0x7f06007b" />
    <public type="color" name="material_blue_grey_900" id="0x7f06007c" />
    <public type="color" name="material_blue_grey_950" id="0x7f06007d" />
    <public type="color" name="material_cursor_color" id="0x7f06007e" />
    <public type="color" name="material_deep_teal_200" id="0x7f06007f" />
    <public type="color" name="material_deep_teal_500" id="0x7f060080" />
    <public type="color" name="material_grey_100" id="0x7f060081" />
    <public type="color" name="material_grey_300" id="0x7f060082" />
    <public type="color" name="material_grey_50" id="0x7f060083" />
    <public type="color" name="material_grey_600" id="0x7f060084" />
    <public type="color" name="material_grey_800" id="0x7f060085" />
    <public type="color" name="material_grey_850" id="0x7f060086" />
    <public type="color" name="material_grey_900" id="0x7f060087" />
    <public type="color" name="material_on_background_disabled" id="0x7f060088" />
    <public type="color" name="material_on_background_emphasis_high_type" id="0x7f060089" />
    <public type="color" name="material_on_background_emphasis_medium" id="0x7f06008a" />
    <public type="color" name="material_on_primary_disabled" id="0x7f06008b" />
    <public type="color" name="material_on_primary_emphasis_high_type" id="0x7f06008c" />
    <public type="color" name="material_on_primary_emphasis_medium" id="0x7f06008d" />
    <public type="color" name="material_on_surface_disabled" id="0x7f06008e" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f06008f" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f060090" />
    <public type="color" name="material_on_surface_stroke" id="0x7f060091" />
    <public type="color" name="material_slider_active_tick_marks_color" id="0x7f060092" />
    <public type="color" name="material_slider_active_track_color" id="0x7f060093" />
    <public type="color" name="material_slider_halo_color" id="0x7f060094" />
    <public type="color" name="material_slider_inactive_tick_marks_color" id="0x7f060095" />
    <public type="color" name="material_slider_inactive_track_color" id="0x7f060096" />
    <public type="color" name="material_slider_thumb_color" id="0x7f060097" />
    <public type="color" name="material_timepicker_button_background" id="0x7f060098" />
    <public type="color" name="material_timepicker_button_stroke" id="0x7f060099" />
    <public type="color" name="material_timepicker_clock_text_color" id="0x7f06009a" />
    <public type="color" name="material_timepicker_clockface" id="0x7f06009b" />
    <public type="color" name="material_timepicker_modebutton_tint" id="0x7f06009c" />
    <public type="color" name="md_btn_selected" id="0x7f06009d" />
    <public type="color" name="md_btn_selected_dark" id="0x7f06009e" />
    <public type="color" name="md_disabled_text_dark_theme" id="0x7f06009f" />
    <public type="color" name="md_disabled_text_light_theme" id="0x7f0600a0" />
    <public type="color" name="md_divider_dark_theme" id="0x7f0600a1" />
    <public type="color" name="md_divider_light_theme" id="0x7f0600a2" />
    <public type="color" name="md_list_item_textcolor" id="0x7f0600a3" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f0600a4" />
    <public type="color" name="mtrl_bottom_nav_colored_ripple_color" id="0x7f0600a5" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f0600a6" />
    <public type="color" name="mtrl_bottom_nav_ripple_color" id="0x7f0600a7" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f0600a8" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f0600a9" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f0600aa" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f0600ab" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f0600ac" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f0600ad" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f0600ae" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f0600af" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f0600b0" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f0600b1" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f0600b2" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f0600b3" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f0600b4" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f0600b5" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f0600b6" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f0600b7" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f0600b8" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f0600b9" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f0600ba" />
    <public type="color" name="mtrl_error" id="0x7f0600bb" />
    <public type="color" name="mtrl_fab_bg_color_selector" id="0x7f0600bc" />
    <public type="color" name="mtrl_fab_icon_text_color_selector" id="0x7f0600bd" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f0600be" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f0600bf" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f0600c0" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f0600c1" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f0600c2" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f0600c3" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f0600c4" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f0600c5" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f0600c6" />
    <public type="color" name="mtrl_on_surface_ripple_color" id="0x7f0600c7" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f0600c8" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f0600c9" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f0600ca" />
    <public type="color" name="mtrl_scrim_color" id="0x7f0600cb" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f0600cc" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f0600cd" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f0600ce" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f0600cf" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f0600d0" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f0600d1" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f0600d2" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f0600d3" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f0600d4" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f0600d5" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f0600d6" />
    <public type="color" name="notification_action_color_filter" id="0x7f0600d7" />
    <public type="color" name="notification_icon_bg_color" id="0x7f0600d8" />
    <public type="color" name="notification_material_background_media_default_color" id="0x7f0600d9" />
    <public type="color" name="powerspinner_scrollbar" id="0x7f0600da" />
    <public type="color" name="preference_fallback_accent_color" id="0x7f0600db" />
    <public type="color" name="primary" id="0x7f0600dc" />
    <public type="color" name="primary_dark" id="0x7f0600dd" />
    <public type="color" name="primary_dark_material_dark" id="0x7f0600de" />
    <public type="color" name="primary_dark_material_light" id="0x7f0600df" />
    <public type="color" name="primary_light" id="0x7f0600e0" />
    <public type="color" name="primary_material_dark" id="0x7f0600e1" />
    <public type="color" name="primary_material_light" id="0x7f0600e2" />
    <public type="color" name="primary_text" id="0x7f0600e3" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f0600e4" />
    <public type="color" name="primary_text_default_material_light" id="0x7f0600e5" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f0600e6" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f0600e7" />
    <public type="color" name="radiobutton_themeable_attribute_color" id="0x7f0600e8" />
    <public type="color" name="rippelColor" id="0x7f0600e9" />
    <public type="color" name="ripple_material_dark" id="0x7f0600ea" />
    <public type="color" name="ripple_material_light" id="0x7f0600eb" />
    <public type="color" name="secondary_text" id="0x7f0600ec" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f0600ed" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f0600ee" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f0600ef" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f0600f0" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f0600f1" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f0600f2" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f0600f3" />
    <public type="color" name="switch_thumb_material_light" id="0x7f0600f4" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f0600f5" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f0600f6" />
    <public type="color" name="test_mtrl_calendar_day" id="0x7f0600f7" />
    <public type="color" name="test_mtrl_calendar_day_selected" id="0x7f0600f8" />
    <public type="color" name="text_primary" id="0x7f0600f9" />
    <public type="color" name="tooltip_background_dark" id="0x7f0600fa" />
    <public type="color" name="tooltip_background_light" id="0x7f0600fb" />
    <public type="color" name="white" id="0x7f0600fc" />
    <public type="color" name="yellow" id="0x7f0600fd" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f070000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f070001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f070002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f070003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f070004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f070005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f070006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f070007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f070008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f070009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f07000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f07000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f07000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f07000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f07000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f07000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f070010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f070011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f070012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f070013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f070014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f070015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f070016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f070017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f070018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f070019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f07001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f07001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f07001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f07001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f07001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f07001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f070020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f070021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f070022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f070023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f070024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f070025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f070026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f070027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f070028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f070029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f07002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f07002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f07002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f07002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f07002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f07002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f070030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f070031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f070032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f070033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f070034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f070035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f070036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f070037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f070038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f070039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f07003a" />
    <public type="dimen" name="abc_star_big" id="0x7f07003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f07003c" />
    <public type="dimen" name="abc_star_small" id="0x7f07003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f07003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f07003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f070040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f070041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f070042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f070043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f070044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f070045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f070046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f070047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f070048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f070049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f07004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f07004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f07004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f07004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f07004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f07004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f070050" />
    <public type="dimen" name="action_bar_size" id="0x7f070051" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f070052" />
    <public type="dimen" name="browser_actions_context_menu_max_width" id="0x7f070053" />
    <public type="dimen" name="browser_actions_context_menu_min_padding" id="0x7f070054" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f070055" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f070056" />
    <public type="dimen" name="cardview_default_radius" id="0x7f070057" />
    <public type="dimen" name="clock_face_margin_start" id="0x7f070058" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f070059" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f07005a" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f07005b" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f07005c" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f07005d" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f07005e" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f07005f" />
    <public type="dimen" name="default_dimension" id="0x7f070060" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f070061" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f070062" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f070063" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f070064" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f070065" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f070066" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f070067" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f070068" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f070069" />
    <public type="dimen" name="design_bottom_navigation_label_padding" id="0x7f07006a" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f07006b" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f07006c" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f07006d" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f07006e" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f07006f" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f070070" />
    <public type="dimen" name="design_fab_border_width" id="0x7f070071" />
    <public type="dimen" name="design_fab_elevation" id="0x7f070072" />
    <public type="dimen" name="design_fab_image_size" id="0x7f070073" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f070074" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f070075" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f070076" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f070077" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f070078" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f070079" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f07007a" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f07007b" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f07007c" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f07007d" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f07007e" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f07007f" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f070080" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f070081" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f070082" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f070083" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f070084" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f070085" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f070086" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f070087" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f070088" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f070089" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f07008a" />
    <public type="dimen" name="design_tab_max_width" id="0x7f07008b" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f07008c" />
    <public type="dimen" name="design_tab_text_size" id="0x7f07008d" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f07008e" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f07008f" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f070090" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f070091" />
    <public type="dimen" name="exo_error_message_height" id="0x7f070092" />
    <public type="dimen" name="exo_error_message_margin_bottom" id="0x7f070093" />
    <public type="dimen" name="exo_error_message_text_padding_horizontal" id="0x7f070094" />
    <public type="dimen" name="exo_error_message_text_padding_vertical" id="0x7f070095" />
    <public type="dimen" name="exo_error_message_text_size" id="0x7f070096" />
    <public type="dimen" name="exo_icon_horizontal_margin" id="0x7f070097" />
    <public type="dimen" name="exo_icon_padding" id="0x7f070098" />
    <public type="dimen" name="exo_icon_padding_bottom" id="0x7f070099" />
    <public type="dimen" name="exo_icon_size" id="0x7f07009a" />
    <public type="dimen" name="exo_icon_text_size" id="0x7f07009b" />
    <public type="dimen" name="exo_media_button_height" id="0x7f07009c" />
    <public type="dimen" name="exo_media_button_width" id="0x7f07009d" />
    <public type="dimen" name="exo_setting_width" id="0x7f07009e" />
    <public type="dimen" name="exo_settings_height" id="0x7f07009f" />
    <public type="dimen" name="exo_settings_icon_size" id="0x7f0700a0" />
    <public type="dimen" name="exo_settings_main_text_size" id="0x7f0700a1" />
    <public type="dimen" name="exo_settings_offset" id="0x7f0700a2" />
    <public type="dimen" name="exo_settings_sub_text_size" id="0x7f0700a3" />
    <public type="dimen" name="exo_settings_text_height" id="0x7f0700a4" />
    <public type="dimen" name="exo_small_icon_height" id="0x7f0700a5" />
    <public type="dimen" name="exo_small_icon_horizontal_margin" id="0x7f0700a6" />
    <public type="dimen" name="exo_small_icon_padding_horizontal" id="0x7f0700a7" />
    <public type="dimen" name="exo_small_icon_padding_vertical" id="0x7f0700a8" />
    <public type="dimen" name="exo_small_icon_width" id="0x7f0700a9" />
    <public type="dimen" name="exo_styled_bottom_bar_height" id="0x7f0700aa" />
    <public type="dimen" name="exo_styled_bottom_bar_margin_top" id="0x7f0700ab" />
    <public type="dimen" name="exo_styled_bottom_bar_time_padding" id="0x7f0700ac" />
    <public type="dimen" name="exo_styled_controls_padding" id="0x7f0700ad" />
    <public type="dimen" name="exo_styled_minimal_controls_margin_bottom" id="0x7f0700ae" />
    <public type="dimen" name="exo_styled_progress_bar_height" id="0x7f0700af" />
    <public type="dimen" name="exo_styled_progress_dragged_thumb_size" id="0x7f0700b0" />
    <public type="dimen" name="exo_styled_progress_enabled_thumb_size" id="0x7f0700b1" />
    <public type="dimen" name="exo_styled_progress_layout_height" id="0x7f0700b2" />
    <public type="dimen" name="exo_styled_progress_margin_bottom" id="0x7f0700b3" />
    <public type="dimen" name="exo_styled_progress_touch_target_height" id="0x7f0700b4" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f0700b5" />
    <public type="dimen" name="fastscroll_margin" id="0x7f0700b6" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f0700b7" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f0700b8" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f0700b9" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f0700ba" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f0700bb" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f0700bc" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f0700bd" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f0700be" />
    <public type="dimen" name="hk_noti_max_height" id="0x7f0700bf" />
    <public type="dimen" name="hk_noti_mid_height" id="0x7f0700c0" />
    <public type="dimen" name="hk_noti_min_height" id="0x7f0700c1" />
    <public type="dimen" name="hk_noti_padding" id="0x7f0700c2" />
    <public type="dimen" name="hk_noti_panel_width" id="0x7f0700c3" />
    <public type="dimen" name="hk_noti_side_padding" id="0x7f0700c4" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f0700c5" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f0700c6" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f0700c7" />
    <public type="dimen" name="material_clock_display_padding" id="0x7f0700c8" />
    <public type="dimen" name="material_clock_face_margin_top" id="0x7f0700c9" />
    <public type="dimen" name="material_clock_hand_center_dot_radius" id="0x7f0700ca" />
    <public type="dimen" name="material_clock_hand_padding" id="0x7f0700cb" />
    <public type="dimen" name="material_clock_hand_stroke_width" id="0x7f0700cc" />
    <public type="dimen" name="material_clock_number_text_size" id="0x7f0700cd" />
    <public type="dimen" name="material_clock_period_toggle_height" id="0x7f0700ce" />
    <public type="dimen" name="material_clock_period_toggle_margin_left" id="0x7f0700cf" />
    <public type="dimen" name="material_clock_period_toggle_width" id="0x7f0700d0" />
    <public type="dimen" name="material_clock_size" id="0x7f0700d1" />
    <public type="dimen" name="material_cursor_inset_bottom" id="0x7f0700d2" />
    <public type="dimen" name="material_cursor_inset_top" id="0x7f0700d3" />
    <public type="dimen" name="material_cursor_width" id="0x7f0700d4" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f0700d5" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f0700d6" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f0700d7" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_bottom" id="0x7f0700d8" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_top" id="0x7f0700d9" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_bottom" id="0x7f0700da" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_top" id="0x7f0700db" />
    <public type="dimen" name="material_font_1_3_box_collapsed_padding_top" id="0x7f0700dc" />
    <public type="dimen" name="material_font_2_0_box_collapsed_padding_top" id="0x7f0700dd" />
    <public type="dimen" name="material_helper_text_default_padding_top" id="0x7f0700de" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_horizontal" id="0x7f0700df" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_top" id="0x7f0700e0" />
    <public type="dimen" name="material_input_text_to_prefix_suffix_padding" id="0x7f0700e1" />
    <public type="dimen" name="material_text_view_test_line_height" id="0x7f0700e2" />
    <public type="dimen" name="material_text_view_test_line_height_override" id="0x7f0700e3" />
    <public type="dimen" name="material_timepicker_dialog_buttons_margin_top" id="0x7f0700e4" />
    <public type="dimen" name="md_action_button_corner_radius" id="0x7f0700e5" />
    <public type="dimen" name="md_action_button_frame_padding" id="0x7f0700e6" />
    <public type="dimen" name="md_action_button_frame_padding_neutral" id="0x7f0700e7" />
    <public type="dimen" name="md_action_button_frame_spec_height" id="0x7f0700e8" />
    <public type="dimen" name="md_action_button_inset_horizontal" id="0x7f0700e9" />
    <public type="dimen" name="md_action_button_inset_vertical" id="0x7f0700ea" />
    <public type="dimen" name="md_action_button_min_width" id="0x7f0700eb" />
    <public type="dimen" name="md_action_button_padding_horizontal" id="0x7f0700ec" />
    <public type="dimen" name="md_action_button_padding_vertical" id="0x7f0700ed" />
    <public type="dimen" name="md_action_button_textsize" id="0x7f0700ee" />
    <public type="dimen" name="md_checkbox_prompt_height" id="0x7f0700ef" />
    <public type="dimen" name="md_checkbox_prompt_margin_horizontal" id="0x7f0700f0" />
    <public type="dimen" name="md_checkbox_prompt_margin_vertical" id="0x7f0700f1" />
    <public type="dimen" name="md_dialog_default_corner_radius" id="0x7f0700f2" />
    <public type="dimen" name="md_dialog_frame_margin_horizontal" id="0x7f0700f3" />
    <public type="dimen" name="md_dialog_frame_margin_vertical" id="0x7f0700f4" />
    <public type="dimen" name="md_dialog_frame_margin_vertical_less" id="0x7f0700f5" />
    <public type="dimen" name="md_dialog_horizontal_margin" id="0x7f0700f6" />
    <public type="dimen" name="md_dialog_max_width" id="0x7f0700f7" />
    <public type="dimen" name="md_dialog_title_layout_margin_bottom" id="0x7f0700f8" />
    <public type="dimen" name="md_dialog_vertical_margin" id="0x7f0700f9" />
    <public type="dimen" name="md_divider_height" id="0x7f0700fa" />
    <public type="dimen" name="md_icon_margin" id="0x7f0700fb" />
    <public type="dimen" name="md_icon_size" id="0x7f0700fc" />
    <public type="dimen" name="md_listitem_control_margin" id="0x7f0700fd" />
    <public type="dimen" name="md_listitem_height" id="0x7f0700fe" />
    <public type="dimen" name="md_listitem_margin_left" id="0x7f0700ff" />
    <public type="dimen" name="md_listitem_margin_left_choice" id="0x7f070100" />
    <public type="dimen" name="md_listitem_textsize" id="0x7f070101" />
    <public type="dimen" name="md_listitem_vertical_margin" id="0x7f070102" />
    <public type="dimen" name="md_listitem_vertical_margin_choice" id="0x7f070103" />
    <public type="dimen" name="md_message_textsize" id="0x7f070104" />
    <public type="dimen" name="md_title_textsize" id="0x7f070105" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f070106" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f070107" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f070108" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f070109" />
    <public type="dimen" name="mtrl_alert_dialog_picker_background_inset" id="0x7f07010a" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f07010b" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f07010c" />
    <public type="dimen" name="mtrl_badge_radius" id="0x7f07010d" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f07010e" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f07010f" />
    <public type="dimen" name="mtrl_badge_toolbar_action_menu_item_horizontal_offset" id="0x7f070110" />
    <public type="dimen" name="mtrl_badge_toolbar_action_menu_item_vertical_offset" id="0x7f070111" />
    <public type="dimen" name="mtrl_badge_with_text_radius" id="0x7f070112" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f070113" />
    <public type="dimen" name="mtrl_bottomappbar_fab_bottom_margin" id="0x7f070114" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f070115" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f070116" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f070117" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f070118" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f070119" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f07011a" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f07011b" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f07011c" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f07011d" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f07011e" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f07011f" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f070120" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f070121" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f070122" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f070123" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f070124" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f070125" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f070126" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f070127" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f070128" />
    <public type="dimen" name="mtrl_btn_snackbar_margin_horizontal" id="0x7f070129" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f07012a" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f07012b" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f07012c" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f07012d" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f07012e" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f07012f" />
    <public type="dimen" name="mtrl_calendar_action_confirm_button_min_width" id="0x7f070130" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f070131" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f070132" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f070133" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f070134" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f070135" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f070136" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f070137" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f070138" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f070139" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f07013a" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f07013b" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f07013c" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f07013d" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f07013e" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f07013f" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f070140" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f070141" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f070142" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f070143" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f070144" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f070145" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f070146" />
    <public type="dimen" name="mtrl_calendar_maximum_default_fullscreen_minor_axis" id="0x7f070147" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f070148" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f070149" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f07014a" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f07014b" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f07014c" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f07014d" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f07014e" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom" id="0x7f07014f" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen" id="0x7f070150" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f070151" />
    <public type="dimen" name="mtrl_calendar_text_input_padding_top" id="0x7f070152" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f070153" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f070154" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f070155" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f070156" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f070157" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f070158" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f070159" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f07015a" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f07015b" />
    <public type="dimen" name="mtrl_card_corner_radius" id="0x7f07015c" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f07015d" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f07015e" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f07015f" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f070160" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f070161" />
    <public type="dimen" name="mtrl_edittext_rectangle_top_offset" id="0x7f070162" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f070163" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f070164" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f070165" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f070166" />
    <public type="dimen" name="mtrl_extended_fab_corner_radius" id="0x7f070167" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f070168" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f070169" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f07016a" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f07016b" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f07016c" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f07016d" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f07016e" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f07016f" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f070170" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f070171" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f070172" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f070173" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f070174" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f070175" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f070176" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f070177" />
    <public type="dimen" name="mtrl_fab_min_touch_target" id="0x7f070178" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f070179" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f07017a" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f07017b" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f07017c" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f07017d" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f07017e" />
    <public type="dimen" name="mtrl_large_touch_target" id="0x7f07017f" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f070180" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f070181" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f070182" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f070183" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f070184" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f070185" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f070186" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f070187" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f070188" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f070189" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f07018a" />
    <public type="dimen" name="mtrl_progress_circular_inset" id="0x7f07018b" />
    <public type="dimen" name="mtrl_progress_circular_inset_extra_small" id="0x7f07018c" />
    <public type="dimen" name="mtrl_progress_circular_inset_medium" id="0x7f07018d" />
    <public type="dimen" name="mtrl_progress_circular_inset_small" id="0x7f07018e" />
    <public type="dimen" name="mtrl_progress_circular_radius" id="0x7f07018f" />
    <public type="dimen" name="mtrl_progress_circular_size" id="0x7f070190" />
    <public type="dimen" name="mtrl_progress_circular_size_extra_small" id="0x7f070191" />
    <public type="dimen" name="mtrl_progress_circular_size_medium" id="0x7f070192" />
    <public type="dimen" name="mtrl_progress_circular_size_small" id="0x7f070193" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_extra_small" id="0x7f070194" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_medium" id="0x7f070195" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_small" id="0x7f070196" />
    <public type="dimen" name="mtrl_progress_indicator_full_rounded_corner_radius" id="0x7f070197" />
    <public type="dimen" name="mtrl_progress_track_thickness" id="0x7f070198" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f070199" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f07019a" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f07019b" />
    <public type="dimen" name="mtrl_slider_halo_radius" id="0x7f07019c" />
    <public type="dimen" name="mtrl_slider_label_padding" id="0x7f07019d" />
    <public type="dimen" name="mtrl_slider_label_radius" id="0x7f07019e" />
    <public type="dimen" name="mtrl_slider_label_square_side" id="0x7f07019f" />
    <public type="dimen" name="mtrl_slider_thumb_elevation" id="0x7f0701a0" />
    <public type="dimen" name="mtrl_slider_thumb_radius" id="0x7f0701a1" />
    <public type="dimen" name="mtrl_slider_track_height" id="0x7f0701a2" />
    <public type="dimen" name="mtrl_slider_track_side_padding" id="0x7f0701a3" />
    <public type="dimen" name="mtrl_slider_track_top" id="0x7f0701a4" />
    <public type="dimen" name="mtrl_slider_widget_height" id="0x7f0701a5" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f0701a6" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f0701a7" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f0701a8" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f0701a9" />
    <public type="dimen" name="mtrl_snackbar_message_margin_horizontal" id="0x7f0701aa" />
    <public type="dimen" name="mtrl_snackbar_padding_horizontal" id="0x7f0701ab" />
    <public type="dimen" name="mtrl_switch_thumb_elevation" id="0x7f0701ac" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f0701ad" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f0701ae" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f0701af" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f0701b0" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f0701b1" />
    <public type="dimen" name="mtrl_textinput_counter_margin_start" id="0x7f0701b2" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f0701b3" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f0701b4" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f0701b5" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f0701b6" />
    <public type="dimen" name="mtrl_tooltip_arrowSize" id="0x7f0701b7" />
    <public type="dimen" name="mtrl_tooltip_cornerSize" id="0x7f0701b8" />
    <public type="dimen" name="mtrl_tooltip_minHeight" id="0x7f0701b9" />
    <public type="dimen" name="mtrl_tooltip_minWidth" id="0x7f0701ba" />
    <public type="dimen" name="mtrl_tooltip_padding" id="0x7f0701bb" />
    <public type="dimen" name="mtrl_transition_shared_axis_slide_distance" id="0x7f0701bc" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f0701bd" />
    <public type="dimen" name="notification_action_text_size" id="0x7f0701be" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f0701bf" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f0701c0" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f0701c1" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f0701c2" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f0701c3" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f0701c4" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f0701c5" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f0701c6" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f0701c7" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f0701c8" />
    <public type="dimen" name="notification_subtext_size" id="0x7f0701c9" />
    <public type="dimen" name="notification_top_pad" id="0x7f0701ca" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f0701cb" />
    <public type="dimen" name="preference_dropdown_padding_start" id="0x7f0701cc" />
    <public type="dimen" name="preference_icon_minWidth" id="0x7f0701cd" />
    <public type="dimen" name="preference_seekbar_padding_horizontal" id="0x7f0701ce" />
    <public type="dimen" name="preference_seekbar_padding_vertical" id="0x7f0701cf" />
    <public type="dimen" name="preference_seekbar_value_minWidth" id="0x7f0701d0" />
    <public type="dimen" name="preferences_detail_width" id="0x7f0701d1" />
    <public type="dimen" name="preferences_header_width" id="0x7f0701d2" />
    <public type="dimen" name="rippleRadius" id="0x7f0701d3" />
    <public type="dimen" name="rippleStrokeWidth" id="0x7f0701d4" />
    <public type="dimen" name="search_icon_padding" id="0x7f0701d5" />
    <public type="dimen" name="test_mtrl_calendar_day_cornerSize" id="0x7f0701d6" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f0701d7" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f0701d8" />
    <public type="dimen" name="tooltip_margin" id="0x7f0701d9" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f0701da" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f0701db" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f0701dc" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f0701dd" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f0701de" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f080000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f080001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f080002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f080003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f080004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f080005" />
    <public type="drawable" name="sani_bg_3" id="0x7f080006" />
    <public type="drawable" name="$ic_launcher_foreground__0" id="0x7f080007" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f080009" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f08000a" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f08000b" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f08000c" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f08000f" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f080010" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f080011" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f080012" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f080017" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f080018" />
    <public type="drawable" name="abc_control_background_material" id="0x7f08001a" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f08001b" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f08001c" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f08001d" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f08001e" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f08001f" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f080021" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f080022" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f080023" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f080024" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f080025" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f080026" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f080027" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f080028" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f080029" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f08002a" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f08002b" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f08002c" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f080032" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f080033" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f080036" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f080037" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f08003a" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f08003b" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f08003c" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f080042" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f080043" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f080044" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f080046" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f080047" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f080048" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f080049" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f08004b" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f08004d" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f080055" />
    <public type="drawable" name="abc_vector_test" id="0x7f080056" />
    <public type="drawable" name="admob_close_button_black_circle_white_cross" id="0x7f080057" />
    <public type="drawable" name="admob_close_button_white_circle_black_cross" id="0x7f080058" />
    <public type="drawable" name="agree" id="0x7f080059" />
    <public type="drawable" name="avd_hide_password" id="0x7f08005a" />
    <public type="drawable" name="avd_show_password" id="0x7f08005b" />
    <public type="drawable" name="background" id="0x7f08005c" />
    <public type="drawable" name="background_rounded_dialog" id="0x7f08005d" />
    <public type="drawable" name="bg_period_pw" id="0x7f08005e" />
    <public type="drawable" name="bg_period_selected_pw_2" id="0x7f08005f" />
    <public type="drawable" name="bonuspack_bubble" id="0x7f080060" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f080061" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f080062" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f080063" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f080064" />
    <public type="drawable" name="btn_moreinfo" id="0x7f080065" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f080066" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f080067" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f080068" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f080069" />
    <public type="drawable" name="center" id="0x7f08006b" />
    <public type="drawable" name="common_google_signin_btn_icon_dark" id="0x7f08006d" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_focused" id="0x7f08006e" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_normal" id="0x7f08006f" />
    <public type="drawable" name="common_google_signin_btn_icon_disabled" id="0x7f080071" />
    <public type="drawable" name="common_google_signin_btn_icon_light" id="0x7f080072" />
    <public type="drawable" name="common_google_signin_btn_icon_light_focused" id="0x7f080073" />
    <public type="drawable" name="common_google_signin_btn_icon_light_normal" id="0x7f080074" />
    <public type="drawable" name="common_google_signin_btn_text_dark" id="0x7f080076" />
    <public type="drawable" name="common_google_signin_btn_text_dark_focused" id="0x7f080077" />
    <public type="drawable" name="common_google_signin_btn_text_dark_normal" id="0x7f080078" />
    <public type="drawable" name="common_google_signin_btn_text_disabled" id="0x7f08007a" />
    <public type="drawable" name="common_google_signin_btn_text_light" id="0x7f08007b" />
    <public type="drawable" name="common_google_signin_btn_text_light_focused" id="0x7f08007c" />
    <public type="drawable" name="common_google_signin_btn_text_light_normal" id="0x7f08007d" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f08007f" />
    <public type="drawable" name="design_fab_background" id="0x7f080080" />
    <public type="drawable" name="design_ic_visibility" id="0x7f080081" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f080082" />
    <public type="drawable" name="design_password_eye" id="0x7f080083" />
    <public type="drawable" name="design_snackbar_background" id="0x7f080084" />
    <public type="drawable" name="dot_background" id="0x7f080085" />
    <public type="drawable" name="empty" id="0x7f080086" />
    <public type="drawable" name="exo_controls_fastforward" id="0x7f080087" />
    <public type="drawable" name="exo_controls_fullscreen_enter" id="0x7f080088" />
    <public type="drawable" name="exo_controls_fullscreen_exit" id="0x7f080089" />
    <public type="drawable" name="exo_controls_next" id="0x7f08008a" />
    <public type="drawable" name="exo_controls_pause" id="0x7f08008b" />
    <public type="drawable" name="exo_controls_play" id="0x7f08008c" />
    <public type="drawable" name="exo_controls_previous" id="0x7f08008d" />
    <public type="drawable" name="exo_controls_repeat_all" id="0x7f08008e" />
    <public type="drawable" name="exo_controls_repeat_off" id="0x7f08008f" />
    <public type="drawable" name="exo_controls_repeat_one" id="0x7f080090" />
    <public type="drawable" name="exo_controls_rewind" id="0x7f080091" />
    <public type="drawable" name="exo_controls_shuffle_off" id="0x7f080092" />
    <public type="drawable" name="exo_controls_shuffle_on" id="0x7f080093" />
    <public type="drawable" name="exo_controls_vr" id="0x7f080094" />
    <public type="drawable" name="exo_notification_fastforward" id="0x7f0800b7" />
    <public type="drawable" name="exo_notification_next" id="0x7f0800b8" />
    <public type="drawable" name="exo_notification_pause" id="0x7f0800b9" />
    <public type="drawable" name="exo_notification_play" id="0x7f0800ba" />
    <public type="drawable" name="exo_notification_previous" id="0x7f0800bb" />
    <public type="drawable" name="exo_notification_rewind" id="0x7f0800bc" />
    <public type="drawable" name="exo_notification_small_icon" id="0x7f0800bd" />
    <public type="drawable" name="exo_notification_stop" id="0x7f0800be" />
    <public type="drawable" name="exo_rounded_rectangle" id="0x7f0800bf" />
    <public type="drawable" name="exo_styled_controls_audiotrack" id="0x7f0800c0" />
    <public type="drawable" name="exo_styled_controls_check" id="0x7f0800c1" />
    <public type="drawable" name="exo_styled_controls_fastforward" id="0x7f0800c2" />
    <public type="drawable" name="exo_styled_controls_fullscreen_enter" id="0x7f0800c3" />
    <public type="drawable" name="exo_styled_controls_fullscreen_exit" id="0x7f0800c4" />
    <public type="drawable" name="exo_styled_controls_next" id="0x7f0800c5" />
    <public type="drawable" name="exo_styled_controls_overflow_hide" id="0x7f0800c6" />
    <public type="drawable" name="exo_styled_controls_overflow_show" id="0x7f0800c7" />
    <public type="drawable" name="exo_styled_controls_pause" id="0x7f0800c8" />
    <public type="drawable" name="exo_styled_controls_play" id="0x7f0800c9" />
    <public type="drawable" name="exo_styled_controls_previous" id="0x7f0800ca" />
    <public type="drawable" name="exo_styled_controls_repeat_all" id="0x7f0800cb" />
    <public type="drawable" name="exo_styled_controls_repeat_off" id="0x7f0800cc" />
    <public type="drawable" name="exo_styled_controls_repeat_one" id="0x7f0800cd" />
    <public type="drawable" name="exo_styled_controls_rewind" id="0x7f0800ce" />
    <public type="drawable" name="exo_styled_controls_settings" id="0x7f0800cf" />
    <public type="drawable" name="exo_styled_controls_shuffle_off" id="0x7f0800d0" />
    <public type="drawable" name="exo_styled_controls_shuffle_on" id="0x7f0800d1" />
    <public type="drawable" name="exo_styled_controls_speed" id="0x7f0800d2" />
    <public type="drawable" name="exo_styled_controls_subtitle_off" id="0x7f0800d3" />
    <public type="drawable" name="exo_styled_controls_subtitle_on" id="0x7f0800d4" />
    <public type="drawable" name="exo_styled_controls_vr" id="0x7f0800d5" />
    <public type="drawable" name="filled" id="0x7f0800d7" />
    <public type="drawable" name="ic_arrow_back_black_24dp" id="0x7f0800dc" />
    <public type="drawable" name="ic_arrow_down_24dp" id="0x7f0800dd" />
    <public type="drawable" name="ic_auto_clean_pw_2" id="0x7f0800df" />
    <public type="drawable" name="ic_blue_dort" id="0x7f0800e0" />
    <public type="drawable" name="ic_cancle" id="0x7f0800e1" />
    <public type="drawable" name="ic_checked_pw_2" id="0x7f0800e2" />
    <public type="drawable" name="ic_clock_black_24dp" id="0x7f0800e3" />
    <public type="drawable" name="ic_close" id="0x7f0800e4" />
    <public type="drawable" name="ic_close_black_24dp" id="0x7f0800e5" />
    <public type="drawable" name="dummy_ae_e7" id="0x7f0800e7" />
    <public type="drawable" name="ic_keyboard_black_24dp" id="0x7f0800e8" />
    <public type="drawable" name="ic_launcher_background" id="0x7f0800e9" />
    <public type="drawable" name="ic_launcher_foreground" id="0x7f0800ea" />
    <public type="drawable" name="ic_menu_compass" id="0x7f0800ec" />
    <public type="drawable" name="ic_menu_mapmode" id="0x7f0800ed" />
    <public type="drawable" name="ic_menu_mylocation" id="0x7f0800ee" />
    <public type="drawable" name="ic_menu_offline" id="0x7f0800ef" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f0800f0" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f0800f1" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f0800f2" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f0800f3" />
    <public type="drawable" name="ic_no_ad_pw_2" id="0x7f0800f4" />
    <public type="drawable" name="ic_pin" id="0x7f0800f6" />
    <public type="drawable" name="ic_search_black_24dp" id="0x7f0800f8" />
    <public type="drawable" name="ic_star_no_select" id="0x7f0800f9" />
    <public type="drawable" name="ic_uncheck_pw" id="0x7f0800fc" />
    <public type="drawable" name="ic_voice_search_black_24dp" id="0x7f0800fd" />
    <public type="drawable" name="imuxuan" id="0x7f0800ff" />
    <public type="drawable" name="material_cursor_drawable" id="0x7f080102" />
    <public type="drawable" name="material_ic_calendar_black_24dp" id="0x7f080103" />
    <public type="drawable" name="material_ic_clear_black_24dp" id="0x7f080104" />
    <public type="drawable" name="material_ic_edit_black_24dp" id="0x7f080105" />
    <public type="drawable" name="material_ic_keyboard_arrow_left_black_24dp" id="0x7f080106" />
    <public type="drawable" name="material_ic_keyboard_arrow_next_black_24dp" id="0x7f080107" />
    <public type="drawable" name="material_ic_keyboard_arrow_previous_black_24dp" id="0x7f080108" />
    <public type="drawable" name="material_ic_keyboard_arrow_right_black_24dp" id="0x7f080109" />
    <public type="drawable" name="material_ic_menu_arrow_down_black_24dp" id="0x7f08010a" />
    <public type="drawable" name="material_ic_menu_arrow_up_black_24dp" id="0x7f08010b" />
    <public type="drawable" name="md_btn_selected" id="0x7f08010c" />
    <public type="drawable" name="md_btn_selected_dark" id="0x7f08010d" />
    <public type="drawable" name="md_btn_selector" id="0x7f08010e" />
    <public type="drawable" name="md_btn_selector_dark" id="0x7f08010f" />
    <public type="drawable" name="md_btn_shape" id="0x7f080110" />
    <public type="drawable" name="md_item_selected" id="0x7f080111" />
    <public type="drawable" name="md_item_selected_dark" id="0x7f080112" />
    <public type="drawable" name="md_item_selector" id="0x7f080113" />
    <public type="drawable" name="md_item_selector_dark" id="0x7f080114" />
    <public type="drawable" name="md_item_shape" id="0x7f080115" />
    <public type="drawable" name="md_transparent" id="0x7f080117" />
    <public type="drawable" name="moreinfo_arrow" id="0x7f080118" />
    <public type="drawable" name="moreinfo_arrow_pressed" id="0x7f080119" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f08011b" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f08011c" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f08011d" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f08011e" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f08011f" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f080120" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f080121" />
    <public type="drawable" name="mtrl_popupmenu_background_dark" id="0x7f080122" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f080123" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f080124" />
    <public type="drawable" name="navto_small" id="0x7f080125" />
    <public type="drawable" name="next" id="0x7f080126" />
    <public type="drawable" name="notification_action_background" id="0x7f080127" />
    <public type="drawable" name="notification_bg" id="0x7f080128" />
    <public type="drawable" name="notification_bg_low" id="0x7f080129" />
    <public type="drawable" name="notification_icon_background" id="0x7f08012e" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f08012f" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f080130" />
    <public type="drawable" name="notification_tile_bg" id="0x7f080131" />
    <public type="drawable" name="powerspinner_arrow" id="0x7f080138" />
    <public type="drawable" name="powerspinner_scrollbar" id="0x7f08013a" />
    <public type="drawable" name="preference_list_divider_material" id="0x7f08013b" />
    <public type="drawable" name="previous" id="0x7f08013c" />
    <public type="drawable" name="rotate_wrp" id="0x7f08013f" />
    <public type="drawable" name="screen_rotate_wrp" id="0x7f080143" />
    <public type="drawable" name="shape_trans" id="0x7f080144" />
    <public type="drawable" name="small_button_shape" id="0x7f080147" />
    <public type="drawable" name="splash" id="0x7f080148" />
    <public type="drawable" name="spring_dot_background" id="0x7f080149" />
    <public type="drawable" name="spring_dot_stroke_background" id="0x7f08014a" />
    <public type="drawable" name="test_custom_background" id="0x7f08014b" />
    <public type="drawable" name="test_level_drawable" id="0x7f08014c" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f08014d" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f08014e" />
    <public type="drawable" name="worm_dot_background" id="0x7f080150" />
    <public type="drawable" name="worm_dot_stroke_background" id="0x7f080151" />
    <public type="drawable" name="zoom_in" id="0x7f080152" />
    <public type="drawable" name="zoom_out" id="0x7f080153" />
    <public type="drawable" name="exo_edit_mode_logo" id="0x7f080095" />
    <public type="drawable" name="exo_ic_audiotrack" id="0x7f080096" />
    <public type="drawable" name="exo_ic_check" id="0x7f080097" />
    <public type="drawable" name="exo_ic_chevron_left" id="0x7f080098" />
    <public type="drawable" name="exo_ic_chevron_right" id="0x7f080099" />
    <public type="drawable" name="exo_ic_default_album_image" id="0x7f08009a" />
    <public type="drawable" name="exo_ic_forward" id="0x7f08009b" />
    <public type="drawable" name="exo_ic_fullscreen_enter" id="0x7f08009c" />
    <public type="drawable" name="exo_ic_fullscreen_exit" id="0x7f08009d" />
    <public type="drawable" name="exo_ic_pause_circle_filled" id="0x7f08009e" />
    <public type="drawable" name="exo_ic_play_circle_filled" id="0x7f08009f" />
    <public type="drawable" name="exo_ic_rewind" id="0x7f0800a0" />
    <public type="drawable" name="exo_ic_settings" id="0x7f0800a1" />
    <public type="drawable" name="exo_ic_skip_next" id="0x7f0800a2" />
    <public type="drawable" name="exo_ic_skip_previous" id="0x7f0800a3" />
    <public type="drawable" name="exo_ic_speed" id="0x7f0800a4" />
    <public type="drawable" name="exo_ic_subtitle_off" id="0x7f0800a5" />
    <public type="drawable" name="exo_ic_subtitle_on" id="0x7f0800a6" />
    <public type="drawable" name="exo_icon_circular_play" id="0x7f0800a7" />
    <public type="drawable" name="exo_icon_fastforward" id="0x7f0800a8" />
    <public type="drawable" name="exo_icon_fullscreen_enter" id="0x7f0800a9" />
    <public type="drawable" name="exo_icon_fullscreen_exit" id="0x7f0800aa" />
    <public type="drawable" name="exo_icon_next" id="0x7f0800ab" />
    <public type="drawable" name="exo_icon_pause" id="0x7f0800ac" />
    <public type="drawable" name="exo_icon_play" id="0x7f0800ad" />
    <public type="drawable" name="exo_icon_previous" id="0x7f0800ae" />
    <public type="drawable" name="exo_icon_repeat_all" id="0x7f0800af" />
    <public type="drawable" name="exo_icon_repeat_off" id="0x7f0800b0" />
    <public type="drawable" name="exo_icon_repeat_one" id="0x7f0800b1" />
    <public type="drawable" name="exo_icon_rewind" id="0x7f0800b2" />
    <public type="drawable" name="exo_icon_shuffle_off" id="0x7f0800b3" />
    <public type="drawable" name="exo_icon_shuffle_on" id="0x7f0800b4" />
    <public type="drawable" name="exo_icon_stop" id="0x7f0800b5" />
    <public type="drawable" name="exo_icon_vr" id="0x7f0800b6" />
    <public type="drawable" name="md_nav_back" id="0x7f080116" />
    <public type="drawable" name="powerspinner_arrow_vector" id="0x7f080139" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f080008" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f08000d" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f08000e" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f080013" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f080014" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f080015" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f080016" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f080019" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f080020" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f08002d" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f08002e" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f08002f" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f080030" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f080031" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f080034" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f080035" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f080038" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f080039" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f08003d" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f08003e" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f08003f" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f080040" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f080041" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f080045" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f08004a" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f08004c" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl" id="0x7f08004e" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl" id="0x7f08004f" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl" id="0x7f080050" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f080051" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f080052" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f080053" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f080054" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_normal_background" id="0x7f080070" />
    <public type="drawable" name="common_google_signin_btn_icon_light_normal_background" id="0x7f080075" />
    <public type="drawable" name="common_google_signin_btn_text_dark_normal_background" id="0x7f080079" />
    <public type="drawable" name="common_google_signin_btn_text_light_normal_background" id="0x7f08007e" />
    <public type="drawable" name="googleg_disabled_color_18" id="0x7f0800d9" />
    <public type="drawable" name="googleg_standard_color_18" id="0x7f0800da" />
    <public type="drawable" name="ic_add" id="0x7f0800db" />
    <public type="drawable" name="ic_location_change" id="0x7f0800eb" />
    <public type="drawable" name="ic_search" id="0x7f0800f7" />
    <public type="drawable" name="marker_default" id="0x7f080100" />
    <public type="drawable" name="marker_default_focused_base" id="0x7f080101" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f08012a" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f08012b" />
    <public type="drawable" name="notification_bg_normal" id="0x7f08012c" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f08012d" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f080132" />
    <public type="drawable" name="osm_ic_center_map" id="0x7f080133" />
    <public type="drawable" name="osm_ic_follow_me" id="0x7f080134" />
    <public type="drawable" name="osm_ic_follow_me_on" id="0x7f080135" />
    <public type="drawable" name="osm_ic_ic_map_ortho" id="0x7f080136" />
    <public type="drawable" name="person" id="0x7f080137" />
    <public type="drawable" name="round_navigation_white_48" id="0x7f080140" />
    <public type="drawable" name="sharp_add_black_36" id="0x7f080145" />
    <public type="drawable" name="sharp_remove_black_36" id="0x7f080146" />
    <public type="drawable" name="twotone_navigation_black_48" id="0x7f08014f" />
    <public type="drawable" name="cat" id="0x7f08006a" />
    <public type="drawable" name="common_full_open_on_phone" id="0x7f08006c" />
    <public type="drawable" name="eyes" id="0x7f0800d6" />
    <public type="drawable" name="mouse" id="0x7f08011a" />
    <public type="drawable" name="ic_crown_pw" id="0x7f0800e6" />
    <public type="drawable" name="ic_photo_hide_pw_2" id="0x7f0800f5" />
    <public type="drawable" name="ic_star_select" id="0x7f0800fa" />
    <public type="drawable" name="ic_star_struck" id="0x7f0800fb" />
    <public type="drawable" name="go_to_zero" id="0x7f0800d8" />
    <public type="drawable" name="ic_arrow_right_blue" id="0x7f0800de" />
    <public type="drawable" name="icon_right" id="0x7f0800fe" />
    <public type="drawable" name="rotate" id="0x7f08013d" />
    <public type="drawable" name="rotate_press" id="0x7f08013e" />
    <public type="drawable" name="screen_rotate" id="0x7f080141" />
    <public type="drawable" name="screen_rotate_press" id="0x7f080142" />
    <public type="font" name="roboto_medium_numbers" id="0x7f090000" />
    <public type="id" name="ALT" id="0x7f0a0000" />
    <public type="id" name="BOTTOM_END" id="0x7f0a0001" />
    <public type="id" name="BOTTOM_START" id="0x7f0a0002" />
    <public type="id" name="CTRL" id="0x7f0a0003" />
    <public type="id" name="FUNCTION" id="0x7f0a0004" />
    <public type="id" name="META" id="0x7f0a0005" />
    <public type="id" name="NO_DEBUG" id="0x7f0a0006" />
    <public type="id" name="SHIFT" id="0x7f0a0007" />
    <public type="id" name="SHOW_ALL" id="0x7f0a0008" />
    <public type="id" name="SHOW_PATH" id="0x7f0a0009" />
    <public type="id" name="SHOW_PROGRESS" id="0x7f0a000a" />
    <public type="id" name="SYM" id="0x7f0a000b" />
    <public type="id" name="TOP_END" id="0x7f0a000c" />
    <public type="id" name="TOP_START" id="0x7f0a000d" />
    <public type="id" name="about" id="0x7f0a000e" />
    <public type="id" name="about_container" id="0x7f0a000f" />
    <public type="id" name="accelerate" id="0x7f0a0010" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f0a0011" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f0a0012" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f0a0013" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f0a0014" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f0a0015" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f0a0016" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f0a0017" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f0a0018" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f0a0019" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f0a001a" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f0a001b" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f0a001c" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f0a001d" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f0a001e" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f0a001f" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f0a0020" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f0a0021" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f0a0022" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f0a0023" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f0a0024" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f0a0025" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f0a0026" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f0a0027" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f0a0028" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f0a0029" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f0a002a" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f0a002b" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f0a002c" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f0a002d" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f0a002e" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f0a002f" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f0a0030" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f0a0031" />
    <public type="id" name="action0" id="0x7f0a0032" />
    <public type="id" name="action_bar" id="0x7f0a0033" />
    <public type="id" name="action_bar_activity_content" id="0x7f0a0034" />
    <public type="id" name="action_bar_container" id="0x7f0a0035" />
    <public type="id" name="action_bar_root" id="0x7f0a0036" />
    <public type="id" name="action_bar_spinner" id="0x7f0a0037" />
    <public type="id" name="action_bar_subtitle" id="0x7f0a0038" />
    <public type="id" name="action_bar_title" id="0x7f0a0039" />
    <public type="id" name="action_container" id="0x7f0a003a" />
    <public type="id" name="action_context_bar" id="0x7f0a003b" />
    <public type="id" name="action_divider" id="0x7f0a003c" />
    <public type="id" name="action_image" id="0x7f0a003d" />
    <public type="id" name="action_menu_divider" id="0x7f0a003e" />
    <public type="id" name="action_menu_presenter" id="0x7f0a003f" />
    <public type="id" name="action_mode_bar" id="0x7f0a0040" />
    <public type="id" name="action_mode_bar_stub" id="0x7f0a0041" />
    <public type="id" name="action_mode_close_button" id="0x7f0a0042" />
    <public type="id" name="action_text" id="0x7f0a0043" />
    <public type="id" name="actions" id="0x7f0a0044" />
    <public type="id" name="activity_chooser_view_content" id="0x7f0a0045" />
    <public type="id" name="add" id="0x7f0a0046" />
    <public type="id" name="adjust_height" id="0x7f0a0047" />
    <public type="id" name="adjust_width" id="0x7f0a0048" />
    <public type="id" name="advBox" id="0x7f0a0049" />
    <public type="id" name="alertTitle" id="0x7f0a004a" />
    <public type="id" name="aligned" id="0x7f0a004b" />
    <public type="id" name="all" id="0x7f0a004c" />
    <public type="id" name="always" id="0x7f0a004d" />
    <public type="id" name="androidx_window_activity_scope" id="0x7f0a004e" />
    <public type="id" name="animateToEnd" id="0x7f0a004f" />
    <public type="id" name="animateToStart" id="0x7f0a0050" />
    <public type="id" name="app_clear" id="0x7f0a0051" />
    <public type="id" name="app_desc" id="0x7f0a0052" />
    <public type="id" name="app_remove" id="0x7f0a0053" />
    <public type="id" name="app_start" id="0x7f0a0054" />
    <public type="id" name="app_stop" id="0x7f0a0055" />
    <public type="id" name="asConfigured" id="0x7f0a0056" />
    <public type="id" name="async" id="0x7f0a0057" />
    <public type="id" name="audio_container" id="0x7f0a0058" />
    <public type="id" name="auto" id="0x7f0a0059" />
    <public type="id" name="autoClean" id="0x7f0a005a" />
    <public type="id" name="autoComplete" id="0x7f0a005b" />
    <public type="id" name="autoCompleteToEnd" id="0x7f0a005c" />
    <public type="id" name="autoCompleteToStart" id="0x7f0a005d" />
    <public type="id" name="backButton" id="0x7f0a005e" />
    <public type="id" name="background" id="0x7f0a005f" />
    <public type="id" name="bar" id="0x7f0a0060" />
    <public type="id" name="barrier" id="0x7f0a0061" />
    <public type="id" name="baseline" id="0x7f0a0062" />
    <public type="id" name="beginOnFirstDraw" id="0x7f0a0063" />
    <public type="id" name="beginning" id="0x7f0a0064" />
    <public type="id" name="blocking" id="0x7f0a0065" />
    <public type="id" name="body" id="0x7f0a0066" />
    <public type="id" name="bottom" id="0x7f0a0067" />
    <public type="id" name="bottomLine" id="0x7f0a0068" />
    <public type="id" name="bottom_view" id="0x7f0a0069" />
    <public type="id" name="bounce" id="0x7f0a006a" />
    <public type="id" name="browser_actions_header_text" id="0x7f0a006b" />
    <public type="id" name="browser_actions_menu_item_icon" id="0x7f0a006c" />
    <public type="id" name="browser_actions_menu_item_text" id="0x7f0a006d" />
    <public type="id" name="browser_actions_menu_items" id="0x7f0a006e" />
    <public type="id" name="browser_actions_menu_view" id="0x7f0a006f" />
    <public type="id" name="btn_apply" id="0x7f0a0070" />
    <public type="id" name="btn_delete" id="0x7f0a0071" />
    <public type="id" name="btn_exit" id="0x7f0a0072" />
    <public type="id" name="btn_rate" id="0x7f0a0073" />
    <public type="id" name="bubble_description" id="0x7f0a0074" />
    <public type="id" name="bubble_image" id="0x7f0a0075" />
    <public type="id" name="bubble_moreinfo" id="0x7f0a0076" />
    <public type="id" name="bubble_subdescription" id="0x7f0a0077" />
    <public type="id" name="bubble_title" id="0x7f0a0078" />
    <public type="id" name="buttonPanel" id="0x7f0a0079" />
    <public type="id" name="cache_measures" id="0x7f0a007a" />
    <public type="id" name="cancel_action" id="0x7f0a007b" />
    <public type="id" name="cancel_button" id="0x7f0a007c" />
    <public type="id" name="card" id="0x7f0a007d" />
    <public type="id" name="cat" id="0x7f0a007e" />
    <public type="id" name="center" id="0x7f0a007f" />
    <public type="id" name="center_horizontal" id="0x7f0a0080" />
    <public type="id" name="center_vertical" id="0x7f0a0081" />
    <public type="id" name="chain" id="0x7f0a0082" />
    <public type="id" name="chains" id="0x7f0a0083" />
    <public type="id" name="channels_select" id="0x7f0a0084" />
    <public type="id" name="check" id="0x7f0a0085" />
    <public type="id" name="checkbox" id="0x7f0a0086" />
    <public type="id" name="checked" id="0x7f0a0087" />
    <public type="id" name="chip" id="0x7f0a0088" />
    <public type="id" name="chip1" id="0x7f0a0089" />
    <public type="id" name="chip2" id="0x7f0a008a" />
    <public type="id" name="chip3" id="0x7f0a008b" />
    <public type="id" name="chip_group" id="0x7f0a008c" />
    <public type="id" name="chronometer" id="0x7f0a008d" />
    <public type="id" name="circle_center" id="0x7f0a008e" />
    <public type="id" name="clearButton" id="0x7f0a008f" />
    <public type="id" name="clear_text" id="0x7f0a0090" />
    <public type="id" name="clip_horizontal" id="0x7f0a0091" />
    <public type="id" name="clip_vertical" id="0x7f0a0092" />
    <public type="id" name="clockwise" id="0x7f0a0093" />
    <public type="id" name="close" id="0x7f0a0094" />
    <public type="id" name="collapseActionView" id="0x7f0a0095" />
    <public type="id" name="confirm_button" id="0x7f0a0096" />
    <public type="id" name="contact_us" id="0x7f0a0097" />
    <public type="id" name="container" id="0x7f0a0098" />
    <public type="id" name="content" id="0x7f0a0099" />
    <public type="id" name="contentPanel" id="0x7f0a009a" />
    <public type="id" name="contiguous" id="0x7f0a009b" />
    <public type="id" name="continueBtn" id="0x7f0a009c" />
    <public type="id" name="coordinator" id="0x7f0a009d" />
    <public type="id" name="cornerLabel" id="0x7f0a009e" />
    <public type="id" name="cos" id="0x7f0a009f" />
    <public type="id" name="counterclockwise" id="0x7f0a00a0" />
    <public type="id" name="custom" id="0x7f0a00a1" />
    <public type="id" name="customPanel" id="0x7f0a00a2" />
    <public type="id" name="cut" id="0x7f0a00a3" />
    <public type="id" name="dark" id="0x7f0a00a4" />
    <public type="id" name="date_picker_actions" id="0x7f0a00a5" />
    <public type="id" name="decelerate" id="0x7f0a00a6" />
    <public type="id" name="decelerateAndComplete" id="0x7f0a00a7" />
    <public type="id" name="decor_content_parent" id="0x7f0a00a8" />
    <public type="id" name="default_activity_button" id="0x7f0a00a9" />
    <public type="id" name="deltaRelative" id="0x7f0a00aa" />
    <public type="id" name="dependency_ordering" id="0x7f0a00ab" />
    <public type="id" name="desc" id="0x7f0a00ac" />
    <public type="id" name="design_bottom_sheet" id="0x7f0a00ad" />
    <public type="id" name="design_menu_item_action_area" id="0x7f0a00ae" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f0a00af" />
    <public type="id" name="design_menu_item_text" id="0x7f0a00b0" />
    <public type="id" name="design_navigation_view" id="0x7f0a00b1" />
    <public type="id" name="dialog_btn_agree" id="0x7f0a00b2" />
    <public type="id" name="dialog_btn_not_agree" id="0x7f0a00b3" />
    <public type="id" name="dialog_button" id="0x7f0a00b4" />
    <public type="id" name="dialog_content" id="0x7f0a00b5" />
    <public type="id" name="dialog_title" id="0x7f0a00b6" />
    <public type="id" name="dimensions" id="0x7f0a00b7" />
    <public type="id" name="direct" id="0x7f0a00b8" />
    <public type="id" name="disableHome" id="0x7f0a00b9" />
    <public type="id" name="disablePostScroll" id="0x7f0a00ba" />
    <public type="id" name="disableScroll" id="0x7f0a00bb" />
    <public type="id" name="disjoint" id="0x7f0a00bc" />
    <public type="id" name="dot" id="0x7f0a00bd" />
    <public type="id" name="dots_indicator" id="0x7f0a00be" />
    <public type="id" name="dragDown" id="0x7f0a00bf" />
    <public type="id" name="dragEnd" id="0x7f0a00c0" />
    <public type="id" name="dragLeft" id="0x7f0a00c1" />
    <public type="id" name="dragRight" id="0x7f0a00c2" />
    <public type="id" name="dragStart" id="0x7f0a00c3" />
    <public type="id" name="dragUp" id="0x7f0a00c4" />
    <public type="id" name="dropdown" id="0x7f0a00c5" />
    <public type="id" name="dropdown_menu" id="0x7f0a00c6" />
    <public type="id" name="easeIn" id="0x7f0a00c7" />
    <public type="id" name="easeInOut" id="0x7f0a00c8" />
    <public type="id" name="easeOut" id="0x7f0a00c9" />
    <public type="id" name="edit_query" id="0x7f0a00ca" />
    <public type="id" name="elastic" id="0x7f0a00cb" />
    <public type="id" name="empty_txt_empty" id="0x7f0a00cc" />
    <public type="id" name="enable" id="0x7f0a00cd" />
    <public type="id" name="enable_btc" id="0x7f0a00ce" />
    <public type="id" name="end" id="0x7f0a00cf" />
    <public type="id" name="endToStart" id="0x7f0a00d0" />
    <public type="id" name="end_padder" id="0x7f0a00d1" />
    <public type="id" name="enterAlways" id="0x7f0a00d2" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f0a00d3" />
    <public type="id" name="exitUntilCollapsed" id="0x7f0a00d4" />
    <public type="id" name="exo_ad_overlay" id="0x7f0a00d5" />
    <public type="id" name="exo_artwork" id="0x7f0a00d6" />
    <public type="id" name="exo_audio_track" id="0x7f0a00d7" />
    <public type="id" name="exo_basic_controls" id="0x7f0a00d8" />
    <public type="id" name="exo_bottom_bar" id="0x7f0a00d9" />
    <public type="id" name="exo_buffering" id="0x7f0a00da" />
    <public type="id" name="exo_center_controls" id="0x7f0a00db" />
    <public type="id" name="exo_check" id="0x7f0a00dc" />
    <public type="id" name="exo_content_frame" id="0x7f0a00dd" />
    <public type="id" name="exo_controller" id="0x7f0a00de" />
    <public type="id" name="exo_controller_placeholder" id="0x7f0a00df" />
    <public type="id" name="exo_controls_background" id="0x7f0a00e0" />
    <public type="id" name="exo_duration" id="0x7f0a00e1" />
    <public type="id" name="exo_error_message" id="0x7f0a00e2" />
    <public type="id" name="exo_extra_controls" id="0x7f0a00e3" />
    <public type="id" name="exo_extra_controls_scroll_view" id="0x7f0a00e4" />
    <public type="id" name="exo_ffwd" id="0x7f0a00e5" />
    <public type="id" name="exo_ffwd_with_amount" id="0x7f0a00e6" />
    <public type="id" name="exo_fullscreen" id="0x7f0a00e7" />
    <public type="id" name="exo_icon" id="0x7f0a00e8" />
    <public type="id" name="exo_main_text" id="0x7f0a00e9" />
    <public type="id" name="exo_minimal_controls" id="0x7f0a00ea" />
    <public type="id" name="exo_minimal_fullscreen" id="0x7f0a00eb" />
    <public type="id" name="exo_next" id="0x7f0a00ec" />
    <public type="id" name="exo_overflow_hide" id="0x7f0a00ed" />
    <public type="id" name="exo_overflow_show" id="0x7f0a00ee" />
    <public type="id" name="exo_overlay" id="0x7f0a00ef" />
    <public type="id" name="exo_pause" id="0x7f0a00f0" />
    <public type="id" name="exo_play" id="0x7f0a00f1" />
    <public type="id" name="exo_play_pause" id="0x7f0a00f2" />
    <public type="id" name="exo_playback_speed" id="0x7f0a00f3" />
    <public type="id" name="exo_position" id="0x7f0a00f4" />
    <public type="id" name="exo_prev" id="0x7f0a00f5" />
    <public type="id" name="exo_progress" id="0x7f0a00f6" />
    <public type="id" name="exo_progress_placeholder" id="0x7f0a00f7" />
    <public type="id" name="exo_repeat_toggle" id="0x7f0a00f8" />
    <public type="id" name="exo_rew" id="0x7f0a00f9" />
    <public type="id" name="exo_rew_with_amount" id="0x7f0a00fa" />
    <public type="id" name="exo_settings" id="0x7f0a00fb" />
    <public type="id" name="exo_settings_listview" id="0x7f0a00fc" />
    <public type="id" name="exo_shuffle" id="0x7f0a00fd" />
    <public type="id" name="exo_shutter" id="0x7f0a00fe" />
    <public type="id" name="exo_sub_text" id="0x7f0a00ff" />
    <public type="id" name="exo_subtitle" id="0x7f0a0100" />
    <public type="id" name="exo_subtitles" id="0x7f0a0101" />
    <public type="id" name="exo_text" id="0x7f0a0102" />
    <public type="id" name="exo_time" id="0x7f0a0103" />
    <public type="id" name="exo_track_selection_view" id="0x7f0a0104" />
    <public type="id" name="exo_vr" id="0x7f0a0105" />
    <public type="id" name="expand_activities_button" id="0x7f0a0106" />
    <public type="id" name="expanded_menu" id="0x7f0a0107" />
    <public type="id" name="eye_left" id="0x7f0a0108" />
    <public type="id" name="eye_right" id="0x7f0a0109" />
    <public type="id" name="eyelid_left" id="0x7f0a010a" />
    <public type="id" name="eyelid_right" id="0x7f0a010b" />
    <public type="id" name="fab" id="0x7f0a010c" />
    <public type="id" name="fade" id="0x7f0a010d" />
    <public type="id" name="fakeLocation" id="0x7f0a010e" />
    <public type="id" name="fill" id="0x7f0a010f" />
    <public type="id" name="fillRipple" id="0x7f0a0110" />
    <public type="id" name="fill_horizontal" id="0x7f0a0111" />
    <public type="id" name="fill_vertical" id="0x7f0a0112" />
    <public type="id" name="filled" id="0x7f0a0113" />
    <public type="id" name="fit" id="0x7f0a0114" />
    <public type="id" name="fitToContents" id="0x7f0a0115" />
    <public type="id" name="fixed" id="0x7f0a0116" />
    <public type="id" name="fixed_height" id="0x7f0a0117" />
    <public type="id" name="fixed_width" id="0x7f0a0118" />
    <public type="id" name="flip" id="0x7f0a0119" />
    <public type="id" name="flip_settings" id="0x7f0a011a" />
    <public type="id" name="flip_txt" id="0x7f0a011b" />
    <public type="id" name="floating" id="0x7f0a011c" />
    <public type="id" name="forever" id="0x7f0a011d" />
    <public type="id" name="fragment" id="0x7f0a011e" />
    <public type="id" name="fragment_container_view_tag" id="0x7f0a011f" />
    <public type="id" name="ghost_view" id="0x7f0a0120" />
    <public type="id" name="ghost_view_holder" id="0x7f0a0121" />
    <public type="id" name="gone" id="0x7f0a0122" />
    <public type="id" name="graduallyTextView" id="0x7f0a0123" />
    <public type="id" name="graph" id="0x7f0a0124" />
    <public type="id" name="graph_wrap" id="0x7f0a0125" />
    <public type="id" name="group_divider" id="0x7f0a0126" />
    <public type="id" name="grouping" id="0x7f0a0127" />
    <public type="id" name="groups" id="0x7f0a0128" />
    <public type="id" name="guideline" id="0x7f0a0129" />
    <public type="id" name="header_title" id="0x7f0a012a" />
    <public type="id" name="hideable" id="0x7f0a012b" />
    <public type="id" name="hk_nitification_holder_1" id="0x7f0a012c" />
    <public type="id" name="hk_nitification_holder_2" id="0x7f0a012d" />
    <public type="id" name="hk_nitification_holder_3" id="0x7f0a012e" />
    <public type="id" name="hk_nitification_holder_4" id="0x7f0a012f" />
    <public type="id" name="hk_notification_view" id="0x7f0a0130" />
    <public type="id" name="home" id="0x7f0a0131" />
    <public type="id" name="homeAsUp" id="0x7f0a0132" />
    <public type="id" name="honorRequest" id="0x7f0a0133" />
    <public type="id" name="hybrid" id="0x7f0a0134" />
    <public type="id" name="ic_pin" id="0x7f0a0135" />
    <public type="id" name="icon" id="0x7f0a0136" />
    <public type="id" name="icon_frame" id="0x7f0a0137" />
    <public type="id" name="icon_group" id="0x7f0a0138" />
    <public type="id" name="icon_only" id="0x7f0a0139" />
    <public type="id" name="ifRoom" id="0x7f0a013a" />
    <public type="id" name="ignore" id="0x7f0a013b" />
    <public type="id" name="ignoreRequest" id="0x7f0a013c" />
    <public type="id" name="image" id="0x7f0a013d" />
    <public type="id" name="image_smile" id="0x7f0a013e" />
    <public type="id" name="import_google" id="0x7f0a013f" />
    <public type="id" name="info" id="0x7f0a0140" />
    <public type="id" name="invisible" id="0x7f0a0141" />
    <public type="id" name="inward" id="0x7f0a0142" />
    <public type="id" name="italic" id="0x7f0a0143" />
    <public type="id" name="item_default_text" id="0x7f0a0144" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f0a0145" />
    <public type="id" name="jumpToEnd" id="0x7f0a0146" />
    <public type="id" name="jumpToStart" id="0x7f0a0147" />
    <public type="id" name="kill_all" id="0x7f0a0148" />
    <public type="id" name="labelGroup" id="0x7f0a0149" />
    <public type="id" name="labeled" id="0x7f0a014a" />
    <public type="id" name="largeLabel" id="0x7f0a014b" />
    <public type="id" name="layout" id="0x7f0a014c" />
    <public type="id" name="left" id="0x7f0a014d" />
    <public type="id" name="leftToRight" id="0x7f0a014e" />
    <public type="id" name="left_bottom" id="0x7f0a014f" />
    <public type="id" name="left_top" id="0x7f0a0150" />
    <public type="id" name="light" id="0x7f0a0151" />
    <public type="id" name="line1" id="0x7f0a0152" />
    <public type="id" name="line3" id="0x7f0a0153" />
    <public type="id" name="linear" id="0x7f0a0154" />
    <public type="id" name="listMode" id="0x7f0a0155" />
    <public type="id" name="list_choose" id="0x7f0a0156" />
    <public type="id" name="list_item" id="0x7f0a0157" />
    <public type="id" name="list_search" id="0x7f0a0158" />
    <public type="id" name="literal" id="0x7f0a0159" />
    <public type="id" name="locale" id="0x7f0a015a" />
    <public type="id" name="ltr" id="0x7f0a015b" />
    <public type="id" name="mainBox" id="0x7f0a015c" />
    <public type="id" name="main_setting" id="0x7f0a015d" />
    <public type="id" name="map" id="0x7f0a015e" />
    <public type="id" name="map_fragment" id="0x7f0a015f" />
    <public type="id" name="masked" id="0x7f0a0160" />
    <public type="id" name="material_clock_display" id="0x7f0a0161" />
    <public type="id" name="material_clock_face" id="0x7f0a0162" />
    <public type="id" name="material_clock_hand" id="0x7f0a0163" />
    <public type="id" name="material_clock_period_am_button" id="0x7f0a0164" />
    <public type="id" name="material_clock_period_pm_button" id="0x7f0a0165" />
    <public type="id" name="material_clock_period_toggle" id="0x7f0a0166" />
    <public type="id" name="material_hour_text_input" id="0x7f0a0167" />
    <public type="id" name="material_hour_tv" id="0x7f0a0168" />
    <public type="id" name="material_label" id="0x7f0a0169" />
    <public type="id" name="material_minute_text_input" id="0x7f0a016a" />
    <public type="id" name="material_minute_tv" id="0x7f0a016b" />
    <public type="id" name="material_textinput_timepicker" id="0x7f0a016c" />
    <public type="id" name="material_timepicker_cancel_button" id="0x7f0a016d" />
    <public type="id" name="material_timepicker_container" id="0x7f0a016e" />
    <public type="id" name="material_timepicker_edit_text" id="0x7f0a016f" />
    <public type="id" name="material_timepicker_mode_button" id="0x7f0a0170" />
    <public type="id" name="material_timepicker_ok_button" id="0x7f0a0171" />
    <public type="id" name="material_timepicker_view" id="0x7f0a0172" />
    <public type="id" name="material_value_index" id="0x7f0a0173" />
    <public type="id" name="md_button_layout" id="0x7f0a0174" />
    <public type="id" name="md_button_negative" id="0x7f0a0175" />
    <public type="id" name="md_button_neutral" id="0x7f0a0176" />
    <public type="id" name="md_button_positive" id="0x7f0a0177" />
    <public type="id" name="md_checkbox_prompt" id="0x7f0a0178" />
    <public type="id" name="md_content_layout" id="0x7f0a0179" />
    <public type="id" name="md_control" id="0x7f0a017a" />
    <public type="id" name="md_icon_title" id="0x7f0a017b" />
    <public type="id" name="md_input_layout" id="0x7f0a017c" />
    <public type="id" name="md_input_message" id="0x7f0a017d" />
    <public type="id" name="md_recyclerview_content" id="0x7f0a017e" />
    <public type="id" name="md_root" id="0x7f0a017f" />
    <public type="id" name="md_scrollview_content" id="0x7f0a0180" />
    <public type="id" name="md_scrollview_frame_content" id="0x7f0a0181" />
    <public type="id" name="md_text_message" id="0x7f0a0182" />
    <public type="id" name="md_text_title" id="0x7f0a0183" />
    <public type="id" name="md_title" id="0x7f0a0184" />
    <public type="id" name="md_title_layout" id="0x7f0a0185" />
    <public type="id" name="media_actions" id="0x7f0a0186" />
    <public type="id" name="media_controller_compat_view_tag" id="0x7f0a0187" />
    <public type="id" name="message" id="0x7f0a0188" />
    <public type="id" name="middle" id="0x7f0a0189" />
    <public type="id" name="mini" id="0x7f0a018a" />
    <public type="id" name="month1" id="0x7f0a018b" />
    <public type="id" name="month3" id="0x7f0a018c" />
    <public type="id" name="month6" id="0x7f0a018d" />
    <public type="id" name="month_grid" id="0x7f0a018e" />
    <public type="id" name="month_navigation_bar" id="0x7f0a018f" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f0a0190" />
    <public type="id" name="month_navigation_next" id="0x7f0a0191" />
    <public type="id" name="month_navigation_previous" id="0x7f0a0192" />
    <public type="id" name="month_title" id="0x7f0a0193" />
    <public type="id" name="motion_base" id="0x7f0a0194" />
    <public type="id" name="mouse" id="0x7f0a0195" />
    <public type="id" name="move_menu" id="0x7f0a0196" />
    <public type="id" name="move_txt" id="0x7f0a0197" />
    <public type="id" name="mtrl_anchor_parent" id="0x7f0a0198" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f0a0199" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f0a019a" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f0a019b" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f0a019c" />
    <public type="id" name="mtrl_calendar_months" id="0x7f0a019d" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f0a019e" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f0a019f" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f0a01a0" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f0a01a1" />
    <public type="id" name="mtrl_child_content_container" id="0x7f0a01a2" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f0a01a3" />
    <public type="id" name="mtrl_motion_snapshot_view" id="0x7f0a01a4" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f0a01a5" />
    <public type="id" name="mtrl_picker_header" id="0x7f0a01a6" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f0a01a7" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f0a01a8" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f0a01a9" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f0a01aa" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f0a01ab" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f0a01ac" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f0a01ad" />
    <public type="id" name="mtrl_view_tag_bottom_padding" id="0x7f0a01ae" />
    <public type="id" name="multiply" id="0x7f0a01af" />
    <public type="id" name="name" id="0x7f0a01b0" />
    <public type="id" name="navigation_header_container" id="0x7f0a01b1" />
    <public type="id" name="never" id="0x7f0a01b2" />
    <public type="id" name="noAdTv" id="0x7f0a01b3" />
    <public type="id" name="noScroll" id="0x7f0a01b4" />
    <public type="id" name="none" id="0x7f0a01b5" />
    <public type="id" name="normal" id="0x7f0a01b6" />
    <public type="id" name="notification_background" id="0x7f0a01b7" />
    <public type="id" name="notification_main_column" id="0x7f0a01b8" />
    <public type="id" name="notification_main_column_container" id="0x7f0a01b9" />
    <public type="id" name="off" id="0x7f0a01ba" />
    <public type="id" name="on" id="0x7f0a01bb" />
    <public type="id" name="one" id="0x7f0a01bc" />
    <public type="id" name="outline" id="0x7f0a01bd" />
    <public type="id" name="outward" id="0x7f0a01be" />
    <public type="id" name="packageName" id="0x7f0a01bf" />
    <public type="id" name="packed" id="0x7f0a01c0" />
    <public type="id" name="parallax" id="0x7f0a01c1" />
    <public type="id" name="parent" id="0x7f0a01c2" />
    <public type="id" name="parentPanel" id="0x7f0a01c3" />
    <public type="id" name="parentRelative" id="0x7f0a01c4" />
    <public type="id" name="parent_matrix" id="0x7f0a01c5" />
    <public type="id" name="password_toggle" id="0x7f0a01c6" />
    <public type="id" name="path" id="0x7f0a01c7" />
    <public type="id" name="pathRelative" id="0x7f0a01c8" />
    <public type="id" name="peekHeight" id="0x7f0a01c9" />
    <public type="id" name="perInfoTV" id="0x7f0a01ca" />
    <public type="id" name="perTV" id="0x7f0a01cb" />
    <public type="id" name="percent" id="0x7f0a01cc" />
    <public type="id" name="photoHide" id="0x7f0a01cd" />
    <public type="id" name="pin" id="0x7f0a01ce" />
    <public type="id" name="player_surface_frame" id="0x7f0a01cf" />
    <public type="id" name="position" id="0x7f0a01d0" />
    <public type="id" name="postLayout" id="0x7f0a01d1" />
    <public type="id" name="powerSpinner_preference" id="0x7f0a01d2" />
    <public type="id" name="preference_title" id="0x7f0a01d3" />
    <public type="id" name="preferences_detail" id="0x7f0a01d4" />
    <public type="id" name="preferences_header" id="0x7f0a01d5" />
    <public type="id" name="preferences_sliding_pane_layout" id="0x7f0a01d6" />
    <public type="id" name="premText" id="0x7f0a01d7" />
    <public type="id" name="price" id="0x7f0a01d8" />
    <public type="id" name="privacy" id="0x7f0a01d9" />
    <public type="id" name="privacyAndTerms" id="0x7f0a01da" />
    <public type="id" name="privacyBox" id="0x7f0a01db" />
    <public type="id" name="progressBar" id="0x7f0a01dc" />
    <public type="id" name="progress_bar" id="0x7f0a01dd" />
    <public type="id" name="progress_circular" id="0x7f0a01de" />
    <public type="id" name="progress_horizontal" id="0x7f0a01df" />
    <public type="id" name="progress_wheel" id="0x7f0a01e0" />
    <public type="id" name="protect_audio" id="0x7f0a01e1" />
    <public type="id" name="protect_audio_switch" id="0x7f0a01e2" />
    <public type="id" name="protect_height_edit" id="0x7f0a01e3" />
    <public type="id" name="protect_height_txt" id="0x7f0a01e4" />
    <public type="id" name="protect_method_btn" id="0x7f0a01e5" />
    <public type="id" name="protect_method_disable_camera" id="0x7f0a01e6" />
    <public type="id" name="protect_method_local" id="0x7f0a01e7" />
    <public type="id" name="protect_method_local_picture" id="0x7f0a01e8" />
    <public type="id" name="protect_method_network" id="0x7f0a01e9" />
    <public type="id" name="protect_method_text" id="0x7f0a01ea" />
    <public type="id" name="protect_path" id="0x7f0a01eb" />
    <public type="id" name="protect_save" id="0x7f0a01ec" />
    <public type="id" name="protect_tip" id="0x7f0a01ed" />
    <public type="id" name="protect_video_select" id="0x7f0a01ee" />
    <public type="id" name="protect_width_edit" id="0x7f0a01ef" />
    <public type="id" name="protect_width_txt" id="0x7f0a01f0" />
    <public type="id" name="radio" id="0x7f0a01f1" />
    <public type="id" name="rate_us" id="0x7f0a01f2" />
    <public type="id" name="rating_bar" id="0x7f0a01f3" />
    <public type="id" name="ratio" id="0x7f0a01f4" />
    <public type="id" name="rectangles" id="0x7f0a01f5" />
    <public type="id" name="recyclerView" id="0x7f0a01f6" />
    <public type="id" name="recycler_view" id="0x7f0a01f7" />
    <public type="id" name="remote_player_surface" id="0x7f0a01f8" />
    <public type="id" name="remote_player_surface_frame" id="0x7f0a01f9" />
    <public type="id" name="remote_subtitles_surface" id="0x7f0a01fa" />
    <public type="id" name="remove_ads" id="0x7f0a01fb" />
    <public type="id" name="reverseSawtooth" id="0x7f0a01fc" />
    <public type="id" name="right" id="0x7f0a01fd" />
    <public type="id" name="rightToLeft" id="0x7f0a01fe" />
    <public type="id" name="right_bottom" id="0x7f0a01ff" />
    <public type="id" name="right_icon" id="0x7f0a0200" />
    <public type="id" name="right_side" id="0x7f0a0201" />
    <public type="id" name="right_top" id="0x7f0a0202" />
    <public type="id" name="ripple_bg" id="0x7f0a0203" />
    <public type="id" name="rocker" id="0x7f0a0204" />
    <public type="id" name="root_id" id="0x7f0a0205" />
    <public type="id" name="rotate_btn" id="0x7f0a0206" />
    <public type="id" name="rotate_txt" id="0x7f0a0207" />
    <public type="id" name="rounded" id="0x7f0a0208" />
    <public type="id" name="row_index_key" id="0x7f0a0209" />
    <public type="id" name="rtl" id="0x7f0a020a" />
    <public type="id" name="satellite" id="0x7f0a020b" />
    <public type="id" name="save_non_transition_alpha" id="0x7f0a020c" />
    <public type="id" name="save_overlay_view" id="0x7f0a020d" />
    <public type="id" name="sawtooth" id="0x7f0a020e" />
    <public type="id" name="scale" id="0x7f0a020f" />
    <public type="id" name="screen" id="0x7f0a0210" />
    <public type="id" name="screen_rotate_btn" id="0x7f0a0211" />
    <public type="id" name="screen_rotate_txt" id="0x7f0a0212" />
    <public type="id" name="scroll" id="0x7f0a0213" />
    <public type="id" name="scrollIndicatorDown" id="0x7f0a0214" />
    <public type="id" name="scrollIndicatorUp" id="0x7f0a0215" />
    <public type="id" name="scrollView" id="0x7f0a0216" />
    <public type="id" name="scrollable" id="0x7f0a0217" />
    <public type="id" name="searchContainer" id="0x7f0a0218" />
    <public type="id" name="searchEditText" id="0x7f0a0219" />
    <public type="id" name="searchView" id="0x7f0a021a" />
    <public type="id" name="search_badge" id="0x7f0a021b" />
    <public type="id" name="search_bar" id="0x7f0a021c" />
    <public type="id" name="search_button" id="0x7f0a021d" />
    <public type="id" name="search_close_btn" id="0x7f0a021e" />
    <public type="id" name="search_edit_frame" id="0x7f0a021f" />
    <public type="id" name="search_go_btn" id="0x7f0a0220" />
    <public type="id" name="search_mag_icon" id="0x7f0a0221" />
    <public type="id" name="search_plate" id="0x7f0a0222" />
    <public type="id" name="search_src_text" id="0x7f0a0223" />
    <public type="id" name="search_voice_btn" id="0x7f0a0224" />
    <public type="id" name="seekbar" id="0x7f0a0225" />
    <public type="id" name="seekbar_value" id="0x7f0a0226" />
    <public type="id" name="select_dialog_listview" id="0x7f0a0227" />
    <public type="id" name="selected" id="0x7f0a0228" />
    <public type="id" name="selection_type" id="0x7f0a0229" />
    <public type="id" name="settings" id="0x7f0a022a" />
    <public type="id" name="share" id="0x7f0a022b" />
    <public type="id" name="shortcut" id="0x7f0a022c" />
    <public type="id" name="showCustom" id="0x7f0a022d" />
    <public type="id" name="showHome" id="0x7f0a022e" />
    <public type="id" name="showTitle" id="0x7f0a022f" />
    <public type="id" name="sin" id="0x7f0a0230" />
    <public type="id" name="size_container" id="0x7f0a0231" />
    <public type="id" name="size_seekbar" id="0x7f0a0232" />
    <public type="id" name="size_txt" id="0x7f0a0233" />
    <public type="id" name="skipCollapsed" id="0x7f0a0234" />
    <public type="id" name="slide" id="0x7f0a0235" />
    <public type="id" name="smallLabel" id="0x7f0a0236" />
    <public type="id" name="small_pin" id="0x7f0a0237" />
    <public type="id" name="snackbar_action" id="0x7f0a0238" />
    <public type="id" name="snackbar_text" id="0x7f0a0239" />
    <public type="id" name="snap" id="0x7f0a023a" />
    <public type="id" name="snapMargins" id="0x7f0a023b" />
    <public type="id" name="spacer" id="0x7f0a023c" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f0a023d" />
    <public type="id" name="spherical_gl_surface_view" id="0x7f0a023e" />
    <public type="id" name="spinner" id="0x7f0a023f" />
    <public type="id" name="spline" id="0x7f0a0240" />
    <public type="id" name="split_action_bar" id="0x7f0a0241" />
    <public type="id" name="spread" id="0x7f0a0242" />
    <public type="id" name="spread_inside" id="0x7f0a0243" />
    <public type="id" name="spring_dot" id="0x7f0a0244" />
    <public type="id" name="square" id="0x7f0a0245" />
    <public type="id" name="src_atop" id="0x7f0a0246" />
    <public type="id" name="src_in" id="0x7f0a0247" />
    <public type="id" name="src_over" id="0x7f0a0248" />
    <public type="id" name="standard" id="0x7f0a0249" />
    <public type="id" name="start" id="0x7f0a024a" />
    <public type="id" name="startHorizontal" id="0x7f0a024b" />
    <public type="id" name="startToEnd" id="0x7f0a024c" />
    <public type="id" name="startVertical" id="0x7f0a024d" />
    <public type="id" name="stateView" id="0x7f0a024e" />
    <public type="id" name="staticLayout" id="0x7f0a024f" />
    <public type="id" name="staticPostLayout" id="0x7f0a0250" />
    <public type="id" name="status_bar_latest_event_content" id="0x7f0a0251" />
    <public type="id" name="stop" id="0x7f0a0252" />
    <public type="id" name="stretch" id="0x7f0a0253" />
    <public type="id" name="strokeRipple" id="0x7f0a0254" />
    <public type="id" name="submenuarrow" id="0x7f0a0255" />
    <public type="id" name="submit_area" id="0x7f0a0256" />
    <public type="id" name="submit_location_button" id="0x7f0a0257" />
    <public type="id" name="subtitles_surface_stub" id="0x7f0a0258" />
    <public type="id" name="surface_stub" id="0x7f0a0259" />
    <public type="id" name="surface_subtitles" id="0x7f0a025a" />
    <public type="id" name="surface_video" id="0x7f0a025b" />
    <public type="id" name="surface_view" id="0x7f0a025c" />
    <public type="id" name="switchWidget" id="0x7f0a025d" />
    <public type="id" name="switch_version" id="0x7f0a025e" />
    <public type="id" name="switch_view" id="0x7f0a025f" />
    <public type="id" name="tabMode" id="0x7f0a0260" />
    <public type="id" name="tag_accessibility_actions" id="0x7f0a0261" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f0a0262" />
    <public type="id" name="tag_accessibility_heading" id="0x7f0a0263" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f0a0264" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f0a0265" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f0a0266" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f0a0267" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f0a0268" />
    <public type="id" name="tag_state_description" id="0x7f0a0269" />
    <public type="id" name="tag_transition_group" id="0x7f0a026a" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0a026b" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0a026c" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f0a026d" />
    <public type="id" name="temp1" id="0x7f0a026e" />
    <public type="id" name="temp2" id="0x7f0a026f" />
    <public type="id" name="temp3" id="0x7f0a0270" />
    <public type="id" name="temp4" id="0x7f0a0271" />
    <public type="id" name="temp5" id="0x7f0a0272" />
    <public type="id" name="temp6" id="0x7f0a0273" />
    <public type="id" name="terms" id="0x7f0a0274" />
    <public type="id" name="terrain" id="0x7f0a0275" />
    <public type="id" name="test_checkbox_android_button_tint" id="0x7f0a0276" />
    <public type="id" name="test_checkbox_app_button_tint" id="0x7f0a0277" />
    <public type="id" name="test_radiobutton_android_button_tint" id="0x7f0a0278" />
    <public type="id" name="test_radiobutton_app_button_tint" id="0x7f0a0279" />
    <public type="id" name="text" id="0x7f0a027a" />
    <public type="id" name="text2" id="0x7f0a027b" />
    <public type="id" name="textEnd" id="0x7f0a027c" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0a027d" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0a027e" />
    <public type="id" name="textStart" id="0x7f0a027f" />
    <public type="id" name="textTop" id="0x7f0a0280" />
    <public type="id" name="text_input_end_icon" id="0x7f0a0281" />
    <public type="id" name="text_input_error_icon" id="0x7f0a0282" />
    <public type="id" name="text_input_start_icon" id="0x7f0a0283" />
    <public type="id" name="text_layout" id="0x7f0a0284" />
    <public type="id" name="textinput_counter" id="0x7f0a0285" />
    <public type="id" name="textinput_error" id="0x7f0a0286" />
    <public type="id" name="textinput_helper_text" id="0x7f0a0287" />
    <public type="id" name="textinput_placeholder" id="0x7f0a0288" />
    <public type="id" name="textinput_prefix_text" id="0x7f0a0289" />
    <public type="id" name="textinput_suffix_text" id="0x7f0a028a" />
    <public type="id" name="texture_stub" id="0x7f0a028b" />
    <public type="id" name="texture_video" id="0x7f0a028c" />
    <public type="id" name="texture_view" id="0x7f0a028d" />
    <public type="id" name="time" id="0x7f0a028e" />
    <public type="id" name="title" id="0x7f0a028f" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0a0290" />
    <public type="id" name="title_template" id="0x7f0a0291" />
    <public type="id" name="toggle" id="0x7f0a0292" />
    <public type="id" name="toolbar" id="0x7f0a0293" />
    <public type="id" name="toolbar_layout" id="0x7f0a0294" />
    <public type="id" name="top" id="0x7f0a0295" />
    <public type="id" name="topPanel" id="0x7f0a0296" />
    <public type="id" name="touch_outside" id="0x7f0a0297" />
    <public type="id" name="transitionToEnd" id="0x7f0a0298" />
    <public type="id" name="transitionToStart" id="0x7f0a0299" />
    <public type="id" name="transition_current_scene" id="0x7f0a029a" />
    <public type="id" name="transition_layout_save" id="0x7f0a029b" />
    <public type="id" name="transition_position" id="0x7f0a029c" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0a029d" />
    <public type="id" name="transition_transform" id="0x7f0a029e" />
    <public type="id" name="triangle" id="0x7f0a029f" />
    <public type="id" name="tvTitle" id="0x7f0a02a0" />
    <public type="id" name="tv_display_marker_location" id="0x7f0a02a1" />
    <public type="id" name="unchecked" id="0x7f0a02a2" />
    <public type="id" name="uniform" id="0x7f0a02a3" />
    <public type="id" name="unlabeled" id="0x7f0a02a4" />
    <public type="id" name="up" id="0x7f0a02a5" />
    <public type="id" name="update_btn" id="0x7f0a02a6" />
    <public type="id" name="update_container" id="0x7f0a02a7" />
    <public type="id" name="update_details" id="0x7f0a02a8" />
    <public type="id" name="update_title" id="0x7f0a02a9" />
    <public type="id" name="upper" id="0x7f0a02aa" />
    <public type="id" name="useLogo" id="0x7f0a02ab" />
    <public type="id" name="vc_github" id="0x7f0a02ac" />
    <public type="id" name="vc_setting" id="0x7f0a02ad" />
    <public type="id" name="version" id="0x7f0a02ae" />
    <public type="id" name="video_decoder_gl_surface_view" id="0x7f0a02af" />
    <public type="id" name="video_size_note" id="0x7f0a02b0" />
    <public type="id" name="viewPager" id="0x7f0a02b1" />
    <public type="id" name="view_offset_helper" id="0x7f0a02b2" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f0a02b3" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f0a02b4" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f0a02b5" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f0a02b6" />
    <public type="id" name="visible" id="0x7f0a02b7" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f0a02b8" />
    <public type="id" name="voiceButton" id="0x7f0a02b9" />
    <public type="id" name="when_playing" id="0x7f0a02ba" />
    <public type="id" name="wide" id="0x7f0a02bb" />
    <public type="id" name="withText" id="0x7f0a02bc" />
    <public type="id" name="withinBounds" id="0x7f0a02bd" />
    <public type="id" name="worm_dot" id="0x7f0a02be" />
    <public type="id" name="wrap" id="0x7f0a02bf" />
    <public type="id" name="wrap_content" id="0x7f0a02c0" />
    <public type="id" name="year1" id="0x7f0a02c1" />
    <public type="id" name="zero_corner_chip" id="0x7f0a02c2" />
    <public type="id" name="zoom" id="0x7f0a02c3" />
    <public type="id" name="zoom_seekbar" id="0x7f0a02c4" />
    <public type="id" name="zoom_txt" id="0x7f0a02c5" />
    <public type="id" name="empty_image" id="0x7f0a02c6" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0b0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0b0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0b0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0b0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0b0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0b0005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0b0006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0b0007" />
    <public type="integer" name="exo_media_button_opacity_percentage_disabled" id="0x7f0b0008" />
    <public type="integer" name="exo_media_button_opacity_percentage_enabled" id="0x7f0b0009" />
    <public type="integer" name="google_play_services_version" id="0x7f0b000a" />
    <public type="integer" name="hide_password_duration" id="0x7f0b000b" />
    <public type="integer" name="mtrl_badge_max_character_count" id="0x7f0b000c" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0b000d" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0b000e" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f0b000f" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f0b0010" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f0b0011" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f0b0012" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f0b0013" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0b0014" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0b0015" />
    <public type="integer" name="preferences_detail_pane_weight" id="0x7f0b0016" />
    <public type="integer" name="preferences_header_pane_weight" id="0x7f0b0017" />
    <public type="integer" name="show_password_duration" id="0x7f0b0018" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0b0019" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0c0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0c0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0c0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0c0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0c0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0c0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0c0006" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0c0007" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0c0008" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0c0009" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0c000a" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0d0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0d0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0d0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0d0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0d0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0d0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0d0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0d0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0d0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0d0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0d000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0d000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0d000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0d000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0d000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0d000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0d0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0d0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0d0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0d0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0d0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0d0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0d0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0d0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0d0018" />
    <public type="layout" name="abc_search_view" id="0x7f0d0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0d001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0d001b" />
    <public type="layout" name="activity_about" id="0x7f0d001c" />
    <public type="layout" name="activity_float" id="0x7f0d001d" />
    <public type="layout" name="activity_gms" id="0x7f0d001e" />
    <public type="layout" name="activity_list" id="0x7f0d001f" />
    <public type="layout" name="activity_main" id="0x7f0d0020" />
    <public type="layout" name="activity_map" id="0x7f0d0021" />
    <public type="layout" name="activity_osmdroid" id="0x7f0d0022" />
    <public type="layout" name="activity_pay" id="0x7f0d0023" />
    <public type="layout" name="activity_setting" id="0x7f0d0024" />
    <public type="layout" name="activity_xp" id="0x7f0d0025" />
    <public type="layout" name="admob_empty_layout" id="0x7f0d0026" />
    <public type="layout" name="agree" id="0x7f0d0027" />
    <public type="layout" name="base_empty" id="0x7f0d0028" />
    <public type="layout" name="base_loading" id="0x7f0d0029" />
    <public type="layout" name="base_retry" id="0x7f0d002a" />
    <public type="layout" name="bonuspack_bubble" id="0x7f0d002b" />
    <public type="layout" name="browser_actions_context_menu_page" id="0x7f0d002c" />
    <public type="layout" name="browser_actions_context_menu_row" id="0x7f0d002d" />
    <public type="layout" name="catloading_main" id="0x7f0d002e" />
    <public type="layout" name="custom_dialog" id="0x7f0d002f" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0d0030" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0d0031" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0d0032" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0d0033" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0d0034" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0d0035" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0d0036" />
    <public type="layout" name="design_navigation_item" id="0x7f0d0037" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0d0038" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0d0039" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0d003a" />
    <public type="layout" name="design_navigation_menu" id="0x7f0d003b" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0d003c" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0d003d" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0d003e" />
    <public type="layout" name="dot_layout" id="0x7f0d003f" />
    <public type="layout" name="en_floating_view" id="0x7f0d0040" />
    <public type="layout" name="exo_list_divider" id="0x7f0d0041" />
    <public type="layout" name="exo_player_control_view" id="0x7f0d0042" />
    <public type="layout" name="exo_player_view" id="0x7f0d0043" />
    <public type="layout" name="exo_styled_player_control_ffwd_button" id="0x7f0d0044" />
    <public type="layout" name="exo_styled_player_control_rewind_button" id="0x7f0d0045" />
    <public type="layout" name="exo_styled_player_control_view" id="0x7f0d0046" />
    <public type="layout" name="exo_styled_player_view" id="0x7f0d0047" />
    <public type="layout" name="exo_styled_settings_list" id="0x7f0d0048" />
    <public type="layout" name="exo_styled_settings_list_item" id="0x7f0d0049" />
    <public type="layout" name="exo_styled_sub_settings_list_item" id="0x7f0d004a" />
    <public type="layout" name="exo_track_selection_dialog" id="0x7f0d004b" />
    <public type="layout" name="expand_button" id="0x7f0d004c" />
    <public type="layout" name="fragment_apps" id="0x7f0d004d" />
    <public type="layout" name="hk_notification_wp" id="0x7f0d004e" />
    <public type="layout" name="hk_notification_wp_simple" id="0x7f0d004f" />
    <public type="layout" name="image_frame" id="0x7f0d0050" />
    <public type="layout" name="item_app" id="0x7f0d0051" />
    <public type="layout" name="item_fake" id="0x7f0d0052" />
    <public type="layout" name="item_gms" id="0x7f0d0053" />
    <public type="layout" name="item_gps" id="0x7f0d0054" />
    <public type="layout" name="item_package" id="0x7f0d0055" />
    <public type="layout" name="item_viewpager" id="0x7f0d0056" />
    <public type="layout" name="item_xp" id="0x7f0d0057" />
    <public type="layout" name="map_content_main" id="0x7f0d0058" />
    <public type="layout" name="material_chip_input_combo" id="0x7f0d0059" />
    <public type="layout" name="material_clock_display" id="0x7f0d005a" />
    <public type="layout" name="material_clock_display_divider" id="0x7f0d005b" />
    <public type="layout" name="material_clock_period_toggle" id="0x7f0d005c" />
    <public type="layout" name="material_clockface_textview" id="0x7f0d005e" />
    <public type="layout" name="material_clockface_view" id="0x7f0d005f" />
    <public type="layout" name="material_radial_view_group" id="0x7f0d0060" />
    <public type="layout" name="material_textinput_timepicker" id="0x7f0d0061" />
    <public type="layout" name="material_time_chip" id="0x7f0d0062" />
    <public type="layout" name="material_time_input" id="0x7f0d0063" />
    <public type="layout" name="material_timepicker" id="0x7f0d0064" />
    <public type="layout" name="material_timepicker_dialog" id="0x7f0d0065" />
    <public type="layout" name="material_timepicker_textinput_display" id="0x7f0d0066" />
    <public type="layout" name="md_dialog_base" id="0x7f0d0067" />
    <public type="layout" name="md_dialog_stub_buttons" id="0x7f0d0068" />
    <public type="layout" name="md_dialog_stub_input" id="0x7f0d0069" />
    <public type="layout" name="md_dialog_stub_message" id="0x7f0d006a" />
    <public type="layout" name="md_dialog_stub_recyclerview" id="0x7f0d006b" />
    <public type="layout" name="md_dialog_stub_scrollview" id="0x7f0d006c" />
    <public type="layout" name="md_dialog_stub_title" id="0x7f0d006d" />
    <public type="layout" name="md_listitem" id="0x7f0d006e" />
    <public type="layout" name="md_listitem_multichoice" id="0x7f0d006f" />
    <public type="layout" name="md_listitem_singlechoice" id="0x7f0d0070" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0d0071" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0d0072" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0d0073" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0d0074" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0d0075" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0d0076" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0d0077" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0d0078" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0d0079" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0d007a" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0d007b" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0d007c" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0d007d" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0d007e" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0d007f" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0d0080" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0d0081" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0d0082" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0d0083" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0d0084" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0d0085" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0d0086" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0d0087" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0d0088" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0d0089" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0d008a" />
    <public type="layout" name="mtrl_picker_text_input_date" id="0x7f0d008b" />
    <public type="layout" name="mtrl_picker_text_input_date_range" id="0x7f0d008c" />
    <public type="layout" name="notification_action" id="0x7f0d008d" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0d008e" />
    <public type="layout" name="notification_media_action" id="0x7f0d008f" />
    <public type="layout" name="notification_media_cancel_action" id="0x7f0d0090" />
    <public type="layout" name="notification_template_big_media" id="0x7f0d0091" />
    <public type="layout" name="notification_template_big_media_custom" id="0x7f0d0092" />
    <public type="layout" name="notification_template_big_media_narrow" id="0x7f0d0093" />
    <public type="layout" name="notification_template_big_media_narrow_custom" id="0x7f0d0094" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0d0095" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0d0096" />
    <public type="layout" name="notification_template_lines_media" id="0x7f0d0097" />
    <public type="layout" name="notification_template_media" id="0x7f0d0098" />
    <public type="layout" name="notification_template_media_custom" id="0x7f0d0099" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0d009a" />
    <public type="layout" name="notification_template_part_time" id="0x7f0d009b" />
    <public type="layout" name="period_pw" id="0x7f0d009c" />
    <public type="layout" name="period_pw_2" id="0x7f0d009d" />
    <public type="layout" name="player_remote" id="0x7f0d009e" />
    <public type="layout" name="powerspinner_item_default_power" id="0x7f0d009f" />
    <public type="layout" name="powerspinner_layout_body" id="0x7f0d00a0" />
    <public type="layout" name="powerspinner_layout_preference" id="0x7f0d00a1" />
    <public type="layout" name="preference" id="0x7f0d00a2" />
    <public type="layout" name="preference_category" id="0x7f0d00a3" />
    <public type="layout" name="preference_category_material" id="0x7f0d00a4" />
    <public type="layout" name="preference_dialog_edittext" id="0x7f0d00a5" />
    <public type="layout" name="preference_dropdown" id="0x7f0d00a6" />
    <public type="layout" name="preference_dropdown_material" id="0x7f0d00a7" />
    <public type="layout" name="preference_information" id="0x7f0d00a8" />
    <public type="layout" name="preference_information_material" id="0x7f0d00a9" />
    <public type="layout" name="preference_list_fragment" id="0x7f0d00aa" />
    <public type="layout" name="preference_material" id="0x7f0d00ab" />
    <public type="layout" name="preference_recyclerview" id="0x7f0d00ac" />
    <public type="layout" name="preference_widget_checkbox" id="0x7f0d00ad" />
    <public type="layout" name="preference_widget_seekbar" id="0x7f0d00ae" />
    <public type="layout" name="preference_widget_seekbar_material" id="0x7f0d00af" />
    <public type="layout" name="preference_widget_switch" id="0x7f0d00b0" />
    <public type="layout" name="preference_widget_switch_compat" id="0x7f0d00b1" />
    <public type="layout" name="rate_layout" id="0x7f0d00b2" />
    <public type="layout" name="search_view" id="0x7f0d00b3" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0d00b4" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0d00b5" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0d00b6" />
    <public type="layout" name="spring_dot_layout" id="0x7f0d00b7" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0d00b8" />
    <public type="layout" name="surface_view" id="0x7f0d00b9" />
    <public type="layout" name="test_action_chip" id="0x7f0d00ba" />
    <public type="layout" name="test_chip_zero_corner_radius" id="0x7f0d00bb" />
    <public type="layout" name="test_design_checkbox" id="0x7f0d00bc" />
    <public type="layout" name="test_design_radiobutton" id="0x7f0d00bd" />
    <public type="layout" name="test_reflow_chipgroup" id="0x7f0d00be" />
    <public type="layout" name="test_toolbar" id="0x7f0d00bf" />
    <public type="layout" name="test_toolbar_custom_background" id="0x7f0d00c0" />
    <public type="layout" name="test_toolbar_elevation" id="0x7f0d00c1" />
    <public type="layout" name="test_toolbar_surface" id="0x7f0d00c2" />
    <public type="layout" name="text_view_with_line_height_from_appearance" id="0x7f0d00c3" />
    <public type="layout" name="text_view_with_line_height_from_layout" id="0x7f0d00c4" />
    <public type="layout" name="text_view_with_line_height_from_style" id="0x7f0d00c5" />
    <public type="layout" name="text_view_with_theme_line_height" id="0x7f0d00c6" />
    <public type="layout" name="text_view_without_line_height" id="0x7f0d00c7" />
    <public type="layout" name="texture_view" id="0x7f0d00c8" />
    <public type="layout" name="vc_activity_camera_settings" id="0x7f0d00c9" />
    <public type="layout" name="vc_activity_settings" id="0x7f0d00ca" />
    <public type="layout" name="view_float_rocker" id="0x7f0d00cb" />
    <public type="layout" name="view_switch" id="0x7f0d00cc" />
    <public type="layout" name="view_toolbar" id="0x7f0d00cd" />
    <public type="layout" name="vlc_video_layout" id="0x7f0d00ce" />
    <public type="layout" name="worm_dot_layout" id="0x7f0d00cf" />
    <public type="layout" name="ic_empty" id="0x7f0d00d0" />
    <public type="layout" name="material_clock_period_toggle_land" id="0x7f0d005d" />
    <public type="menu" name="app_menu" id="0x7f0e0000" />
    <public type="menu" name="menu_list" id="0x7f0e0001" />
    <public type="menu" name="menu_main" id="0x7f0e0002" />
    <public type="menu" name="menu_search" id="0x7f0e0003" />
    <public type="menu" name="temp0" id="0x7f0e0004" />
    <public type="menu" name="temp1" id="0x7f0e0005" />
    <public type="menu" name="temp2" id="0x7f0e0006" />
    <public type="menu" name="temp3" id="0x7f0e0007" />
    <public type="menu" name="vc_camera_menu" id="0x7f0e0008" />
    <public type="menu" name="xp_menu" id="0x7f0e0009" />
    <public type="mipmap" name="ic_launcher" id="0x7f0f0000" />
    <public type="mipmap" name="ic_launcher_b" id="0x7f0f0001" />
    <public type="mipmap" name="sani_bg_3" id="0x7f0f0002" />
    <public type="plurals" name="exo_controls_fastforward_by_amount_description" id="0x7f100000" />
    <public type="plurals" name="exo_controls_rewind_by_amount_description" id="0x7f100001" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f100002" />
    <public type="raw" name="firebase_common_keep" id="0x7f110000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f120000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f120001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f120002" />
    <public type="string" name="abc_action_mode_done" id="0x7f120003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f120004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f120005" />
    <public type="string" name="abc_capital_off" id="0x7f120006" />
    <public type="string" name="abc_capital_on" id="0x7f120007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f120008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f120009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f12000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f12000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f12000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f12000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f12000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f12000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f120010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f120011" />
    <public type="string" name="abc_search_hint" id="0x7f120012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f120013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f120014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f120015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f120016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f120017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f120018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f120019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f12001a" />
    <public type="string" name="about" id="0x7f12001b" />
    <public type="string" name="about_message" id="0x7f12001c" />
    <public type="string" name="action_back" id="0x7f12001d" />
    <public type="string" name="action_clear_search" id="0x7f12001e" />
    <public type="string" name="action_goto" id="0x7f12001f" />
    <public type="string" name="action_voice_search" id="0x7f120020" />
    <public type="string" name="add_shortcut_fail_msg" id="0x7f120021" />
    <public type="string" name="androidx_startup" id="0x7f120022" />
    <public type="string" name="app_clear" id="0x7f120023" />
    <public type="string" name="app_clear_hint" id="0x7f120024" />
    <public type="string" name="app_name" id="0x7f120025" />
    <public type="string" name="app_remove" id="0x7f120026" />
    <public type="string" name="app_shortcut" id="0x7f120027" />
    <public type="string" name="app_stop" id="0x7f120028" />
    <public type="string" name="app_stop_hint" id="0x7f120029" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f12002a" />
    <public type="string" name="apply_for_apps" id="0x7f12002b" />
    <public type="string" name="assist_process" id="0x7f12002c" />
    <public type="string" name="base" id="0x7f12002d" />
    <public type="string" name="base_nl" id="0x7f12002e" />
    <public type="string" name="bing" id="0x7f12002f" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f120030" />
    <public type="string" name="bottomsheet_action_expand_halfway" id="0x7f120031" />
    <public type="string" name="build_libvlc_revision" id="0x7f120032" />
    <public type="string" name="build_vlc_revision" id="0x7f120033" />
    <public type="string" name="cancel" id="0x7f120034" />
    <public type="string" name="cannot_create_shortcut" id="0x7f120035" />
    <public type="string" name="character_counter_content_description" id="0x7f120036" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f120037" />
    <public type="string" name="character_counter_pattern" id="0x7f120038" />
    <public type="string" name="chip_text" id="0x7f120039" />
    <public type="string" name="choose" id="0x7f12003a" />
    <public type="string" name="choose_apk" id="0x7f12003b" />
    <public type="string" name="clear_success" id="0x7f12003c" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f12003d" />
    <public type="string" name="close_app_fake_location" id="0x7f12003e" />
    <public type="string" name="close_fake_location" id="0x7f12003f" />
    <public type="string" name="close_fake_location_success" id="0x7f120040" />
    <public type="string" name="common_google_play_services_enable_button" id="0x7f120041" />
    <public type="string" name="common_google_play_services_enable_text" id="0x7f120042" />
    <public type="string" name="common_google_play_services_enable_title" id="0x7f120043" />
    <public type="string" name="common_google_play_services_install_button" id="0x7f120044" />
    <public type="string" name="common_google_play_services_install_text" id="0x7f120045" />
    <public type="string" name="common_google_play_services_install_title" id="0x7f120046" />
    <public type="string" name="common_google_play_services_notification_channel_name" id="0x7f120047" />
    <public type="string" name="common_google_play_services_notification_ticker" id="0x7f120048" />
    <public type="string" name="common_google_play_services_unknown_issue" id="0x7f120049" />
    <public type="string" name="common_google_play_services_unsupported_text" id="0x7f12004a" />
    <public type="string" name="common_google_play_services_update_button" id="0x7f12004b" />
    <public type="string" name="common_google_play_services_update_text" id="0x7f12004c" />
    <public type="string" name="common_google_play_services_update_title" id="0x7f12004d" />
    <public type="string" name="common_google_play_services_updating_text" id="0x7f12004e" />
    <public type="string" name="common_google_play_services_wear_update_text" id="0x7f12004f" />
    <public type="string" name="common_open_on_phone" id="0x7f120050" />
    <public type="string" name="common_signin_button_text" id="0x7f120051" />
    <public type="string" name="common_signin_button_text_long" id="0x7f120052" />
    <public type="string" name="compass" id="0x7f120053" />
    <public type="string" name="confirm_location" id="0x7f120054" />
    <public type="string" name="copy" id="0x7f120055" />
    <public type="string" name="copy_toast_msg" id="0x7f120056" />
    <public type="string" name="cyclemap" id="0x7f120057" />
    <public type="string" name="daemon_enable" id="0x7f120058" />
    <public type="string" name="default_web_client_id" id="0x7f120059" />
    <public type="string" name="disable" id="0x7f12005a" />
    <public type="string" name="disable_gms" id="0x7f12005b" />
    <public type="string" name="disable_gms_hint" id="0x7f12005c" />
    <public type="string" name="done" id="0x7f12005d" />
    <public type="string" name="empty_empty" id="0x7f12005e" />
    <public type="string" name="empty_pro" id="0x7f12005f" />
    <public type="string" name="enable_gms" id="0x7f120060" />
    <public type="string" name="enable_gms_hint" id="0x7f120061" />
    <public type="string" name="enable_xposed" id="0x7f120062" />
    <public type="string" name="enable_xposed_module_first" id="0x7f120063" />
    <public type="string" name="error_icon_content_description" id="0x7f120064" />
    <public type="string" name="exo_controls_cc_disabled_description" id="0x7f120065" />
    <public type="string" name="exo_controls_cc_enabled_description" id="0x7f120066" />
    <public type="string" name="exo_controls_custom_playback_speed" id="0x7f120067" />
    <public type="string" name="exo_controls_fastforward_description" id="0x7f120068" />
    <public type="string" name="exo_controls_fullscreen_enter_description" id="0x7f120069" />
    <public type="string" name="exo_controls_fullscreen_exit_description" id="0x7f12006a" />
    <public type="string" name="exo_controls_hide" id="0x7f12006b" />
    <public type="string" name="exo_controls_next_description" id="0x7f12006c" />
    <public type="string" name="exo_controls_overflow_hide_description" id="0x7f12006d" />
    <public type="string" name="exo_controls_overflow_show_description" id="0x7f12006e" />
    <public type="string" name="exo_controls_pause_description" id="0x7f12006f" />
    <public type="string" name="exo_controls_play_description" id="0x7f120070" />
    <public type="string" name="exo_controls_playback_speed" id="0x7f120071" />
    <public type="string" name="exo_controls_previous_description" id="0x7f120072" />
    <public type="string" name="exo_controls_repeat_all_description" id="0x7f120073" />
    <public type="string" name="exo_controls_repeat_off_description" id="0x7f120074" />
    <public type="string" name="exo_controls_repeat_one_description" id="0x7f120075" />
    <public type="string" name="exo_controls_rewind_description" id="0x7f120076" />
    <public type="string" name="exo_controls_seek_bar_description" id="0x7f120077" />
    <public type="string" name="exo_controls_settings_description" id="0x7f120078" />
    <public type="string" name="exo_controls_show" id="0x7f120079" />
    <public type="string" name="exo_controls_shuffle_off_description" id="0x7f12007a" />
    <public type="string" name="exo_controls_shuffle_on_description" id="0x7f12007b" />
    <public type="string" name="exo_controls_stop_description" id="0x7f12007c" />
    <public type="string" name="exo_controls_time_placeholder" id="0x7f12007d" />
    <public type="string" name="exo_controls_vr_description" id="0x7f12007e" />
    <public type="string" name="exo_download_completed" id="0x7f12007f" />
    <public type="string" name="exo_download_description" id="0x7f120080" />
    <public type="string" name="exo_download_downloading" id="0x7f120081" />
    <public type="string" name="exo_download_failed" id="0x7f120082" />
    <public type="string" name="exo_download_notification_channel_name" id="0x7f120083" />
    <public type="string" name="exo_download_paused" id="0x7f120084" />
    <public type="string" name="exo_download_paused_for_network" id="0x7f120085" />
    <public type="string" name="exo_download_paused_for_wifi" id="0x7f120086" />
    <public type="string" name="exo_download_removing" id="0x7f120087" />
    <public type="string" name="exo_item_list" id="0x7f120088" />
    <public type="string" name="exo_track_bitrate" id="0x7f120089" />
    <public type="string" name="exo_track_mono" id="0x7f12008a" />
    <public type="string" name="exo_track_resolution" id="0x7f12008b" />
    <public type="string" name="exo_track_role_alternate" id="0x7f12008c" />
    <public type="string" name="exo_track_role_closed_captions" id="0x7f12008d" />
    <public type="string" name="exo_track_role_commentary" id="0x7f12008e" />
    <public type="string" name="exo_track_role_supplementary" id="0x7f12008f" />
    <public type="string" name="exo_track_selection_auto" id="0x7f120090" />
    <public type="string" name="exo_track_selection_none" id="0x7f120091" />
    <public type="string" name="exo_track_selection_title_audio" id="0x7f120092" />
    <public type="string" name="exo_track_selection_title_text" id="0x7f120093" />
    <public type="string" name="exo_track_selection_title_video" id="0x7f120094" />
    <public type="string" name="exo_track_stereo" id="0x7f120095" />
    <public type="string" name="exo_track_surround" id="0x7f120096" />
    <public type="string" name="exo_track_surround_5_point_1" id="0x7f120097" />
    <public type="string" name="exo_track_surround_7_point_1" id="0x7f120098" />
    <public type="string" name="exo_track_unknown" id="0x7f120099" />
    <public type="string" name="expand_button_title" id="0x7f12009a" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f12009b" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f12009c" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f12009d" />
    <public type="string" name="failed_select_location" id="0x7f12009e" />
    <public type="string" name="fake_location" id="0x7f12009f" />
    <public type="string" name="fallback_menu_item_copy_link" id="0x7f1200a0" />
    <public type="string" name="fallback_menu_item_open_in_browser" id="0x7f1200a1" />
    <public type="string" name="fallback_menu_item_share_link" id="0x7f1200a2" />
    <public type="string" name="fiets_nl" id="0x7f1200a3" />
    <public type="string" name="filter" id="0x7f1200a4" />
    <public type="string" name="first_fix_message" id="0x7f1200a5" />
    <public type="string" name="forget_about_limits" id="0x7f1200a6" />
    <public type="string" name="format_distance_feet" id="0x7f1200a7" />
    <public type="string" name="format_distance_kilometers" id="0x7f1200a8" />
    <public type="string" name="format_distance_meters" id="0x7f1200a9" />
    <public type="string" name="format_distance_miles" id="0x7f1200aa" />
    <public type="string" name="format_distance_nautical_miles" id="0x7f1200ab" />
    <public type="string" name="format_distance_only_foot" id="0x7f1200ac" />
    <public type="string" name="format_distance_only_kilometer" id="0x7f1200ad" />
    <public type="string" name="format_distance_only_meter" id="0x7f1200ae" />
    <public type="string" name="format_distance_only_mile" id="0x7f1200af" />
    <public type="string" name="format_distance_only_nautical_mile" id="0x7f1200b0" />
    <public type="string" name="format_distance_value_unit" id="0x7f1200b1" />
    <public type="string" name="gcm_defaultSenderId" id="0x7f1200b2" />
    <public type="string" name="gms_manager" id="0x7f1200b3" />
    <public type="string" name="go_to_set_location" id="0x7f1200b4" />
    <public type="string" name="google_api_key" id="0x7f1200b5" />
    <public type="string" name="google_app_id" id="0x7f1200b6" />
    <public type="string" name="google_crash_reporting_api_key" id="0x7f1200b7" />
    <public type="string" name="google_storage_bucket" id="0x7f1200b8" />
    <public type="string" name="gps_setting" id="0x7f1200b9" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f1200ba" />
    <public type="string" name="hide_root" id="0x7f1200bb" />
    <public type="string" name="hide_xposed" id="0x7f1200bc" />
    <public type="string" name="hider" id="0x7f1200bd" />
    <public type="string" name="hills" id="0x7f1200be" />
    <public type="string" name="icon_content_description" id="0x7f1200bf" />
    <public type="string" name="import_google" id="0x7f1200c0" />
    <public type="string" name="install_fail" id="0x7f1200c1" />
    <public type="string" name="install_fail_no_msg" id="0x7f1200c2" />
    <public type="string" name="install_success" id="0x7f1200c3" />
    <public type="string" name="installed_app" id="0x7f1200c4" />
    <public type="string" name="installed_module" id="0x7f1200c5" />
    <public type="string" name="is_stop" id="0x7f1200c6" />
    <public type="string" name="item_view_role_description" id="0x7f1200c7" />
    <public type="string" name="jump_gms" id="0x7f1200c8" />
    <public type="string" name="jump_module" id="0x7f1200c9" />
    <public type="string" name="kill_all" id="0x7f1200ca" />
    <public type="string" name="lbl_rate_app" id="0x7f1200cb" />
    <public type="string" name="loading" id="0x7f1200cc" />
    <public type="string" name="location_manager" id="0x7f1200cd" />
    <public type="string" name="map_mode" id="0x7f1200ce" />
    <public type="string" name="mapbox" id="0x7f1200cf" />
    <public type="string" name="mapnik" id="0x7f1200d0" />
    <public type="string" name="mapquest_aerial" id="0x7f1200d1" />
    <public type="string" name="mapquest_osm" id="0x7f1200d2" />
    <public type="string" name="material_clock_display_divider" id="0x7f1200d3" />
    <public type="string" name="material_clock_toggle_content_description" id="0x7f1200d4" />
    <public type="string" name="material_hour_selection" id="0x7f1200d5" />
    <public type="string" name="material_hour_suffix" id="0x7f1200d6" />
    <public type="string" name="material_minute_selection" id="0x7f1200d7" />
    <public type="string" name="material_minute_suffix" id="0x7f1200d8" />
    <public type="string" name="material_slider_range_end" id="0x7f1200d9" />
    <public type="string" name="material_slider_range_start" id="0x7f1200da" />
    <public type="string" name="material_timepicker_am" id="0x7f1200db" />
    <public type="string" name="material_timepicker_clock_mode_description" id="0x7f1200dc" />
    <public type="string" name="material_timepicker_hour" id="0x7f1200dd" />
    <public type="string" name="material_timepicker_minute" id="0x7f1200de" />
    <public type="string" name="material_timepicker_pm" id="0x7f1200df" />
    <public type="string" name="material_timepicker_select_time" id="0x7f1200e0" />
    <public type="string" name="material_timepicker_text_input_mode_description" id="0x7f1200e1" />
    <public type="string" name="module_list" id="0x7f1200e2" />
    <public type="string" name="module_setting" id="0x7f1200e3" />
    <public type="string" name="mokc_location_enable" id="0x7f1200e4" />
    <public type="string" name="month" id="0x7f1200e5" />
    <public type="string" name="month1" id="0x7f1200e6" />
    <public type="string" name="month3" id="0x7f1200e7" />
    <public type="string" name="month6" id="0x7f1200e8" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f1200e9" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f1200ea" />
    <public type="string" name="mtrl_exceed_max_badge_number_content_description" id="0x7f1200eb" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f1200ec" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f1200ed" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f1200ee" />
    <public type="string" name="mtrl_picker_announce_current_selection" id="0x7f1200ef" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f1200f0" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f1200f1" />
    <public type="string" name="mtrl_picker_date_header_selected" id="0x7f1200f2" />
    <public type="string" name="mtrl_picker_date_header_title" id="0x7f1200f3" />
    <public type="string" name="mtrl_picker_date_header_unselected" id="0x7f1200f4" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f1200f5" />
    <public type="string" name="mtrl_picker_invalid_format" id="0x7f1200f6" />
    <public type="string" name="mtrl_picker_invalid_format_example" id="0x7f1200f7" />
    <public type="string" name="mtrl_picker_invalid_format_use" id="0x7f1200f8" />
    <public type="string" name="mtrl_picker_invalid_range" id="0x7f1200f9" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f1200fa" />
    <public type="string" name="mtrl_picker_out_of_range" id="0x7f1200fb" />
    <public type="string" name="mtrl_picker_range_header_only_end_selected" id="0x7f1200fc" />
    <public type="string" name="mtrl_picker_range_header_only_start_selected" id="0x7f1200fd" />
    <public type="string" name="mtrl_picker_range_header_selected" id="0x7f1200fe" />
    <public type="string" name="mtrl_picker_range_header_title" id="0x7f1200ff" />
    <public type="string" name="mtrl_picker_range_header_unselected" id="0x7f120100" />
    <public type="string" name="mtrl_picker_save" id="0x7f120101" />
    <public type="string" name="mtrl_picker_text_input_date_hint" id="0x7f120102" />
    <public type="string" name="mtrl_picker_text_input_date_range_end_hint" id="0x7f120103" />
    <public type="string" name="mtrl_picker_text_input_date_range_start_hint" id="0x7f120104" />
    <public type="string" name="mtrl_picker_text_input_day_abbr" id="0x7f120105" />
    <public type="string" name="mtrl_picker_text_input_month_abbr" id="0x7f120106" />
    <public type="string" name="mtrl_picker_text_input_year_abbr" id="0x7f120107" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f120108" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f120109" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f12010a" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f12010b" />
    <public type="string" name="my_location" id="0x7f12010c" />
    <public type="string" name="native_body" id="0x7f12010d" />
    <public type="string" name="native_headline" id="0x7f12010e" />
    <public type="string" name="native_media_view" id="0x7f12010f" />
    <public type="string" name="no_ads" id="0x7f120110" />
    <public type="string" name="no_gms" id="0x7f120111" />
    <public type="string" name="no_permission" id="0x7f120112" />
    <public type="string" name="no_permission_query_packages" id="0x7f120113" />
    <public type="string" name="no_reminders" id="0x7f120114" />
    <public type="string" name="not_set" id="0x7f120115" />
    <public type="string" name="not_set_gps_location" id="0x7f120116" />
    <public type="string" name="not_support_area" id="0x7f120117" />
    <public type="string" name="notification_running_title" id="0x7f120118" />
    <public type="string" name="notification_running_warn" id="0x7f120119" />
    <public type="string" name="offline" id="0x7f12011a" />
    <public type="string" name="offline_notification_text" id="0x7f12011b" />
    <public type="string" name="offline_notification_title" id="0x7f12011c" />
    <public type="string" name="offline_opt_in_confirm" id="0x7f12011d" />
    <public type="string" name="offline_opt_in_confirmation" id="0x7f12011e" />
    <public type="string" name="offline_opt_in_decline" id="0x7f12011f" />
    <public type="string" name="offline_opt_in_message" id="0x7f120120" />
    <public type="string" name="offline_opt_in_title" id="0x7f120121" />
    <public type="string" name="open_source_path" id="0x7f120122" />
    <public type="string" name="other" id="0x7f120123" />
    <public type="string" name="password_toggle_content_description" id="0x7f120124" />
    <public type="string" name="path_password_eye" id="0x7f120125" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f120126" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f120127" />
    <public type="string" name="path_password_strike_through" id="0x7f120128" />
    <public type="string" name="permission_content" id="0x7f120129" />
    <public type="string" name="permission_setting" id="0x7f12012a" />
    <public type="string" name="preference_copied" id="0x7f12012b" />
    <public type="string" name="premium_features" id="0x7f12012c" />
    <public type="string" name="premium_month_text" id="0x7f12012d" />
    <public type="string" name="premium_month_text_saved" id="0x7f12012e" />
    <public type="string" name="premium_text" id="0x7f12012f" />
    <public type="string" name="premium_text_new" id="0x7f120130" />
    <public type="string" name="premium_year_text" id="0x7f120131" />
    <public type="string" name="privacy_police" id="0x7f120132" />
    <public type="string" name="privacy_police_link" id="0x7f120133" />
    <public type="string" name="pro" id="0x7f120134" />
    <public type="string" name="pro_features" id="0x7f120135" />
    <public type="string" name="project_id" id="0x7f120136" />
    <public type="string" name="public_transport" id="0x7f120137" />
    <public type="string" name="rate_text" id="0x7f120138" />
    <public type="string" name="rate_text2" id="0x7f120139" />
    <public type="string" name="rate_us" id="0x7f12013a" />
    <public type="string" name="real_location" id="0x7f12013b" />
    <public type="string" name="remove_ads" id="0x7f12013c" />
    <public type="string" name="remove_success" id="0x7f12013d" />
    <public type="string" name="restart_module" id="0x7f12013e" />
    <public type="string" name="roads_nl" id="0x7f12013f" />
    <public type="string" name="s1" id="0x7f120140" />
    <public type="string" name="s2" id="0x7f120141" />
    <public type="string" name="s3" id="0x7f120142" />
    <public type="string" name="s4" id="0x7f120143" />
    <public type="string" name="s5" id="0x7f120144" />
    <public type="string" name="s6" id="0x7f120145" />
    <public type="string" name="s7" id="0x7f120146" />
    <public type="string" name="samples" id="0x7f120147" />
    <public type="string" name="search_hint" id="0x7f120148" />
    <public type="string" name="search_menu_title" id="0x7f120149" />
    <public type="string" name="service_not_available" id="0x7f12014a" />
    <public type="string" name="service_process" id="0x7f12014b" />
    <public type="string" name="set_location" id="0x7f12014c" />
    <public type="string" name="set_location_first" id="0x7f12014d" />
    <public type="string" name="set_mode_hide_me" id="0x7f12014e" />
    <public type="string" name="set_mode_offline" id="0x7f12014f" />
    <public type="string" name="set_mode_online" id="0x7f120150" />
    <public type="string" name="set_mode_show_me" id="0x7f120151" />
    <public type="string" name="shortcut_name" id="0x7f120152" />
    <public type="string" name="snapshot" id="0x7f120153" />
    <public type="string" name="start_fail" id="0x7f120154" />
    <public type="string" name="start_in_outside" id="0x7f120155" />
    <public type="string" name="states" id="0x7f120156" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f120157" />
    <public type="string" name="str_continue" id="0x7f120158" />
    <public type="string" name="str_version" id="0x7f120159" />
    <public type="string" name="summary_collapsed_preference_list" id="0x7f12015a" />
    <public type="string" name="text_empty" id="0x7f12015b" />
    <public type="string" name="text_retry" id="0x7f12015c" />
    <public type="string" name="tg_group" id="0x7f12015d" />
    <public type="string" name="thanks_feedback" id="0x7f12015e" />
    <public type="string" name="topo" id="0x7f12015f" />
    <public type="string" name="try_add_shortcut" id="0x7f120160" />
    <public type="string" name="uninstall_app" id="0x7f120161" />
    <public type="string" name="uninstall_app_hint" id="0x7f120162" />
    <public type="string" name="uninstall_fail" id="0x7f120163" />
    <public type="string" name="uninstall_module" id="0x7f120164" />
    <public type="string" name="uninstall_module_hint" id="0x7f120165" />
    <public type="string" name="uninstall_module_toast" id="0x7f120166" />
    <public type="string" name="uninstall_success" id="0x7f120167" />
    <public type="string" name="unknown" id="0x7f120168" />
    <public type="string" name="unlock_unlimited_access" id="0x7f120169" />
    <public type="string" name="update_check" id="0x7f12016a" />
    <public type="string" name="use_mock_location" id="0x7f12016b" />
    <public type="string" name="use_real_location" id="0x7f12016c" />
    <public type="string" name="userRemark" id="0x7f12016d" />
    <public type="string" name="v7_preference_off" id="0x7f12016e" />
    <public type="string" name="v7_preference_on" id="0x7f12016f" />
    <public type="string" name="vc_app_desc" id="0x7f120170" />
    <public type="string" name="vc_app_name" id="0x7f120171" />
    <public type="string" name="vc_app_name_all" id="0x7f120172" />
    <public type="string" name="vc_audio_enable" id="0x7f120173" />
    <public type="string" name="vc_cancle" id="0x7f120174" />
    <public type="string" name="vc_choise_picture" id="0x7f120175" />
    <public type="string" name="vc_choise_video" id="0x7f120176" />
    <public type="string" name="vc_dialog_github_start" id="0x7f120177" />
    <public type="string" name="vc_goto_str" id="0x7f120178" />
    <public type="string" name="vc_killApps" id="0x7f120179" />
    <public type="string" name="vc_never_show" id="0x7f12017a" />
    <public type="string" name="vc_open_source" id="0x7f12017b" />
    <public type="string" name="vc_protect_method" id="0x7f12017c" />
    <public type="string" name="vc_protect_method_disable_camera" id="0x7f12017d" />
    <public type="string" name="vc_protect_method_local" id="0x7f12017e" />
    <public type="string" name="vc_protect_method_local_picture" id="0x7f12017f" />
    <public type="string" name="vc_protect_method_network" id="0x7f120180" />
    <public type="string" name="vc_protect_path_hint" id="0x7f120181" />
    <public type="string" name="vc_protect_tip_disable" id="0x7f120182" />
    <public type="string" name="vc_protect_tip_local" id="0x7f120183" />
    <public type="string" name="vc_protect_tip_local_picture" id="0x7f120184" />
    <public type="string" name="vc_protect_tip_network" id="0x7f120185" />
    <public type="string" name="vc_protect_tip_none" id="0x7f120186" />
    <public type="string" name="vc_save" id="0x7f120187" />
    <public type="string" name="vc_setting" id="0x7f120188" />
    <public type="string" name="vc_tips" id="0x7f120189" />
    <public type="string" name="vc_video_size_note" id="0x7f12018a" />
    <public type="string" name="watermark_label_prefix" id="0x7f12018b" />
    <public type="string" name="xp_module_setting" id="0x7f12018c" />
    <public type="string" name="xp_setting" id="0x7f12018d" />
    <public type="string" name="xposed_module_disable" id="0x7f12018e" />
    <public type="string" name="xposed_module_enable" id="0x7f12018f" />
    <public type="string" name="year1" id="0x7f120190" />
    <public type="string" name="yes" id="0x7f120191" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f130000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f130001" />
    <public type="style" name="AndroidThemeColorAccentYellow" id="0x7f130002" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f130003" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f130004" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f130005" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f130006" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f130007" />
    <public type="style" name="AppTheme" id="0x7f130008" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f130009" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f13000a" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f13000b" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f13000c" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f13000d" />
    <public type="style" name="Base.CardView" id="0x7f13000e" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f13000f" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f130010" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f130011" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f130012" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f130013" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f130014" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f130015" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f130016" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f130017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f130018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f130019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f13001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f13001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f13001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f13001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f13001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f13001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f130020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f130021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f130022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f130023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f130024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f130025" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f130026" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f130027" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f130028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f130029" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f13002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f13002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f13002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f13002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f13002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f13002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f130030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f130031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f130032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f130033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f130034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f130035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f130036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f130037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f130038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f130039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f13003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f13003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f13003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f13003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f13003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f13003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f130040" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f130041" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f130042" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f130043" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f130044" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f130045" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f130046" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f130047" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f130048" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f130049" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f13004a" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f13004b" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f13004c" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f13004d" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f13004e" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f13004f" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f130050" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f130051" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f130052" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f130053" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f130054" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f130055" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f130056" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f130057" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f130058" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f130059" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f13005a" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f13005b" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f13005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f13005d" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f13005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f13005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f130060" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f130061" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f130062" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f130063" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f130064" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f130065" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f130066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f130067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f130068" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f130069" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f13006a" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f13006b" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f13006c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f13006d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f13006e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f13006f" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f130070" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f130071" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f130072" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f130073" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f130074" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f130075" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f130076" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f130077" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f130078" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f130079" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f13007a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f13007b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f13007c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f13007d" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f13007e" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f13007f" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f130080" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f130081" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f130082" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f130083" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f130084" />
    <public type="style" name="Base.V21.Theme.MaterialComponents" id="0x7f130085" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Dialog" id="0x7f130086" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light" id="0x7f130087" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light.Dialog" id="0x7f130088" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f130089" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f13008a" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f13008b" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f13008c" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f13008d" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f13008e" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f13008f" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f130090" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f130091" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f130092" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f130093" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f130094" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f130095" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f130096" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f130097" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f130098" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f130099" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f13009a" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f13009b" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f13009c" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f13009d" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f13009e" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f13009f" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f1300a0" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f1300a1" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f1300a2" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f1300a3" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f1300a4" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f1300a5" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f1300a6" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f1300a7" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f1300a8" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1300a9" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f1300aa" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f1300ab" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f1300ac" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1300ad" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1300ae" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1300af" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f1300b0" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f1300b1" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1300b2" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1300b3" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1300b4" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1300b5" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1300b6" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1300b7" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1300b8" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1300b9" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1300ba" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1300bb" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1300bc" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1300bd" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1300be" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1300bf" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1300c0" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1300c1" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1300c2" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1300c3" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1300c4" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f1300c5" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f1300c6" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1300c7" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1300c8" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1300c9" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1300ca" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1300cb" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1300cc" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1300cd" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f1300ce" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1300cf" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f1300d0" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f1300d1" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1300d2" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1300d3" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1300d4" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f1300d5" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f1300d6" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f1300d7" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f1300d8" />
    <public type="style" name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton" id="0x7f1300d9" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f1300da" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f1300db" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f1300dc" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f1300dd" />
    <public type="style" name="Base.Widget.MaterialComponents.Slider" id="0x7f1300de" />
    <public type="style" name="Base.Widget.MaterialComponents.Snackbar" id="0x7f1300df" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f1300e0" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f1300e1" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f1300e2" />
    <public type="style" name="BasePreferenceThemeOverlay" id="0x7f1300e3" />
    <public type="style" name="CardView" id="0x7f1300e4" />
    <public type="style" name="CardView.Dark" id="0x7f1300e5" />
    <public type="style" name="CardView.Light" id="0x7f1300e6" />
    <public type="style" name="EmptyTheme" id="0x7f1300e7" />
    <public type="style" name="ExoMediaButton" id="0x7f1300e8" />
    <public type="style" name="ExoMediaButton.FastForward" id="0x7f1300e9" />
    <public type="style" name="ExoMediaButton.Next" id="0x7f1300ea" />
    <public type="style" name="ExoMediaButton.Pause" id="0x7f1300eb" />
    <public type="style" name="ExoMediaButton.Play" id="0x7f1300ec" />
    <public type="style" name="ExoMediaButton.Previous" id="0x7f1300ed" />
    <public type="style" name="ExoMediaButton.Rewind" id="0x7f1300ee" />
    <public type="style" name="ExoMediaButton.VR" id="0x7f1300ef" />
    <public type="style" name="ExoStyledControls" id="0x7f1300f0" />
    <public type="style" name="ExoStyledControls.Button" id="0x7f1300f1" />
    <public type="style" name="ExoStyledControls.Button.Bottom" id="0x7f1300f2" />
    <public type="style" name="ExoStyledControls.Button.Bottom.AudioTrack" id="0x7f1300f3" />
    <public type="style" name="ExoStyledControls.Button.Bottom.CC" id="0x7f1300f4" />
    <public type="style" name="ExoStyledControls.Button.Bottom.FullScreen" id="0x7f1300f5" />
    <public type="style" name="ExoStyledControls.Button.Bottom.OverflowHide" id="0x7f1300f6" />
    <public type="style" name="ExoStyledControls.Button.Bottom.OverflowShow" id="0x7f1300f7" />
    <public type="style" name="ExoStyledControls.Button.Bottom.PlaybackSpeed" id="0x7f1300f8" />
    <public type="style" name="ExoStyledControls.Button.Bottom.RepeatToggle" id="0x7f1300f9" />
    <public type="style" name="ExoStyledControls.Button.Bottom.Settings" id="0x7f1300fa" />
    <public type="style" name="ExoStyledControls.Button.Bottom.Shuffle" id="0x7f1300fb" />
    <public type="style" name="ExoStyledControls.Button.Bottom.VR" id="0x7f1300fc" />
    <public type="style" name="ExoStyledControls.Button.Center" id="0x7f1300fd" />
    <public type="style" name="ExoStyledControls.Button.Center.FfwdWithAmount" id="0x7f1300fe" />
    <public type="style" name="ExoStyledControls.Button.Center.Next" id="0x7f1300ff" />
    <public type="style" name="ExoStyledControls.Button.Center.PlayPause" id="0x7f130100" />
    <public type="style" name="ExoStyledControls.Button.Center.Previous" id="0x7f130101" />
    <public type="style" name="ExoStyledControls.Button.Center.RewWithAmount" id="0x7f130102" />
    <public type="style" name="ExoStyledControls.TimeBar" id="0x7f130103" />
    <public type="style" name="ExoStyledControls.TimeText" id="0x7f130104" />
    <public type="style" name="ExoStyledControls.TimeText.Duration" id="0x7f130105" />
    <public type="style" name="ExoStyledControls.TimeText.Position" id="0x7f130106" />
    <public type="style" name="ExoStyledControls.TimeText.Separator" id="0x7f130107" />
    <public type="style" name="MD_ActionButton" id="0x7f130108" />
    <public type="style" name="MD_Dark" id="0x7f130109" />
    <public type="style" name="MD_Dialog_CheckPrompt" id="0x7f13010a" />
    <public type="style" name="MD_Dialog_Icon" id="0x7f13010b" />
    <public type="style" name="MD_Dialog_Message" id="0x7f13010c" />
    <public type="style" name="MD_Dialog_ScrollView_FrameContent" id="0x7f13010d" />
    <public type="style" name="MD_Dialog_Title_Text" id="0x7f13010e" />
    <public type="style" name="MD_Light" id="0x7f13010f" />
    <public type="style" name="MD_ListItem" id="0x7f130110" />
    <public type="style" name="MD_ListItem.Choice" id="0x7f130111" />
    <public type="style" name="MD_ListItemText" id="0x7f130112" />
    <public type="style" name="MD_ListItemText.Choice" id="0x7f130113" />
    <public type="style" name="MD_ListItem_Control" id="0x7f130114" />
    <public type="style" name="MD_WindowAnimation" id="0x7f130115" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f130116" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f130117" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" id="0x7f130118" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" id="0x7f130119" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f13011a" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" id="0x7f13011b" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f13011c" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" id="0x7f13011d" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f13011e" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" id="0x7f13011f" />
    <public type="style" name="Platform.AppCompat" id="0x7f130120" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f130121" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f130122" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f130123" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f130124" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f130125" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f130126" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f130127" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f130128" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f130129" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f13012a" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f13012b" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f13012c" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f13012d" />
    <public type="style" name="PowerSpinnerStyle" id="0x7f13012e" />
    <public type="style" name="PowerSpinner_DropDown" id="0x7f13012f" />
    <public type="style" name="PowerSpinner_Elastic" id="0x7f130130" />
    <public type="style" name="PowerSpinner_Fade" id="0x7f130131" />
    <public type="style" name="Preference" id="0x7f130132" />
    <public type="style" name="Preference.Category" id="0x7f130133" />
    <public type="style" name="Preference.Category.Material" id="0x7f130134" />
    <public type="style" name="Preference.CheckBoxPreference" id="0x7f130135" />
    <public type="style" name="Preference.CheckBoxPreference.Material" id="0x7f130136" />
    <public type="style" name="Preference.DialogPreference" id="0x7f130137" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference" id="0x7f130138" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference.Material" id="0x7f130139" />
    <public type="style" name="Preference.DialogPreference.Material" id="0x7f13013a" />
    <public type="style" name="Preference.DropDown" id="0x7f13013b" />
    <public type="style" name="Preference.DropDown.Material" id="0x7f13013c" />
    <public type="style" name="Preference.Information" id="0x7f13013d" />
    <public type="style" name="Preference.Information.Material" id="0x7f13013e" />
    <public type="style" name="Preference.Material" id="0x7f13013f" />
    <public type="style" name="Preference.PreferenceScreen" id="0x7f130140" />
    <public type="style" name="Preference.PreferenceScreen.Material" id="0x7f130141" />
    <public type="style" name="Preference.SeekBarPreference" id="0x7f130142" />
    <public type="style" name="Preference.SeekBarPreference.Material" id="0x7f130143" />
    <public type="style" name="Preference.SwitchPreference" id="0x7f130144" />
    <public type="style" name="Preference.SwitchPreference.Material" id="0x7f130145" />
    <public type="style" name="Preference.SwitchPreferenceCompat" id="0x7f130146" />
    <public type="style" name="Preference.SwitchPreferenceCompat.Material" id="0x7f130147" />
    <public type="style" name="PreferenceCategoryTitleTextStyle" id="0x7f130148" />
    <public type="style" name="PreferenceFragment" id="0x7f130149" />
    <public type="style" name="PreferenceFragment.Material" id="0x7f13014a" />
    <public type="style" name="PreferenceFragmentList" id="0x7f13014b" />
    <public type="style" name="PreferenceFragmentList.Material" id="0x7f13014c" />
    <public type="style" name="PreferenceSummaryTextStyle" id="0x7f13014d" />
    <public type="style" name="PreferenceThemeOverlay" id="0x7f13014e" />
    <public type="style" name="PreferenceThemeOverlay.v14" id="0x7f13014f" />
    <public type="style" name="PreferenceThemeOverlay.v14.Material" id="0x7f130150" />
    <public type="style" name="RatingBar" id="0x7f130151" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f130152" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f130153" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f130154" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f130155" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f130156" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f130157" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f130158" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f130159" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f13015a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f13015b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f13015c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f13015d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f13015e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f13015f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f130160" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f130161" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f130162" />
    <public type="style" name="SearchViewIcon" id="0x7f130163" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f130164" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f130165" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f130166" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f130167" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Test" id="0x7f130168" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Tooltip" id="0x7f130169" />
    <public type="style" name="ShapeAppearanceOverlay" id="0x7f13016a" />
    <public type="style" name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize" id="0x7f13016b" />
    <public type="style" name="ShapeAppearanceOverlay.BottomRightCut" id="0x7f13016c" />
    <public type="style" name="ShapeAppearanceOverlay.Cut" id="0x7f13016d" />
    <public type="style" name="ShapeAppearanceOverlay.DifferentCornerSize" id="0x7f13016e" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f13016f" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f130170" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f130171" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f130172" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f130173" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f130174" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f130175" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f130176" />
    <public type="style" name="ShapeAppearanceOverlay.TopLeftCut" id="0x7f130177" />
    <public type="style" name="ShapeAppearanceOverlay.TopRightDifferentCornerSize" id="0x7f130178" />
    <public type="style" name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f130179" />
    <public type="style" name="Test.Theme.MaterialComponents.MaterialCalendar" id="0x7f13017a" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar" id="0x7f13017b" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f13017c" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f13017d" />
    <public type="style" name="TestStyleWithLineHeight" id="0x7f13017e" />
    <public type="style" name="TestStyleWithLineHeightAppearance" id="0x7f13017f" />
    <public type="style" name="TestStyleWithThemeLineHeightAttribute" id="0x7f130180" />
    <public type="style" name="TestStyleWithoutLineHeight" id="0x7f130181" />
    <public type="style" name="TestThemeWithLineHeight" id="0x7f130182" />
    <public type="style" name="TestThemeWithLineHeightDisabled" id="0x7f130183" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f130184" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f130185" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f130186" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f130187" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f130188" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f130189" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f13018a" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f13018b" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f13018c" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f13018d" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f13018e" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f13018f" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f130190" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f130191" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f130192" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f130193" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f130194" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f130195" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f130196" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f130197" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f130198" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f130199" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f13019a" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f13019b" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f13019c" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f13019d" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f13019e" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f13019f" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f1301a0" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f1301a1" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f1301a2" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f1301a3" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f1301a4" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f1301a5" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f1301a6" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f1301a7" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f1301a8" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f1301a9" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f1301aa" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f1301ab" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f1301ac" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f1301ad" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f1301ae" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f1301af" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f1301b0" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f1301b1" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f1301b2" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f1301b3" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f1301b4" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f1301b5" />
    <public type="style" name="TextAppearance.Compat.Notification.Info.Media" id="0x7f1301b6" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f1301b7" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2.Media" id="0x7f1301b8" />
    <public type="style" name="TextAppearance.Compat.Notification.Media" id="0x7f1301b9" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f1301ba" />
    <public type="style" name="TextAppearance.Compat.Notification.Time.Media" id="0x7f1301bb" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f1301bc" />
    <public type="style" name="TextAppearance.Compat.Notification.Title.Media" id="0x7f1301bd" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f1301be" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f1301bf" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f1301c0" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f1301c1" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f1301c2" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f1301c3" />
    <public type="style" name="TextAppearance.Design.Placeholder" id="0x7f1301c4" />
    <public type="style" name="TextAppearance.Design.Prefix" id="0x7f1301c5" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f1301c6" />
    <public type="style" name="TextAppearance.Design.Suffix" id="0x7f1301c7" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f1301c8" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f1301c9" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f1301ca" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f1301cb" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f1301cc" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f1301cd" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f1301ce" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f1301cf" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f1301d0" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f1301d1" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f1301d2" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f1301d3" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f1301d4" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f1301d5" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f1301d6" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f1301d7" />
    <public type="style" name="TextAppearance.MaterialComponents.TimePicker.Title" id="0x7f1301d8" />
    <public type="style" name="TextAppearance.MaterialComponents.Tooltip" id="0x7f1301d9" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f1301da" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f1301db" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f1301dc" />
    <public type="style" name="Theme.ActivityAgent" id="0x7f1301dd" />
    <public type="style" name="Theme.ActivityAgentT" id="0x7f1301de" />
    <public type="style" name="Theme.AppCompat" id="0x7f1301df" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f1301e0" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f1301e1" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f1301e2" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f1301e3" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f1301e4" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f1301e5" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f1301e6" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f1301e7" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f1301e8" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f1301e9" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f1301ea" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f1301eb" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f1301ec" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f1301ed" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f1301ee" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f1301ef" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f1301f0" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f1301f1" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f1301f2" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f1301f3" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f1301f4" />
    <public type="style" name="Theme.Design" id="0x7f1301f5" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f1301f6" />
    <public type="style" name="Theme.Design.Light" id="0x7f1301f7" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f1301f8" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f1301f9" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f1301fa" />
    <public type="style" name="Theme.IAPTheme" id="0x7f1301fb" />
    <public type="style" name="Theme.Main" id="0x7f1301fc" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f1301fd" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f1301fe" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f1301ff" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f130200" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f130201" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f130202" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f130203" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f130204" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f130205" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f130206" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f130207" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f130208" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f130209" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f13020a" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f13020b" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f13020c" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f13020d" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f13020e" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f13020f" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f130210" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f130211" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f130212" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f130213" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f130214" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f130215" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f130216" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f130217" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f130218" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f130219" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f13021a" />
    <public type="style" name="Theme.MaterialComponents.Light.BarSize" id="0x7f13021b" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f13021c" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f13021d" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f13021e" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f13021f" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f130220" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f130221" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f130222" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f130223" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f130224" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f130225" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f130226" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f130227" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f130228" />
    <public type="style" name="Theme.MaterialComponents.Light.LargeTouch" id="0x7f130229" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f13022a" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f13022b" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f13022c" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f13022d" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f13022e" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f13022f" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f130230" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f130231" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f130232" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f130233" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f130234" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f130235" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f130236" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f130237" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f130238" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f130239" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Primary" id="0x7f13023a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f13023b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f13023c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f13023d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f13023e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f13023f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f130240" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" id="0x7f130241" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" id="0x7f130242" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f130243" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f130244" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f130245" />
    <public type="style" name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f130246" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f130247" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f130248" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f130249" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f13024a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" id="0x7f13024b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f13024c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f13024d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" id="0x7f13024e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" id="0x7f13024f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" id="0x7f130250" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" id="0x7f130251" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" id="0x7f130252" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" id="0x7f130253" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f130254" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f130255" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f130256" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f130257" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f130258" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f130259" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f13025a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker" id="0x7f13025b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker.Display" id="0x7f13025c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Primary" id="0x7f13025d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Surface" id="0x7f13025e" />
    <public type="style" name="ThemeOverlayColorAccentRed" id="0x7f13025f" />
    <public type="style" name="ToolBarColorStyle" id="0x7f130260" />
    <public type="style" name="ToolbarTextPrimaryColorButtons" id="0x7f130261" />
    <public type="style" name="WelcomeTheme" id="0x7f130262" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f130263" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f130264" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f130265" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f130266" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f130267" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f130268" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f130269" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f13026a" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f13026b" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f13026c" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f13026d" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f13026e" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f13026f" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f130270" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f130271" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f130272" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f130273" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f130274" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f130275" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f130276" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f130277" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f130278" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f130279" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f13027a" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f13027b" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f13027c" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f13027d" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f13027e" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f13027f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f130280" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f130281" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f130282" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f130283" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f130284" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f130285" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f130286" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f130287" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f130288" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f130289" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f13028a" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f13028b" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f13028c" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f13028d" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f13028e" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f13028f" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f130290" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f130291" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f130292" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f130293" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f130294" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f130295" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f130296" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f130297" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f130298" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f130299" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f13029a" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f13029b" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f13029c" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f13029d" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f13029e" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f13029f" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f1302a0" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f1302a1" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f1302a2" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f1302a3" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f1302a4" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f1302a5" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f1302a6" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f1302a7" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f1302a8" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f1302a9" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f1302aa" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1302ab" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f1302ac" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f1302ad" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f1302ae" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f1302af" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f1302b0" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f1302b1" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f1302b2" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f1302b3" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f1302b4" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f1302b5" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f1302b6" />
    <public type="style" name="Widget.Design.TextInputEditText" id="0x7f1302b7" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f1302b8" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Primary" id="0x7f1302b9" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.PrimarySurface" id="0x7f1302ba" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Solid" id="0x7f1302bb" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f1302bc" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f1302bd" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" id="0x7f1302be" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f1302bf" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f1302c0" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f1302c1" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f1302c2" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f1302c3" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f1302c4" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f1302c5" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f1302c6" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" id="0x7f1302c7" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f1302c8" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f1302c9" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" id="0x7f1302ca" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f1302cb" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f1302cc" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f1302cd" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f1302ce" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f1302cf" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f1302d0" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f1302d1" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f1302d2" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f1302d3" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f1302d4" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f1302d5" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f1302d6" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f1302d7" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f1302d8" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f1302d9" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f1302da" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f1302db" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f1302dc" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f1302dd" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f1302de" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f1302df" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator" id="0x7f1302e0" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall" id="0x7f1302e1" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.Medium" id="0x7f1302e2" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.Small" id="0x7f1302e3" />
    <public type="style" name="Widget.MaterialComponents.CollapsingToolbar" id="0x7f1302e4" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f1302e5" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f1302e6" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f1302e7" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f1302e8" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f1302e9" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f1302ea" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f1302eb" />
    <public type="style" name="Widget.MaterialComponents.LinearProgressIndicator" id="0x7f1302ec" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f1302ed" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f1302ee" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f1302ef" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f1302f0" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f1302f1" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f1302f2" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f1302f3" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1302f4" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton" id="0x7f1302f5" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f1302f6" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f1302f7" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f1302f8" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f1302f9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f1302fa" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f1302fb" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f1302fc" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f1302fd" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton" id="0x7f1302fe" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthTextView" id="0x7f1302ff" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f130300" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f130301" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f130302" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton" id="0x7f130303" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f130304" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f130305" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f130306" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f130307" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f130308" />
    <public type="style" name="Widget.MaterialComponents.ProgressIndicator" id="0x7f130309" />
    <public type="style" name="Widget.MaterialComponents.ShapeableImageView" id="0x7f13030a" />
    <public type="style" name="Widget.MaterialComponents.Slider" id="0x7f13030b" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f13030c" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f13030d" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.TextView" id="0x7f13030e" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f13030f" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f130310" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.PrimarySurface" id="0x7f130311" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f130312" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f130313" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f130314" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f130315" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f130316" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f130317" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f130318" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f130319" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f13031a" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f13031b" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f13031c" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f13031d" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f13031e" />
    <public type="style" name="Widget.MaterialComponents.TimePicker" id="0x7f13031f" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Button" id="0x7f130320" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Clock" id="0x7f130321" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display" id="0x7f130322" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText" id="0x7f130323" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton" id="0x7f130324" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance" id="0x7f130325" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f130326" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Primary" id="0x7f130327" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.PrimarySurface" id="0x7f130328" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Surface" id="0x7f130329" />
    <public type="style" name="Widget.MaterialComponents.Tooltip" id="0x7f13032a" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f13032b" />
    <public type="style" name="cart_dialog" id="0x7f13032c" />
    <public type="xml" name="image_share_filepaths" id="0x7f150000" />
    <public type="xml" name="network_security_config" id="0x7f150001" />
    <public type="xml" name="remote_config_defaults" id="0x7f150002" />
    <public type="xml" name="setting" id="0x7f150003" />
    <public type="xml" name="standalone_badge" id="0x7f150004" />
    <public type="xml" name="standalone_badge_gravity_bottom_end" id="0x7f150005" />
    <public type="xml" name="standalone_badge_gravity_bottom_start" id="0x7f150006" />
    <public type="xml" name="standalone_badge_gravity_top_start" id="0x7f150007" />
    <public type="xml" name="standalone_badge_offset" id="0x7f150008" />
    <public type="xml" name="splits0" id="0x7f150009" />
</resources>