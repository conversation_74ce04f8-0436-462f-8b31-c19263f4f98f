.class public Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo00/o00oo0OO$o00oo0O;->o00oo00O(Lo0OOo00/o0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:Lo0OOo00/o0;

.field public final synthetic o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;


# direct methods
.method public varargs constructor <init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;Lo0OOo00/o0;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;->o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;

    iput-object p4, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;->o00oo0O:Lo0OOo00/o0;

    invoke-direct {p0, p2, p3}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public o00oOooo()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;->o00oo0Oo:Lo0OOo00/o00oo0OO$o00oo0O;

    iget-object v0, v0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00ooOo:Lo0OOo00/o0O0o;

    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;->o00oo0O:Lo0OOo00/o0;

    invoke-virtual {v0, v1}, Lo0OOo00/o0O0o;->o00oOOo0(Lo0OOo00/o0;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method
