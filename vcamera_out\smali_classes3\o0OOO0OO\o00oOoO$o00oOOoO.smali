.class public Lo0OOO0Oo/o00oOoO$o00oOOoO;
.super Lcom/google/android/gms/ads/interstitial/InterstitialAdLoadCallback;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oOoO;->o00oOoO(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oOoO;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oOoO;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    invoke-direct {p0}, Lcom/google/android/gms/ads/interstitial/InterstitialAdLoadCallback;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOOo0(Lcom/google/android/gms/ads/interstitial/InterstitialAd;)V
    .locals 2
    .param p1    # Lcom/google/android/gms/ads/interstitial/InterstitialAd;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    iget-object v0, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    invoke-static {v0, p1}, Lo0OOO0Oo/o00oOoO;->o00oOooO(Lo0OOO0Oo/o00oOoO;Lcom/google/android/gms/ads/interstitial/InterstitialAd;)Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    iget-object p1, p1, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    new-instance v0, Lo0OOO0Oo/o00oOoO$o00oOOoO$o00oOOo0;

    invoke-direct {v0, p0}, Lo0OOO0Oo/o00oOoO$o00oOOoO$o00oOOo0;-><init>(Lo0OOO0Oo/o00oOoO$o00oOOoO;)V

    invoke-virtual {p1, v0}, Lcom/google/android/gms/ads/interstitial/InterstitialAd;->setOnPaidEventListener(Lcom/google/android/gms/ads/OnPaidEventListener;)V

    const/16 p1, 0x13

    new-array p1, p1, [B

    fill-array-data p1, :array_0

    const/16 v0, 0x8

    new-array v1, v0, [B

    fill-array-data v1, :array_1

    invoke-static {p1, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 p1, 0xd

    new-array p1, p1, [B

    fill-array-data p1, :array_2

    new-array v0, v0, [B

    fill-array-data v0, :array_3

    invoke-static {p1, v0}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oOoO;->o00oOOoO:Z

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p1, Lo0OOO0Oo/o00oOoO;->o00oOo00:J

    return-void

    nop

    :array_0
    .array-data 1
        -0xct
        -0x29t
        0x6ct
        -0x4dt
        0x4ct
        0x66t
        -0x8t
        -0x3bt
        -0x39t
        -0x6t
        0x4ft
        -0x5ft
        0x47t
        0x75t
        -0x15t
        -0x10t
        -0x2ct
        -0x2ct
        0x44t
    .end array-data

    :array_1
    .array-data 1
        -0x4bt
        -0x4dt
        0x21t
        -0x2et
        0x22t
        0x7t
        -0x61t
        -0x60t
    .end array-data

    :array_2
    .array-data 1
        -0xat
        -0x67t
        0x78t
        0x2et
        -0x26t
        0x48t
        0x6ft
        -0x25t
        -0x4t
        -0x6dt
        0x17t
        0x64t
        -0x48t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x67t
        -0x9t
        0x39t
        0x4at
        -0x6at
        0x27t
        0xet
        -0x41t
    .end array-data
.end method

.method public onAdFailedToLoad(Lcom/google/android/gms/ads/LoadAdError;)V
    .locals 3
    .param p1    # Lcom/google/android/gms/ads/LoadAdError;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    const/16 v0, 0x13

    new-array v0, v0, [B

    fill-array-data v0, :array_0

    const/16 v1, 0x8

    new-array v2, v1, [B

    fill-array-data v2, :array_1

    invoke-static {v0, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    const/16 v0, 0x11

    new-array v0, v0, [B

    fill-array-data v0, :array_2

    new-array v1, v1, [B

    fill-array-data v1, :array_3

    invoke-static {v0, v1}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    invoke-virtual {p1}, Lcom/google/android/gms/ads/AdError;->getMessage()Ljava/lang/String;

    iget-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lo0OOO0Oo/o00oOoO;->o00oOooO(Lo0OOO0Oo/o00oOoO;Lcom/google/android/gms/ads/interstitial/InterstitialAd;)Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    iget-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oOoO;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lo0OOO0Oo/o00oOoO;->o00oOOoO:Z

    return-void

    nop

    :array_0
    .array-data 1
        0x53t
        -0x69t
        -0x2at
        -0x4bt
        0x2et
        0x63t
        0x32t
        0x3t
        0x60t
        -0x46t
        -0xbt
        -0x59t
        0x25t
        0x70t
        0x21t
        0x36t
        0x73t
        -0x6ct
        -0x2t
    .end array-data

    :array_1
    .array-data 1
        0x12t
        -0xdt
        -0x65t
        -0x2ct
        0x40t
        0x2t
        0x55t
        0x66t
    .end array-data

    :array_2
    .array-data 1
        -0x74t
        0x78t
        0x62t
        0x67t
        -0x42t
        0x36t
        -0x37t
        0x1at
        -0x7at
        0x72t
        0x77t
        0x6ct
        -0x4ct
        0x38t
        -0x3ft
        0x12t
        -0x27t
    .end array-data

    nop

    :array_3
    .array-data 1
        -0x1dt
        0x16t
        0x23t
        0x3t
        -0x8t
        0x57t
        -0x60t
        0x76t
    .end array-data
.end method

.method public bridge synthetic onAdLoaded(Ljava/lang/Object;)V
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    check-cast p1, Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    invoke-virtual {p0, p1}, Lo0OOO0Oo/o00oOoO$o00oOOoO;->o00oOOo0(Lcom/google/android/gms/ads/interstitial/InterstitialAd;)V

    return-void
.end method
