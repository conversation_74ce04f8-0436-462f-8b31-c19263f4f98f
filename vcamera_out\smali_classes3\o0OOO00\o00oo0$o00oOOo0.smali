.class public Lo0OOo00/o00oo0$o00oOOo0;
.super Lokio/o00oo0O0;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oOOo0"
.end annotation


# instance fields
.field public final synthetic o00oo0O:Lo0OOo00/o00oo0;


# direct methods
.method public constructor <init>(Lo0OOo00/o00oo0;Lokio/o0OoO00O;)V
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0$o00oOOo0;->o00oo0O:Lo0OOo00/o00oo0;

    invoke-direct {p0, p2}, Lokio/o00oo0O0;-><init>(Lokio/o0OoO00O;)V

    return-void
.end method


# virtual methods
.method public close()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0$o00oOOo0;->o00oo0O:Lo0OOo00/o00oo0;

    iget-object v1, v0, Lo0OOo00/o00oo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    const/4 v2, 0x0

    invoke-virtual {v1, v2, v0}, Lo0OOOooO/o0OoO00O;->o00oo0O0(ZLo0OOOooo/o0O00OO;)V

    invoke-super {p0}, Lokio/o00oo0O0;->close()V

    return-void
.end method
