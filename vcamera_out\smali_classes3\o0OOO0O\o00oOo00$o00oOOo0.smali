.class public Lo0OOo0O/o00oOo00$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOo0O/o00oOo00;-><init>(Lokhttp3/o0O00OOO;Lokhttp3/o0O0O0Oo;Ljava/util/Random;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOo0O/o00oOo00;


# direct methods
.method public constructor <init>(Lo0OOo0O/o00oOo00;)V
    .locals 0

    iput-object p1, p0, Lo0OOo0O/o00oOo00$o00oOOo0;->o00oo0O0:Lo0OOo0O/o00oOo00;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    :goto_0
    :try_start_0
    iget-object v0, p0, Lo0OOo0O/o00oOo00$o00oOOo0;->o00oo0O0:Lo0OOo0O/o00oOo00;

    invoke-virtual {v0}, Lo0OOo0O/o00oOo00;->o00ooO0()Z

    move-result v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz v0, :cond_0

    goto :goto_0

    :catch_0
    move-exception v0

    iget-object v1, p0, Lo0OOo0O/o00oOo00$o00oOOo0;->o00oo0O0:Lo0OOo0O/o00oOo00;

    const/4 v2, 0x0

    invoke-virtual {v1, v0, v2}, Lo0OOo0O/o00oOo00;->o00oo0OO(Ljava/lang/Exception;Lokhttp3/o0O00o00;)V

    :cond_0
    return-void
.end method
