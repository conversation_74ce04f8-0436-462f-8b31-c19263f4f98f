.class public interface abstract annotation Lo0OO0OoO/o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->CLASS:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\n\n\u0002\u0018\u0002\n\u0002\u0010\u001b\n\u0000\u0008\u0087\u0002\u0018\u00002\u00020\u0001B\u0000\u00a8\u0006\u0002"
    }
    d2 = {
        "Lo0OO0OoO/o00oOOo0;",
        "",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation

.annotation runtime Lo0O/o00oOo00;
.end annotation

.annotation runtime Lo0O/o00oOoO;
    value = .enum Lo0O/o00oOOo0;->BINARY:Lo0O/o00oOOo0;
.end annotation

.annotation build Lo0O0oooo/oO000OOo;
    level = .enum Lo0O0oooo/oO000OOo$o00oOOo0;->WARNING:Lo0O0oooo/oO000OOo$o00oOOo0;
.end annotation

.annotation build Lo0O0oooo/oO000Oo;
    version = "1.4"
.end annotation
