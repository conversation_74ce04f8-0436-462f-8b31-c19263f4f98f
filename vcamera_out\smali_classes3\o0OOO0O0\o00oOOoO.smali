.class public final Lo0OOO0O0/o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Landroid/annotation/SuppressLint;
    value = {
        "all"
    }
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOO0O0/o00oOOoO$o00oOOo0;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0006\u0008\u00c1\u0002\u0018\u00002\u00020\u0001:\u0001\u000eB\t\u0008\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001a\u0010\u0007\u001a\u00020\u00062\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0008\u0010\u0008\u001a\u00020\u0006H\u0002R\u0014\u0010\u000b\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0008\u0010\n\u00a8\u0006\u000f"
    }
    d2 = {
        "Lo0OOO0O0/o00oOOoO;",
        "",
        "",
        "args",
        "Ljava/lang/instrument/Instrumentation;",
        "instrumentation",
        "Lo0O0oooo/oO0O00o0;",
        "o00oOooO",
        "o00oOOoO",
        "",
        "Z",
        "enableCreationStackTraces",
        "<init>",
        "()V",
        "o00oOOo0",
        "kotlinx-coroutines-core"
    }
    k = 0x1
    mv = {
        0x1,
        0x6,
        0x0
    }
.end annotation

.annotation build Lorg/codehaus/mojo/animal_sniffer/IgnoreJRERequirement;
.end annotation


# static fields
.field public static final o00oOOo0:Lo0OOO0O0/o00oOOoO;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final o00oOOoO:Z


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    new-instance v0, Lo0OOO0O0/o00oOOoO;

    invoke-direct {v0}, Lo0OOO0O0/o00oOOoO;-><init>()V

    sput-object v0, Lo0OOO0O0/o00oOOoO;->o00oOOo0:Lo0OOO0O0/o00oOOoO;

    const/4 v0, 0x0

    :try_start_0
    sget-object v1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    const-string v1, "kotlinx.coroutines.debug.enable.creation.stack.trace"

    invoke-static {v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-static {v1}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    goto :goto_0

    :cond_0
    move-object v1, v0

    :goto_0
    invoke-static {v1}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v1

    sget-object v2, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {v1}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    :goto_1
    invoke-static {v1}, Lo0O0oooo/ooOOOOoo;->isFailure-impl(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_2

    :cond_1
    move-object v0, v1

    :goto_2
    check-cast v0, Ljava/lang/Boolean;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    goto :goto_3

    :cond_2
    sget-object v0, Lkotlinx/coroutines/debug/internal/o00oo0;->o00oOOo0:Lkotlinx/coroutines/debug/internal/o00oo0;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    sget-boolean v0, Lkotlinx/coroutines/debug/internal/o00oo0;->o00oOoOo:Z

    :goto_3
    sput-boolean v0, Lo0OOO0O0/o00oOOoO;->o00oOOoO:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic o00oOOo0(Lsun/misc/Signal;)V
    .locals 0

    invoke-static {p0}, Lo0OOO0O0/o00oOOoO;->o00oOo00(Lsun/misc/Signal;)V

    return-void
.end method

.method public static final o00oOo00(Lsun/misc/Signal;)V
    .locals 1

    sget-object p0, Lkotlinx/coroutines/debug/internal/o00oo0;->o00oOOo0:Lkotlinx/coroutines/debug/internal/o00oo0;

    invoke-virtual {p0}, Lkotlinx/coroutines/debug/internal/o00oo0;->o00ooO0O()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p0, v0}, Lkotlinx/coroutines/debug/internal/o00oo0;->o00oOo0o(Ljava/io/PrintStream;)V

    goto :goto_0

    :cond_0
    const-string p0, "Cannot perform coroutines dump, debug probes are disabled"

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0, p0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public static final o00oOooO(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;)V
    .locals 1
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .param p1    # Ljava/lang/instrument/Instrumentation;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    sget-object p0, Lkotlinx/coroutines/debug/internal/o00oOOo0;->o00oOOo0:Lkotlinx/coroutines/debug/internal/o00oOOo0;

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lkotlinx/coroutines/debug/internal/o00oOOo0;->o00oOOoO(Z)V

    sget-object p0, Lo0OOO0O0/o00oOOoO$o00oOOo0;->o00oOOo0:Lo0OOO0O0/o00oOOoO$o00oOOo0;

    check-cast p0, Ljava/lang/instrument/ClassFileTransformer;

    invoke-interface {p1, p0}, Ljava/lang/instrument/Instrumentation;->addTransformer(Ljava/lang/instrument/ClassFileTransformer;)V

    sget-object p0, Lkotlinx/coroutines/debug/internal/o00oo0;->o00oOOo0:Lkotlinx/coroutines/debug/internal/o00oo0;

    sget-boolean p1, Lo0OOO0O0/o00oOOoO;->o00oOOoO:Z

    invoke-virtual {p0, p1}, Lkotlinx/coroutines/debug/internal/o00oo0;->o00ooo0(Z)V

    invoke-virtual {p0}, Lkotlinx/coroutines/debug/internal/o00oo0;->o00ooO00()V

    sget-object p0, Lo0OOO0O0/o00oOOoO;->o00oOOo0:Lo0OOO0O0/o00oOOoO;

    invoke-virtual {p0}, Lo0OOO0O0/o00oOOoO;->o00oOOoO()V

    return-void
.end method


# virtual methods
.method public final o00oOOoO()V
    .locals 2

    :try_start_0
    new-instance v0, Lsun/misc/Signal;

    const-string v1, "TRAP"

    invoke-direct {v0, v1}, Lsun/misc/Signal;-><init>(Ljava/lang/String;)V

    new-instance v1, Lo0OOO0O0/o00oOOo0;

    invoke-direct {v1}, Lo0OOO0O0/o00oOOo0;-><init>()V

    invoke-static {v0, v1}, Lsun/misc/Signal;->handle(Lsun/misc/Signal;Lsun/misc/SignalHandler;)Lsun/misc/SignalHandler;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    return-void
.end method
