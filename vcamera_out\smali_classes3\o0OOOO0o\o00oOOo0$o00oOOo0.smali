.class public Lo0OOOO0o/o00oOOo0$o00oOOo0;
.super Lde/robv/android/xposed/XC_MethodHook;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOOO0o/o00oOOo0;->o00oOOo0()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOOO0o/o00oOOo0;


# direct methods
.method public constructor <init>(Lo0OOOO0o/o00oOOo0;)V
    .locals 0

    iput-object p1, p0, Lo0OOOO0o/o00oOOo0$o00oOOo0;->o00oo0O0:Lo0OOOO0o/o00oOOo0;

    invoke-direct {p0}, Lde/robv/android/xposed/XC_MethodHook;-><init>()V

    return-void
.end method


# virtual methods
.method public beforeHookedMethod(Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lde/robv/android/xposed/XC_MethodHook$MethodHookParam;->setResult(Ljava/lang/Object;)V

    return-void
.end method
