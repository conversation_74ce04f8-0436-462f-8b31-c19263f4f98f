.class public Lo0OOO0Oo/o00oo00O$o00oOOoO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/ads/OnPaidEventListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0(Lcom/google/android/gms/ads/rewardedinterstitial/RewardedInterstitialAd;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oo00O$o00oOOoO;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oo00O$o00oOOoO;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO$o00oOOo0;->o00oOOo0:Lo0OOO0Oo/o00oo00O$o00oOOoO;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onPaidEvent(Lcom/google/android/gms/ads/AdValue;)V
    .locals 1
    .param p1    # Lcom/google/android/gms/ads/AdValue;
        .annotation build Lo00oOo00/o0O0OOO0;
        .end annotation
    .end param

    iget-object v0, p0, Lo0OOO0Oo/o00oo00O$o00oOOoO$o00oOOo0;->o00oOOo0:Lo0OOO0Oo/o00oo00O$o00oOOoO;

    iget-object v0, v0, Lo0OOO0Oo/o00oo00O$o00oOOoO;->o00oOOo0:Lo0OOO0Oo/o00oo00O;

    invoke-virtual {v0, p1}, Lo0OOO0Oo/o00oo00O;->o00oOoOO(Lcom/google/android/gms/ads/AdValue;)V

    return-void
.end method
