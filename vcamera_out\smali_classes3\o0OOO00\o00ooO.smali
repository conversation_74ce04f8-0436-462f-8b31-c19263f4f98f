.class public interface abstract Lo0OOo00/o00ooO;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final o00oOOo0:Lo0OOo00/o00ooO;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOo00/o00ooO$o00oOOo0;

    invoke-direct {v0}, Lo0OOo00/o00ooO$o00oOOo0;-><init>()V

    sput-object v0, Lo0OOo00/o00ooO;->o00oOOo0:Lo0OOo00/o00ooO;

    return-void
.end method


# virtual methods
.method public abstract o00oOOo0(ILo0OOo00/o00oOo00;)V
.end method

.method public abstract o00oOOoO(ILjava/util/List;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)Z"
        }
    .end annotation
.end method

.method public abstract o00oOo00(ILjava/util/List;Z)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;Z)Z"
        }
    .end annotation
.end method

.method public abstract o00oOooO(ILokio/o00oOoO;IZ)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
