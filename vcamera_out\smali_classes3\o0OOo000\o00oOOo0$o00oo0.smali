.class public Lo0OOo000/o00oOOo0$o00oo0;
.super Lo0OOo000/o00oOOo0$o00oOOoO;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oo0"
.end annotation


# instance fields
.field public final synthetic o00oo0o:Lo0OOo000/o00oOOo0;

.field public o00oo0o0:Z


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;)V
    .locals 0

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oo0;->o00oo0o:Lo0OOo000/o00oOOo0;

    invoke-direct {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;-><init>(Lo0OOo000/o00oOOo0;)V

    return-void
.end method


# virtual methods
.method public close()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oo0;->o00oo0o0:Z

    if-nez v0, :cond_1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    :cond_1
    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    return-void
.end method

.method public o0O0o0oO(Lokio/o00oOo00;J)J
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-wide/16 v0, 0x0

    cmp-long v0, p2, v0

    if-ltz v0, :cond_3

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O:Z

    if-nez v0, :cond_2

    iget-boolean v0, p0, Lo0OOo000/o00oOOo0$o00oo0;->o00oo0o0:Z

    const-wide/16 v1, -0x1

    if-eqz v0, :cond_0

    return-wide v1

    :cond_0
    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oo0;->o00oo0o:Lo0OOo000/o00oOOo0;

    iget-object v0, v0, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {v0, p1, p2, p3}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide p1

    cmp-long p3, p1, v1

    if-nez p3, :cond_1

    const/4 p1, 0x1

    iput-boolean p1, p0, Lo0OOo000/o00oOOo0$o00oo0;->o00oo0o0:Z

    invoke-virtual {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oOOo0(Z)V

    return-wide v1

    :cond_1
    return-wide p1

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "byteCount < 0: "

    invoke-static {v0, p2, p3}, Lo0O0O0O/o00oOo0O;->o00oOOo0(Ljava/lang/String;J)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
