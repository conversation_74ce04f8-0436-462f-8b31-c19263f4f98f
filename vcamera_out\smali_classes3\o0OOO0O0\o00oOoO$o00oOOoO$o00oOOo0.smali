.class public Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0o0/o00oOoO$o00oOOoO;->onActivityResumed(Landroid/app/Activity;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O:Lo0OOO0o0/o00oOoO$o00oOOoO;

.field public final synthetic o00oo0O0:Landroid/app/Activity;


# direct methods
.method public constructor <init>(Lo0OOO0o0/o00oOoO$o00oOOoO;Landroid/app/Activity;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    iput-object p1, p0, Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;->o00oo0O:Lo0OOO0o0/o00oOoO$o00oOOoO;

    iput-object p2, p0, Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;->o00oo0O0:Landroid/app/Activity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    iget-object v0, p0, Lo0OOO0o0/o00oOoO$o00oOOoO$o00oOOo0;->o00oo0O0:Landroid/app/Activity;

    invoke-static {v0}, Lmultispace/multiapp/clone/util/o00ooO;->o00oo0oO(Landroid/app/Activity;)V

    return-void
.end method
