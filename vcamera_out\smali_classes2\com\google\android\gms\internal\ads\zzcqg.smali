.class public final Lcom/google/android/gms/internal/ads/zzcqg;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzgwy;


# instance fields
.field private final zza:Lcom/google/android/gms/internal/ads/zzcqe;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzcqe;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzcqg;->zza:Lcom/google/android/gms/internal/ads/zzcqe;

    return-void
.end method


# virtual methods
.method public final synthetic zzb()Ljava/lang/Object;
    .locals 1
    .annotation build Lo00oOo00/Class7651;
    .end annotation

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcqg;->zza:Lcom/google/android/gms/internal/ads/zzcqe;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzcqe;->zzb()Lcom/google/android/gms/internal/ads/zzczk;

    move-result-object v0

    return-object v0
.end method
