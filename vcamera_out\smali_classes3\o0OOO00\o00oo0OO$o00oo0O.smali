.class public Lo0OOo00/o00oo0OO$o00oo0O;
.super Lo0OOOoOo/o0O00000;
.source "SourceFile"

# interfaces
.implements Lo0OOo00/o00oo0O0$o00oOOoO;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0OO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "o00oo0O"
.end annotation


# instance fields
.field public final o00oo0O:Lo0OOo00/o00oo0O0;

.field public final synthetic o00oo0Oo:Lo0OOo00/o00oo0OO;


# direct methods
.method public constructor <init>(Lo0OOo00/o00oo0OO;Lo0OOo00/o00oo0O0;)V
    .locals 2

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget-object p1, p1, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    aput-object p1, v0, v1

    const-string p1, "OkHttp %s"

    invoke-direct {p0, p1, v0}, Lo0OOOoOo/o0O00000;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    iput-object p2, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0O:Lo0OOo00/o00oo0O0;

    return-void
.end method


# virtual methods
.method public o00oOOo0(ILo0OOo00/o00oOo00;Lokio/o00oo00O;)V
    .locals 3

    invoke-virtual {p3}, Lokio/o00oo00O;->size()I

    iget-object p2, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    monitor-enter p2

    :try_start_0
    iget-object p3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object p3, p3, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {p3}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object p3

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v0, v0, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    new-array v0, v0, [Lo0OOo00/o00oo0O;

    invoke-interface {p3, v0}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p3

    check-cast p3, [Lo0OOo00/o00oo0O;

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    const/4 v1, 0x1

    iput-boolean v1, v0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    monitor-exit p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    array-length p2, p3

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p2, :cond_1

    aget-object v1, p3, v0

    iget v2, v1, Lo0OOo00/o00oo0O;->o00oOo00:I

    if-le v2, p1, :cond_0

    invoke-virtual {v1}, Lo0OOo00/o00oo0O;->o00oo00O()Z

    move-result v2

    if-eqz v2, :cond_0

    sget-object v2, Lo0OOo00/o00oOo00;->REFUSED_STREAM:Lo0OOo00/o00oOo00;

    invoke-virtual {v1, v2}, Lo0OOo00/o00oo0O;->o00oo0o0(Lo0OOo00/o00oOo00;)V

    iget-object v2, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget v1, v1, Lo0OOo00/o00oo0O;->o00oOo00:I

    invoke-virtual {v2, v1}, Lo0OOo00/o00oo0OO;->o0O000o(I)Lo0OOo00/o00oo0O;

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public o00oOOoO(ZLo0OOo00/o0;)V
    .locals 10

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v1, v1, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    invoke-virtual {v1}, Lo0OOo00/o0;->o00oOo0O()I

    move-result v1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object p1, p1, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    invoke-virtual {p1}, Lo0OOo00/o0;->o00oOOo0()V

    :cond_0
    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object p1, p1, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    invoke-virtual {p1, p2}, Lo0OOo00/o0;->o00oOoOo(Lo0OOo00/o0;)V

    invoke-virtual {p0, p2}, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo00O(Lo0OOo00/o0;)V

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object p1, p1, Lo0OOo00/o00oo0OO;->o00ooOO:Lo0OOo00/o0;

    invoke-virtual {p1}, Lo0OOo00/o0;->o00oOo0O()I

    move-result p1

    const/4 p2, -0x1

    const/4 v2, 0x1

    const-wide/16 v3, 0x0

    const/4 v5, 0x0

    if-eq p1, p2, :cond_2

    if-eq p1, v1, :cond_2

    sub-int/2addr p1, v1

    int-to-long p1, p1

    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-boolean v6, v1, Lo0OOo00/o00oo0OO;->o00ooOOo:Z

    if-nez v6, :cond_1

    invoke-virtual {v1, p1, p2}, Lo0OOo00/o00oo0OO;->o00oOOo0(J)V

    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iput-boolean v2, v1, Lo0OOo00/o00oo0OO;->o00ooOOo:Z

    :cond_1
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v1, v1, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_3

    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v1, v1, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v1

    iget-object v5, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v5, v5, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-interface {v5}, Ljava/util/Map;->size()I

    move-result v5

    new-array v5, v5, [Lo0OOo00/o00oo0O;

    invoke-interface {v1, v5}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, [Lo0OOo00/o00oo0O;

    goto :goto_0

    :cond_2
    move-wide p1, v3

    :cond_3
    :goto_0
    sget-object v1, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance v6, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOoO;

    const-string v7, "OkHttp %s settings"

    new-array v2, v2, [Ljava/lang/Object;

    iget-object v8, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v8, v8, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    const/4 v9, 0x0

    aput-object v8, v2, v9

    invoke-direct {v6, p0, v7, v2}, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOoO;-><init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;)V

    invoke-interface {v1, v6}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    if-eqz v5, :cond_4

    cmp-long v0, p1, v3

    if-eqz v0, :cond_4

    array-length v0, v5

    :goto_1
    if-ge v9, v0, :cond_4

    aget-object v1, v5, v9

    monitor-enter v1

    :try_start_1
    invoke-virtual {v1, p1, p2}, Lo0OOo00/o00oo0O;->o00oOOo0(J)V

    monitor-exit v1

    add-int/lit8 v9, v9, 0x1

    goto :goto_1

    :catchall_0
    move-exception p1

    monitor-exit v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_4
    return-void

    :catchall_1
    move-exception p1

    :try_start_2
    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1
.end method

.method public o00oOo00(ZIILjava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZII",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation

    iget-object p3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {p3, p2}, Lo0OOo00/o00oo0OO;->o0OoOoOo(I)Z

    move-result p3

    if-eqz p3, :cond_0

    iget-object p3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {p3, p2, p4, p1}, Lo0OOo00/o00oo0OO;->o00ooO0(ILjava/util/List;Z)V

    return-void

    :cond_0
    iget-object p3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    monitor-enter p3

    :try_start_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-boolean v1, v0, Lo0OOo00/o00oo0OO;->o0O0o:Z

    if-eqz v1, :cond_1

    monitor-exit p3

    return-void

    :cond_1
    invoke-virtual {v0, p2}, Lo0OOo00/o00oo0OO;->o00oOo0o(I)Lo0OOo00/o00oo0O;

    move-result-object v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget v1, v0, Lo0OOo00/o00oo0OO;->o00oo0o:I

    if-gt p2, v1, :cond_2

    monitor-exit p3

    return-void

    :cond_2
    rem-int/lit8 v1, p2, 0x2

    iget v0, v0, Lo0OOo00/o00oo0OO;->o00oo0oO:I

    const/4 v2, 0x2

    rem-int/2addr v0, v2

    if-ne v1, v0, :cond_3

    monitor-exit p3

    return-void

    :cond_3
    new-instance v0, Lo0OOo00/o00oo0O;

    iget-object v5, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    const/4 v6, 0x0

    move-object v3, v0

    move v4, p2

    move v7, p1

    move-object v8, p4

    invoke-direct/range {v3 .. v8}, Lo0OOo00/o00oo0O;-><init>(ILo0OOo00/o00oo0OO;ZZLjava/util/List;)V

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iput p2, p1, Lo0OOo00/o00oo0OO;->o00oo0o:I

    iget-object p1, p1, Lo0OOo00/o00oo0OO;->o00oo0Oo:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p4

    invoke-interface {p1, p4, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    sget-object p1, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance p4, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;

    const-string v1, "OkHttp %s stream %d"

    new-array v2, v2, [Ljava/lang/Object;

    iget-object v3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v3, v3, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    const/4 v4, 0x0

    aput-object v3, v2, v4

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    const/4 v3, 0x1

    aput-object p2, v2, v3

    invoke-direct {p4, p0, v1, v2, v0}, Lo0OOo00/o00oo0OO$o00oo0O$o00oOOo0;-><init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;Lo0OOo00/o00oo0O;)V

    invoke-interface {p1, p4}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    monitor-exit p3

    return-void

    :cond_4
    monitor-exit p3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-virtual {v0, p4}, Lo0OOo00/o00oo0O;->o00oo0Oo(Ljava/util/List;)V

    if-eqz p1, :cond_5

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oo0O()V

    :cond_5
    return-void

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit p3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public o00oOo0O(ILjava/lang/String;Lokio/o00oo00O;Ljava/lang/String;IJ)V
    .locals 0

    return-void
.end method

.method public o00oOo0o(IILjava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Lo0OOo00/o00oOo0O;",
            ">;)V"
        }
    .end annotation

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {p1, p2, p3}, Lo0OOo00/o00oo0OO;->o00ooOoo(ILjava/util/List;)V

    return-void
.end method

.method public o00oOoO(ZILokio/o00oOoO;I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p2}, Lo0OOo00/o00oo0OO;->o0OoOoOo(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p2, p3, p4, p1}, Lo0OOo00/o00oo0OO;->o00ooO00(ILokio/o00oOoO;IZ)V

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p2}, Lo0OOo00/o00oo0OO;->o00oOo0o(I)Lo0OOo00/o00oo0O;

    move-result-object v0

    if-nez v0, :cond_1

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    sget-object v0, Lo0OOo00/o00oOo00;->PROTOCOL_ERROR:Lo0OOo00/o00oOo00;

    invoke-virtual {p1, p2, v0}, Lo0OOo00/o00oo0OO;->o0O0o00o(ILo0OOo00/o00oOo00;)V

    int-to-long p1, p4

    invoke-interface {p3, p1, p2}, Lokio/o00oOoO;->skip(J)V

    return-void

    :cond_1
    invoke-virtual {v0, p3, p4}, Lo0OOo00/o00oo0O;->o00oo0O0(Lokio/o00oOoO;I)V

    if-eqz p1, :cond_2

    invoke-virtual {v0}, Lo0OOo00/o00oo0O;->o00oo0O()V

    :cond_2
    return-void
.end method

.method public o00oOoO0()V
    .locals 0

    return-void
.end method

.method public o00oOoOO(ZII)V
    .locals 2

    if-eqz p1, :cond_0

    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {p1, p2}, Lo0OOo00/o00oo0OO;->o0O000o0(I)Lo0OOo00/o00ooO0;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1}, Lo0OOo00/o00ooO0;->o00oOOoO()V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    const/4 v0, 0x1

    const/4 v1, 0x0

    invoke-virtual {p1, v0, p2, p3, v1}, Lo0OOo00/o00oo0OO;->o0O0OO0O(ZIILo0OOo00/o00ooO0;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public o00oOoOo(IIIZ)V
    .locals 0

    return-void
.end method

.method public o00oOoo0(ILo0OOo00/o00oOo00;)V
    .locals 1

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p1}, Lo0OOo00/o00oo0OO;->o0OoOoOo(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p1, p2}, Lo0OOo00/o00oo0OO;->o00oooOo(ILo0OOo00/o00oOo00;)V

    return-void

    :cond_0
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v0, p1}, Lo0OOo00/o00oo0OO;->o0O000o(I)Lo0OOo00/o00oo0O;

    move-result-object p1

    if-eqz p1, :cond_1

    invoke-virtual {p1, p2}, Lo0OOo00/o00oo0O;->o00oo0o0(Lo0OOo00/o00oOo00;)V

    :cond_1
    return-void
.end method

.method public o00oOooO(IJ)V
    .locals 3

    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    if-nez p1, :cond_0

    monitor-enter v0

    :try_start_0
    iget-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-wide v1, p1, Lo0OOo00/o00oo0OO;->o00ooO:J

    add-long/2addr v1, p2

    iput-wide v1, p1, Lo0OOo00/o00oo0OO;->o00ooO:J

    invoke-virtual {p1}, Ljava/lang/Object;->notifyAll()V

    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    :cond_0
    invoke-virtual {v0, p1}, Lo0OOo00/o00oo0OO;->o00oOo0o(I)Lo0OOo00/o00oo0O;

    move-result-object p1

    if-eqz p1, :cond_1

    monitor-enter p1

    :try_start_1
    invoke-virtual {p1, p2, p3}, Lo0OOo00/o00oo0O;->o00oOOo0(J)V

    monitor-exit p1

    goto :goto_0

    :catchall_1
    move-exception p2

    monitor-exit p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    throw p2

    :cond_1
    :goto_0
    return-void
.end method

.method public o00oOooo()V
    .locals 4

    sget-object v0, Lo0OOo00/o00oOo00;->INTERNAL_ERROR:Lo0OOo00/o00oOo00;

    :try_start_0
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0O:Lo0OOo00/o00oo0O0;

    invoke-virtual {v1, p0}, Lo0OOo00/o00oo0O0;->o00oOo0O(Lo0OOo00/o00oo0O0$o00oOOoO;)V

    :goto_0
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0O:Lo0OOo00/o00oo0O0;

    const/4 v2, 0x0

    invoke-virtual {v1, v2, p0}, Lo0OOo00/o00oo0O0;->o00oOOoO(ZLo0OOo00/o00oo0O0$o00oOOoO;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    sget-object v1, Lo0OOo00/o00oOo00;->NO_ERROR:Lo0OOo00/o00oOo00;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    sget-object v0, Lo0OOo00/o00oOo00;->CANCEL:Lo0OOo00/o00oOo00;
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    :try_start_2
    iget-object v2, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v2, v1, v0}, Lo0OOo00/o00oo0OO;->o00oOOoO(Lo0OOo00/o00oOo00;Lo0OOo00/o00oOo00;)V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2

    goto :goto_1

    :catchall_0
    move-exception v2

    move-object v1, v0

    goto :goto_2

    :catch_0
    move-object v1, v0

    :catch_1
    :try_start_3
    sget-object v0, Lo0OOo00/o00oOo00;->PROTOCOL_ERROR:Lo0OOo00/o00oOo00;
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :try_start_4
    iget-object v1, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v1, v0, v0}, Lo0OOo00/o00oo0OO;->o00oOOoO(Lo0OOo00/o00oOo00;Lo0OOo00/o00oOo00;)V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_2

    :catch_2
    :goto_1
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0O:Lo0OOo00/o00oo0O0;

    invoke-static {v0}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    return-void

    :catchall_1
    move-exception v2

    :goto_2
    :try_start_5
    iget-object v3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    invoke-virtual {v3, v1, v0}, Lo0OOo00/o00oo0OO;->o00oOOoO(Lo0OOo00/o00oOo00;Lo0OOo00/o00oOo00;)V
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_3

    :catch_3
    iget-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0O:Lo0OOo00/o00oo0O0;

    invoke-static {v0}, Lo0OOOoOo/o0O0000O;->o00oOo00(Ljava/io/Closeable;)V

    throw v2
.end method

.method public final o00oo00O(Lo0OOo00/o0;)V
    .locals 5

    sget-object v0, Lo0OOo00/o00oo0OO;->o00ooo00:Ljava/util/concurrent/ExecutorService;

    new-instance v1, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    iget-object v3, p0, Lo0OOo00/o00oo0OO$o00oo0O;->o00oo0Oo:Lo0OOo00/o00oo0OO;

    iget-object v3, v3, Lo0OOo00/o00oo0OO;->o00oo0o0:Ljava/lang/String;

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const-string v3, "OkHttp %s ACK Settings"

    invoke-direct {v1, p0, v3, v2, p1}, Lo0OOo00/o00oo0OO$o00oo0O$o00oOo00;-><init>(Lo0OOo00/o00oo0OO$o00oo0O;Ljava/lang/String;[Ljava/lang/Object;Lo0OOo00/o0;)V

    invoke-interface {v0, v1}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    return-void
.end method
