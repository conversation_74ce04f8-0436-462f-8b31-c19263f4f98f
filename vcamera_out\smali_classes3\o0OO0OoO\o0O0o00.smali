.class public final Lo0OO0oOo/o0O0o00;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OO0oOo/o0O0o00$o00oOOo0;,
        Lo0OO0oOo/o0O0o00$o00oOOoO;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u000b\u0008\u0087\u0008\u0018\u0000 \u00182\u00020\u0001:\u0001\u0005B\u001b\u0012\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0010\t\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0008\u0010\u0003\u001a\u00020\u0002H\u0016J\u000b\u0010\u0005\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J!\u0010\n\u001a\u00020\u00002\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u00042\n\u0008\u0002\u0010\t\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\t\u0010\u000c\u001a\u00020\u000bH\u00d6\u0001J\u0013\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\r\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003R\u0019\u0010\u0008\u001a\u0004\u0018\u00010\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012R\u0019\u0010\t\u001a\u0004\u0018\u00010\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0019"
    }
    d2 = {
        "Lo0OO0oOo/o0O0o00;",
        "",
        "",
        "toString",
        "Lo0OO0oOo/o0O0o00O;",
        "o00oOOo0",
        "Lo0OO0oOo/oo0OOoo;",
        "o00oOOoO",
        "variance",
        "type",
        "o00oOooO",
        "",
        "hashCode",
        "other",
        "",
        "equals",
        "Lo0OO0oOo/o0O0o00O;",
        "o00oOoO",
        "()Lo0OO0oOo/o0O0o00O;",
        "Lo0OO0oOo/oo0OOoo;",
        "o00oOoO0",
        "()Lo0OO0oOo/oo0OOoo;",
        "<init>",
        "(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V",
        "o00oOo00",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation

.annotation build Lo0O0oooo/oO000Oo;
    version = "1.1"
.end annotation


# static fields
.field public static final o00oOo00:Lo0OO0oOo/o0O0o00$o00oOOo0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final o00oOooO:Lo0OO0oOo/o0O0o00;
    .annotation build Lo0OOooO0/o0;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final o00oOOo0:Lo0OO0oOo/o0O0o00O;
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation
.end field

.field public final o00oOOoO:Lo0OO0oOo/oo0OOoo;
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    new-instance v0, Lo0OO0oOo/o0O0o00$o00oOOo0;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lo0OO0oOo/o0O0o00$o00oOOo0;-><init>(Lkotlin/jvm/internal/o0O00;)V

    sput-object v0, Lo0OO0oOo/o0O0o00;->o00oOo00:Lo0OO0oOo/o0O0o00$o00oOOo0;

    new-instance v0, Lo0OO0oOo/o0O0o00;

    invoke-direct {v0, v1, v1}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    sput-object v0, Lo0OO0oOo/o0O0o00;->o00oOooO:Lo0OO0oOo/o0O0o00;

    return-void
.end method

.method public constructor <init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V
    .locals 3
    .param p1    # Lo0OO0oOo/o0O0o00O;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .param p2    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    iput-object p2, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-nez p1, :cond_0

    move v2, v0

    goto :goto_0

    :cond_0
    move v2, v1

    :goto_0
    if-nez p2, :cond_1

    move p2, v0

    goto :goto_1

    :cond_1
    move p2, v1

    :goto_1
    if-ne v2, p2, :cond_2

    goto :goto_2

    :cond_2
    move v0, v1

    :goto_2
    if-nez v0, :cond_4

    if-nez p1, :cond_3

    const-string p1, "Star projection must have no type specified."

    goto :goto_3

    :cond_3
    new-instance p2, Ljava/lang/StringBuilder;

    const-string v0, "The projection variance "

    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p1, " requires type to be specified."

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :goto_3
    new-instance p2, Ljava/lang/IllegalArgumentException;

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2

    :cond_4
    return-void
.end method

.method public static final o00oOo00(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 1
    .param p0    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lo0OO0oOo/o0O0o00;->o00oOo00:Lo0OO0oOo/o0O0o00$o00oOOo0;

    invoke-virtual {v0, p0}, Lo0OO0oOo/o0O0o00$o00oOOo0;->o00oOOo0(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;

    move-result-object p0

    return-object p0
.end method

.method public static o00oOo0O(Lo0OO0oOo/o0O0o00;Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;ILjava/lang/Object;)Lo0OO0oOo/o0O0o00;
    .locals 0

    and-int/lit8 p4, p3, 0x1

    if-eqz p4, :cond_0

    iget-object p1, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    :cond_0
    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_1

    iget-object p2, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    :cond_1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    new-instance p0, Lo0OO0oOo/o0O0o00;

    invoke-direct {p0, p1, p2}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    return-object p0
.end method

.method public static final o00oOo0o(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 1
    .param p0    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lo0OO0oOo/o0O0o00;->o00oOo00:Lo0OO0oOo/o0O0o00$o00oOOo0;

    invoke-virtual {v0, p0}, Lo0OO0oOo/o0O0o00$o00oOOo0;->o00oOOoO(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;

    move-result-object p0

    return-object p0
.end method

.method public static final o00oOoOO(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 1
    .param p0    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lo0OOooO0/o0O00;
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lo0OO0oOo/o0O0o00;->o00oOo00:Lo0OO0oOo/o0O0o00$o00oOOo0;

    invoke-virtual {v0, p0}, Lo0OO0oOo/o0O0o00$o00oOOo0;->o00oOo0O(Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lo0OO0oOo/o0O0o00;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lo0OO0oOo/o0O0o00;

    iget-object v1, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    iget-object v3, p1, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    iget-object p1, p1, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/o0ooO;->o00oOoO0(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    :goto_0
    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    if-nez v2, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v2}, Ljava/lang/Object;->hashCode()I

    move-result v1

    :goto_1
    add-int/2addr v0, v1

    return v0
.end method

.method public final o00oOOo0()Lo0OO0oOo/o0O0o00O;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    return-object v0
.end method

.method public final o00oOOoO()Lo0OO0oOo/oo0OOoo;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    return-object v0
.end method

.method public final o00oOoO()Lo0OO0oOo/o0O0o00O;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    return-object v0
.end method

.method public final o00oOoO0()Lo0OO0oOo/oo0OOoo;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/Nullable;
    .end annotation

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    return-object v0
.end method

.method public final o00oOooO(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)Lo0OO0oOo/o0O0o00;
    .locals 1
    .param p1    # Lo0OO0oOo/o0O0o00O;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .param p2    # Lo0OO0oOo/oo0OOoo;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    new-instance v0, Lo0OO0oOo/o0O0o00;

    invoke-direct {v0, p1, p2}, Lo0OO0oOo/o0O0o00;-><init>(Lo0OO0oOo/o0O0o00O;Lo0OO0oOo/oo0OOoo;)V

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOo0:Lo0OO0oOo/o0O0o00O;

    const/4 v1, -0x1

    if-nez v0, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    sget-object v2, Lo0OO0oOo/o0O0o00$o00oOOoO;->o00oOOo0:[I

    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    move-result v0

    aget v0, v2, v0

    :goto_0
    if-eq v0, v1, :cond_4

    const/4 v1, 0x1

    if-eq v0, v1, :cond_3

    const/4 v1, 0x2

    if-eq v0, v1, :cond_2

    const/4 v1, 0x3

    if-ne v0, v1, :cond_1

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "out "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    new-instance v0, Lo0O0oooo/o0OOo000;

    invoke-direct {v0}, Lo0O0oooo/o0OOo000;-><init>()V

    throw v0

    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "in "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    :goto_1
    iget-object v1, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_2

    :cond_3
    iget-object v0, p0, Lo0OO0oOo/o0O0o00;->o00oOOoO:Lo0OO0oOo/oo0OOoo;

    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    goto :goto_2

    :cond_4
    const-string v0, "*"

    :goto_2
    return-object v0
.end method
