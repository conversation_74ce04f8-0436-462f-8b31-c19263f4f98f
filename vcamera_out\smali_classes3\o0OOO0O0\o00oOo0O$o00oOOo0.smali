.class public final Lo0OOO0o0/o00oOo0O$o00oOOo0;
.super Lkotlin/jvm/internal/o0O0OO;
.source "SourceFile"

# interfaces
.implements Lo0OO0Ooo/o00oOOoO;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0o0/o00oOo0O;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/o0O0OO;",
        "Lo0OO0Ooo/o00oOOoO<",
        "Landroid/content/SharedPreferences;",
        ">;"
    }
.end annotation

.annotation runtime Lo0O0oooo/o0OOOO;
    d1 = {
        "\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0000\u001a\n \u0002*\u0004\u0018\u00010\u00010\u0001H\n\u00a2\u0006\u0002\u0008\u0003"
    }
    d2 = {
        "<anonymous>",
        "Landroid/content/SharedPreferences;",
        "kotlin.jvm.PlatformType",
        "invoke"
    }
    k = 0x3
    mv = {
        0x1,
        0x7,
        0x1
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lo0OOO0o0/o00oOo0O$o00oOOo0;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOO0o0/o00oOo0O$o00oOOo0;

    invoke-direct {v0}, Lo0OOO0o0/o00oOo0O$o00oOOo0;-><init>()V

    sput-object v0, Lo0OOO0o0/o00oOo0O$o00oOOo0;->INSTANCE:Lo0OOO0o0/o00oOo0O$o00oOOo0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lkotlin/jvm/internal/o0O0OO;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final invoke()Landroid/content/SharedPreferences;
    .locals 3

    sget-object v0, Lmultispace/multiapp/clone/app/App;->o00oo0Oo:Lmultispace/multiapp/clone/app/App$o00oOOo0;

    invoke-virtual {v0}, Lmultispace/multiapp/clone/app/App$o00oOOo0;->o00oOOo0()Landroid/content/Context;

    move-result-object v0

    const/16 v1, 0xa

    new-array v1, v1, [B

    fill-array-data v1, :array_0

    const/16 v2, 0x8

    new-array v2, v2, [B

    fill-array-data v2, :array_1

    invoke-static {v1, v2}, Lo0OOO0OO/o00oOOoO;->o00oOOo0([B[B)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    return-object v0

    :array_0
    .array-data 1
        -0x1ft
        -0x3at
        0x31t
        -0x2bt
        -0x21t
        -0xct
        -0x1ct
        -0x44t
        -0x3at
        -0x22t
    .end array-data

    nop

    :array_1
    .array-data 1
        -0x4ct
        -0x4bt
        0x54t
        -0x59t
        -0x73t
        -0x6ft
        -0x77t
        -0x23t
    .end array-data
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Lo0OOO0o0/o00oOo0O$o00oOOo0;->invoke()Landroid/content/SharedPreferences;

    move-result-object v0

    return-object v0
.end method
