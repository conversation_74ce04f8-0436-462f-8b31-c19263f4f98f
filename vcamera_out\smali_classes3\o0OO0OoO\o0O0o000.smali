.class public interface abstract Lo0OO0oOo/o0O0o000;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lo0OO0oOo/o0O0O0O;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008g\u0018\u00002\u00020\u0001R\u0014\u0010\u0005\u001a\u00020\u00028&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0003\u0010\u0004R\u001a\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00068&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0008\u0010\tR\u0014\u0010\u000e\u001a\u00020\u000b8&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0012\u001a\u00020\u000f8&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0010\u0010\u0011\u00a8\u0006\u0013"
    }
    d2 = {
        "Lo0OO0oOo/o0O0o000;",
        "Lo0OO0oOo/o0O0O0O;",
        "",
        "getName",
        "()Ljava/lang/String;",
        "name",
        "",
        "Lo0OO0oOo/oo0OOoo;",
        "getUpperBounds",
        "()Ljava/util/List;",
        "upperBounds",
        "Lo0OO0oOo/o0O0o00O;",
        "o00oOoO",
        "()Lo0OO0oOo/o0O0o00O;",
        "variance",
        "",
        "o00oOooO",
        "()Z",
        "isReified",
        "kotlin-stdlib"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
.end annotation

.annotation build Lo0O0oooo/oO000Oo;
    version = "1.1"
.end annotation


# virtual methods
.method public abstract getName()Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract getUpperBounds()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lo0OO0oOo/oo0OOoo;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract o00oOoO()Lo0OO0oOo/o0O0o00O;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract o00oOooO()Z
.end method
