.class public final Lo0OOO0O/o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a@\u0010\u0006\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0000*\u0018\u0008\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u00012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0002H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a{\u0010\u0010\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0008\"\u0004\u0008\u0001\u0010\u0000*\u001e\u0008\u0001\u0012\u0004\u0012\u00028\u0000\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00010\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\t2\u0006\u0010\n\u001a\u00028\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00010\u00022%\u0008\u0002\u0010\u000f\u001a\u001f\u0012\u0013\u0012\u00110\u000b\u00a2\u0006\u000c\u0008\u000c\u0012\u0008\u0008\r\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0001H\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u001a\u001e\u0010\u0013\u001a\u00020\u0005*\u0008\u0012\u0004\u0012\u00020\u00050\u00022\n\u0010\u0012\u001a\u0006\u0012\u0002\u0008\u00030\u0002H\u0000\u001a#\u0010\u0016\u001a\u00020\u00052\n\u0010\u0004\u001a\u0006\u0012\u0002\u0008\u00030\u00022\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0014H\u0082\u0008\u001a\u001c\u0010\u0018\u001a\u00020\u00052\n\u0010\u0004\u001a\u0006\u0012\u0002\u0008\u00030\u00022\u0006\u0010\u0017\u001a\u00020\u000bH\u0002\u0082\u0002\u0004\n\u0002\u0008\u0019\u00a8\u0006\u0019"
    }
    d2 = {
        "T",
        "Lkotlin/Function1;",
        "Lkotlin/coroutines/o00oOo0O;",
        "",
        "completion",
        "Lo0O0oooo/oO0O00o0;",
        "o00oOooO",
        "(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)V",
        "R",
        "Lkotlin/Function2;",
        "receiver",
        "",
        "Lo0O0oooo/oO000O0;",
        "name",
        "cause",
        "onCancellation",
        "o00oOo0O",
        "(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00ooO0;)V",
        "fatalCompletion",
        "o00oOo00",
        "Lkotlin/Function0;",
        "block",
        "o00oOOoO",
        "e",
        "o00oOOo0",
        "kotlinx-coroutines-core"
    }
    k = 0x2
    mv = {
        0x1,
        0x6,
        0x0
    }
.end annotation


# direct methods
.method public static final o00oOOo0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Throwable;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/o00oOo0O<",
            "*>;",
            "Ljava/lang/Throwable;",
            ")V"
        }
    .end annotation

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    invoke-static {p1}, Lo0O0oooo/oO0OOo0o;->o00oOOo0(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p0, v0}, Lkotlin/coroutines/o00oOo0O;->resumeWith(Ljava/lang/Object;)V

    throw p1
.end method

.method public static final o00oOOoO(Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00oOOoO;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/o00oOo0O<",
            "*>;",
            "Lo0OO0Ooo/o00oOOoO<",
            "Lo0O0oooo/oO0O00o0;",
            ">;)V"
        }
    .end annotation

    :try_start_0
    invoke-interface {p1}, Lo0OO0Ooo/o00oOOoO;->invoke()Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    invoke-static {p0, p1}, Lo0OOO0O/o00oOOo0;->o00oOOo0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public static final o00oOo00(Lkotlin/coroutines/o00oOo0O;Lkotlin/coroutines/o00oOo0O;)V
    .locals 3
    .param p0    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/o00oOo0O<",
            "-",
            "Lo0O0oooo/oO0O00o0;",
            ">;",
            "Lkotlin/coroutines/o00oOo0O<",
            "*>;)V"
        }
    .end annotation

    :try_start_0
    invoke-static {p0}, Lkotlin/coroutines/intrinsics/o00oOo00;->o00oOooO(Lkotlin/coroutines/o00oOo0O;)Lkotlin/coroutines/o00oOo0O;

    move-result-object p0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    sget-object v0, Lo0O0oooo/oO0O00o0;->o00oOOo0:Lo0O0oooo/oO0O00o0;

    invoke-static {v0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    invoke-static {p0, v0, v2, v1, v2}, Lkotlinx/coroutines/internal/o00ooO0;->o00oOoO0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Object;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p0

    invoke-static {p1, p0}, Lo0OOO0O/o00oOOo0;->o00oOOo0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public static final o00oOo0O(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00ooO0;)V
    .locals 0
    .param p0    # Lo0OO0Ooo/o0O0000O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lo0OO0Ooo/o00ooO0;
        .annotation build Lorg/jetbrains/annotations/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o0O0000O<",
            "-TR;-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Ljava/lang/Throwable;",
            "Lo0O0oooo/oO0O00o0;",
            ">;)V"
        }
    .end annotation

    :try_start_0
    invoke-static {p0, p1, p2}, Lkotlin/coroutines/intrinsics/o00oOo00;->o00oOo00(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;)Lkotlin/coroutines/o00oOo0O;

    move-result-object p0

    invoke-static {p0}, Lkotlin/coroutines/intrinsics/o00oOo00;->o00oOooO(Lkotlin/coroutines/o00oOo0O;)Lkotlin/coroutines/o00oOo0O;

    move-result-object p0

    sget-object p1, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    sget-object p1, Lo0O0oooo/oO0O00o0;->o00oOOo0:Lo0O0oooo/oO0O00o0;

    invoke-static {p1}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p0, p1, p3}, Lkotlinx/coroutines/internal/o00ooO0;->o00oOo0o(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Object;Lo0OO0Ooo/o00ooO0;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p0

    invoke-static {p2, p0}, Lo0OOO0O/o00oOOo0;->o00oOOo0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public static synthetic o00oOo0o(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    const/4 p3, 0x0

    :cond_0
    invoke-static {p0, p1, p2, p3}, Lo0OOO0O/o00oOOo0;->o00oOo0O(Lo0OO0Ooo/o0O0000O;Ljava/lang/Object;Lkotlin/coroutines/o00oOo0O;Lo0OO0Ooo/o00ooO0;)V

    return-void
.end method

.method public static final o00oOooO(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)V
    .locals 3
    .param p0    # Lo0OO0Ooo/o00ooO0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/coroutines/o00oOo0O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lo0OO0Ooo/o00ooO0<",
            "-",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/o00oOo0O<",
            "-TT;>;)V"
        }
    .end annotation

    .annotation build Lkotlinx/coroutines/oO000;
    .end annotation

    :try_start_0
    invoke-static {p0, p1}, Lkotlin/coroutines/intrinsics/o00oOo00;->o00oOOoO(Lo0OO0Ooo/o00ooO0;Lkotlin/coroutines/o00oOo0O;)Lkotlin/coroutines/o00oOo0O;

    move-result-object p0

    invoke-static {p0}, Lkotlin/coroutines/intrinsics/o00oOo00;->o00oOooO(Lkotlin/coroutines/o00oOo0O;)Lkotlin/coroutines/o00oOo0O;

    move-result-object p0

    sget-object v0, Lo0O0oooo/ooOOOOoo;->Companion:Lo0O0oooo/ooOOOOoo$o00oOOo0;

    sget-object v0, Lo0O0oooo/oO0O00o0;->o00oOOo0:Lo0O0oooo/oO0O00o0;

    invoke-static {v0}, Lo0O0oooo/ooOOOOoo;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x2

    const/4 v2, 0x0

    invoke-static {p0, v0, v2, v1, v2}, Lkotlinx/coroutines/internal/o00ooO0;->o00oOoO0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Object;Lo0OO0Ooo/o00ooO0;ILjava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p0

    invoke-static {p1, p0}, Lo0OOO0O/o00oOOo0;->o00oOOo0(Lkotlin/coroutines/o00oOo0O;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method
