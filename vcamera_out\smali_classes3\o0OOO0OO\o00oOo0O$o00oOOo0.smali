.class public Lo0OOO0Oo/o00oOo0O$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/ads/initialization/OnInitializationCompleteListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lo0OOO0Oo/o00oOo0O;->o00oOOo0(Landroid/content/Context;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oOOo0:Lo0OOO0Oo/o00oOo0O;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oOo0O;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oOo0O$o00oOOo0;->o00oOOo0:Lo0OOO0Oo/o00oOo0O;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onInitializationComplete(Lcom/google/android/gms/ads/initialization/InitializationStatus;)V
    .locals 0

    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOOo0()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_0

    invoke-static {}, Lo0OOO0Oo/o00oOoO;->o00oOo0o()Lo0OOO0Oo/o00oOoO;

    move-result-object p1

    invoke-virtual {p1}, Lo0OOO0Oo/o00oOoO;->o00oOoOo()V

    :cond_0
    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOOoO()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_1

    invoke-static {}, Lo0OOO0Oo/o00oo00O;->o00oOo0o()Lo0OOO0Oo/o00oo00O;

    move-result-object p1

    invoke-virtual {p1}, Lo0OOO0Oo/o00oo00O;->o00oOoOo()V

    :cond_1
    invoke-static {}, Lo0OOO0Oo/o00oOo00;->o00oOo00()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_2

    invoke-static {}, Lo0OOO0Oo/o00oo0;->o00oOo0o()Lo0OOO0Oo/o00oo0;

    move-result-object p1

    invoke-virtual {p1}, Lo0OOO0Oo/o00oo0;->o00oOoo0()V

    :cond_2
    return-void
.end method
