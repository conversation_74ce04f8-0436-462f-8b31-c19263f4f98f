.class public final Lo0OOO0oO/o00oo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0003\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lo0OOO0oO/o00oo0O;",
        "",
        "<init>",
        "()V",
        "app_vcamRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
.end annotation


# static fields
.field public static final o00oOOo0:Lo0OOO0oO/o00oo0O;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lo0OOO0oO/o00oo0O;

    invoke-direct {v0}, Lo0OOO0oO/o00oo0O;-><init>()V

    sput-object v0, Lo0OOO0oO/o00oo0O;->o00oOOo0:Lo0OOO0oO/o00oo0O;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
