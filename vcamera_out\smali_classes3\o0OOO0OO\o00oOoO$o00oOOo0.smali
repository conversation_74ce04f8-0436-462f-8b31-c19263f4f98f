.class public Lo0OOO0Oo/o00oOoO$o00oOOo0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0Oo/o00oOoO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic o00oo0O0:Lo0OOO0Oo/o00oOoO;


# direct methods
.method public constructor <init>(Lo0OOO0Oo/o00oOoO;)V
    .locals 0

    iput-object p1, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    const-wide/32 v0, 0x6ddd00

    :try_start_0
    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    invoke-static {v2}, Lo0OOO0Oo/o00oOoO;->o00oOOo0(Lo0OOO0Oo/o00oOoO;)J

    move-result-wide v2

    add-long/2addr v2, v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    cmp-long v2, v2, v4

    if-gtz v2, :cond_0

    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    const/4 v3, 0x0

    iput-object v3, v2, Lo0OOO0Oo/o00oOoO;->o00oOOo0:Lcom/google/android/gms/ads/interstitial/InterstitialAd;

    invoke-static {}, Lmultispace/multiapp/clone/app/App;->o00oOOoO()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v2, v3}, Lo0OOO0Oo/o00oOoO;->o00oOoO(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    iget-object v2, v2, Lo0OOO0Oo/o00oOoO;->o00oOooO:Ljava/lang/Runnable;

    invoke-static {v2}, Lmultispace/multiapp/clone/util/o0O00oO0;->o00oOOoO(Ljava/lang/Runnable;)V

    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    goto :goto_0

    :catchall_0
    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    invoke-static {v2}, Lo0OOO0Oo/o00oOoO;->o00oOo0O(Lo0OOO0Oo/o00oOoO;)Ljava/lang/Runnable;

    move-result-object v2

    invoke-static {v2}, Lmultispace/multiapp/clone/util/o0O00oO0;->o00oOOoO(Ljava/lang/Runnable;)V

    iget-object v2, p0, Lo0OOO0Oo/o00oOoO$o00oOOo0;->o00oo0O0:Lo0OOO0Oo/o00oOoO;

    :goto_0
    iget-object v2, v2, Lo0OOO0Oo/o00oOoO;->o00oOooO:Ljava/lang/Runnable;

    invoke-static {v2, v0, v1}, Lmultispace/multiapp/clone/util/o0O00oO0;->o00oOo0o(Ljava/lang/Runnable;J)V

    return-void
.end method
