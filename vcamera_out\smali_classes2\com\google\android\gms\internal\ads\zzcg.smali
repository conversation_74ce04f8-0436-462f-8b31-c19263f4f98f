.class public interface abstract Lcom/google/android/gms/internal/ads/zzcg;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract zzA()Z
.end method

.method public abstract zzb()Z
.end method

.method public abstract zzc()Z
.end method

.method public abstract zzd()Z
.end method

.method public abstract zze()I
.end method

.method public abstract zzf()I
.end method

.method public abstract zzg()I
.end method

.method public abstract zzh()I
.end method

.method public abstract zzi()I
.end method

.method public abstract zzj()I
.end method

.method public abstract zzk()I
.end method

.method public abstract zzl()J
.end method

.method public abstract zzm()J
.end method

.method public abstract zzn()J
.end method

.method public abstract zzo()J
.end method

.method public abstract zzp()J
.end method

.method public abstract zzq()Lcom/google/android/gms/internal/ads/zzcn;
.end method

.method public abstract zzr()Lcom/google/android/gms/internal/ads/zzcy;
.end method

.method public abstract zzs()V
.end method

.method public abstract zzt()V
.end method

.method public abstract zzu(Z)V
.end method

.method public abstract zzv(Landroid/view/Surface;)V
    .param p1    # Landroid/view/Surface;
        .annotation build Lo00oOo00/Class7651;
        .end annotation
    .end param
.end method

.method public abstract zzw(F)V
    .param p1    # F
        .annotation build Lo00oOo00/Class7794;
            from = 0.0
            to = 1.0
        .end annotation
    .end param
.end method

.method public abstract zzx()V
.end method

.method public abstract zzy()Z
.end method

.method public abstract zzz()Z
.end method
