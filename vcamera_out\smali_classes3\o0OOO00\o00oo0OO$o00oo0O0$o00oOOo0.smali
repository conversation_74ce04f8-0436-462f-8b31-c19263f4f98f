.class public final Lo0OOo00/o00oo0OO$o00oo0O0$o00oOOo0;
.super Lo0OOo00/o00oo0OO$o00oo0O0;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0OO$o00oo0O0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lo0OOo00/o00oo0OO$o00oo0O0;-><init>()V

    return-void
.end method


# virtual methods
.method public o00oOo0o(Lo0OOo00/o00oo0O;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    sget-object v0, Lo0OOo00/o00oOo00;->REFUSED_STREAM:Lo0OOo00/o00oOo00;

    invoke-virtual {p1, v0}, Lo0OOo00/o00oo0O;->o00oOooO(Lo0OOo00/o00oOo00;)V

    return-void
.end method
