.class public interface abstract Lo0OOO0Oo/o00oo0$o00oOo0O;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOO0Oo/o00oo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "o00oOo0O"
.end annotation


# virtual methods
.method public abstract o00oOOo0()V
.end method

.method public abstract o00oOOoO()V
.end method
