.class public final Lo0OOo0O/o00oOoO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lo0OOo0O/o00oOoO$o00oOOo0;
    }
.end annotation


# instance fields
.field public final o00oOOo0:Z

.field public final o00oOOoO:Lokio/o00oOoO;

.field public final o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

.field public o00oOo0O:I

.field public o00oOo0o:J

.field public o00oOoO:Z

.field public o00oOoO0:J

.field public o00oOoOO:Z

.field public o00oOoOo:Z

.field public final o00oOoo0:[B

.field public o00oOooO:Z

.field public final o00oOooo:[B


# direct methods
.method public constructor <init>(ZLokio/o00oOoO;Lo0OOo0O/o00oOoO$o00oOOo0;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x4

    new-array v0, v0, [B

    iput-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOoo0:[B

    const/16 v0, 0x2000

    new-array v0, v0, [B

    iput-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    if-eqz p2, :cond_1

    if-eqz p3, :cond_0

    iput-boolean p1, p0, Lo0OOo0O/o00oOoO;->o00oOOo0:Z

    iput-object p2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    iput-object p3, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    return-void

    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "frameCallback == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "source == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public o00oOOo0()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOo00()V

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoOO:Z

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOOoO()V

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOo0O()V

    :goto_0
    return-void
.end method

.method public final o00oOOoO()V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lokio/o00oOo00;

    invoke-direct {v0}, Lokio/o00oOo00;-><init>()V

    iget-wide v1, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    iget-wide v3, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    cmp-long v1, v1, v3

    if-gez v1, :cond_2

    iget-boolean v1, p0, Lo0OOo0O/o00oOoO;->o00oOOo0:Z

    if-eqz v1, :cond_0

    iget-object v1, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v1, v0, v3, v4}, Lokio/o00oOoO;->o00ooOO0(Lokio/o00oOo00;J)V

    goto :goto_1

    :cond_0
    :goto_0
    iget-wide v1, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    iget-wide v3, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    cmp-long v5, v1, v3

    if-gez v5, :cond_2

    sub-long/2addr v3, v1

    iget-object v1, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    array-length v1, v1

    int-to-long v1, v1

    invoke-static {v3, v4, v1, v2}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v1

    long-to-int v1, v1

    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    iget-object v3, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    const/4 v4, 0x0

    invoke-interface {v2, v3, v4, v1}, Lokio/o00oOoO;->read([BII)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_1

    iget-object v5, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    int-to-long v2, v1

    iget-object v8, p0, Lo0OOo0O/o00oOoO;->o00oOoo0:[B

    iget-wide v9, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    move-wide v6, v2

    invoke-static/range {v5 .. v10}, Lo0OOo0O/o00oOo0O;->o00oOo00([BJ[BJ)V

    iget-object v5, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    invoke-virtual {v0, v5, v4, v1}, Lokio/o00oOo00;->o0O0oOo([BII)Lokio/o00oOo00;

    iget-wide v4, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    add-long/2addr v4, v2

    iput-wide v4, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/io/EOFException;

    invoke-direct {v0}, Ljava/io/EOFException;-><init>()V

    throw v0

    :cond_2
    :goto_1
    iget v1, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    packed-switch v1, :pswitch_data_0

    new-instance v0, Ljava/net/ProtocolException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Unknown control opcode: "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v2, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    invoke-static {v2}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :pswitch_0
    iget-object v1, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    invoke-virtual {v0}, Lokio/o00oOo00;->o0O0OooO()Lokio/o00oo00O;

    move-result-object v0

    invoke-interface {v1, v0}, Lo0OOo0O/o00oOoO$o00oOOo0;->o00oOoO(Lokio/o00oo00O;)V

    goto :goto_3

    :pswitch_1
    iget-object v1, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    invoke-virtual {v0}, Lokio/o00oOo00;->o0O0OooO()Lokio/o00oo00O;

    move-result-object v0

    invoke-interface {v1, v0}, Lo0OOo0O/o00oOoO$o00oOOo0;->o00oOo0o(Lokio/o00oo00O;)V

    goto :goto_3

    :pswitch_2
    iget-wide v1, v0, Lokio/o00oOo00;->o00oo0O:J

    const-wide/16 v3, 0x1

    cmp-long v3, v1, v3

    if-eqz v3, :cond_5

    const-wide/16 v3, 0x0

    cmp-long v1, v1, v3

    if-eqz v1, :cond_4

    invoke-virtual {v0}, Lokio/o00oOo00;->readShort()S

    move-result v1

    invoke-virtual {v0}, Lokio/o00oOo00;->o0O0o0o0()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1}, Lo0OOo0O/o00oOo0O;->o00oOOoO(I)Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_3

    goto :goto_2

    :cond_3
    new-instance v0, Ljava/net/ProtocolException;

    invoke-direct {v0, v2}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_4
    const/16 v1, 0x3ed

    const-string v0, ""

    :goto_2
    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    invoke-interface {v2, v1, v0}, Lo0OOo0O/o00oOoO$o00oOOo0;->o00oOoOo(ILjava/lang/String;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOooO:Z

    :goto_3
    return-void

    :cond_5
    new-instance v0, Ljava/net/ProtocolException;

    const-string v1, "Malformed close payload length of 1."

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :pswitch_data_0
    .packed-switch 0x8
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final o00oOo00()V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOooO:Z

    if-nez v0, :cond_11

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o0OoO00O;->o00oOooO()Lokio/o0O00O0o;

    move-result-object v0

    invoke-virtual {v0}, Lokio/o0O00O0o;->o00oOoOO()J

    move-result-wide v0

    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v2}, Lokio/o0OoO00O;->o00oOooO()Lokio/o0O00O0o;

    move-result-object v2

    invoke-virtual {v2}, Lokio/o0O00O0o;->o00oOOoO()Lokio/o0O00O0o;

    :try_start_0
    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v2}, Lokio/o00oOoO;->readByte()B

    move-result v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    and-int/lit16 v2, v2, 0xff

    iget-object v3, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v3}, Lokio/o0OoO00O;->o00oOooO()Lokio/o0O00O0o;

    move-result-object v3

    sget-object v4, Ljava/util/concurrent/TimeUnit;->NANOSECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v3, v0, v1, v4}, Lokio/o0O00O0o;->o00oOoO(JLjava/util/concurrent/TimeUnit;)Lokio/o0O00O0o;

    and-int/lit8 v0, v2, 0xf

    iput v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    and-int/lit16 v0, v2, 0x80

    const/4 v1, 0x1

    const/4 v3, 0x0

    if-eqz v0, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    move v0, v3

    :goto_0
    iput-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO:Z

    and-int/lit8 v4, v2, 0x8

    if-eqz v4, :cond_1

    move v4, v1

    goto :goto_1

    :cond_1
    move v4, v3

    :goto_1
    iput-boolean v4, p0, Lo0OOo0O/o00oOoO;->o00oOoOO:Z

    if-eqz v4, :cond_3

    if-eqz v0, :cond_2

    goto :goto_2

    :cond_2
    new-instance v0, Ljava/net/ProtocolException;

    const-string v1, "Control frames must be final."

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_3
    :goto_2
    and-int/lit8 v0, v2, 0x40

    if-eqz v0, :cond_4

    move v0, v1

    goto :goto_3

    :cond_4
    move v0, v3

    :goto_3
    and-int/lit8 v4, v2, 0x20

    if-eqz v4, :cond_5

    move v4, v1

    goto :goto_4

    :cond_5
    move v4, v3

    :goto_4
    and-int/lit8 v2, v2, 0x10

    if-eqz v2, :cond_6

    move v2, v1

    goto :goto_5

    :cond_6
    move v2, v3

    :goto_5
    if-nez v0, :cond_10

    if-nez v4, :cond_10

    if-nez v2, :cond_10

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readByte()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    and-int/lit16 v2, v0, 0x80

    if-eqz v2, :cond_7

    goto :goto_6

    :cond_7
    move v1, v3

    :goto_6
    iput-boolean v1, p0, Lo0OOo0O/o00oOoO;->o00oOoOo:Z

    iget-boolean v2, p0, Lo0OOo0O/o00oOoO;->o00oOOo0:Z

    if-ne v1, v2, :cond_9

    new-instance v0, Ljava/net/ProtocolException;

    iget-boolean v1, p0, Lo0OOo0O/o00oOoO;->o00oOOo0:Z

    if-eqz v1, :cond_8

    const-string v1, "Server-sent frames must not be masked."

    goto :goto_7

    :cond_8
    const-string v1, "Client-sent frames must be masked."

    :goto_7
    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_9
    and-int/lit8 v0, v0, 0x7f

    int-to-long v0, v0

    iput-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    const-wide/16 v2, 0x7e

    cmp-long v2, v0, v2

    const-wide/16 v3, 0x0

    if-nez v2, :cond_a

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readShort()S

    move-result v0

    int-to-long v0, v0

    const-wide/32 v5, 0xffff

    and-long/2addr v0, v5

    iput-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    goto :goto_8

    :cond_a
    const-wide/16 v5, 0x7f

    cmp-long v0, v0, v5

    if-nez v0, :cond_c

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v0}, Lokio/o00oOoO;->readLong()J

    move-result-wide v0

    iput-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    cmp-long v0, v0, v3

    if-ltz v0, :cond_b

    goto :goto_8

    :cond_b
    new-instance v0, Ljava/net/ProtocolException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Frame length 0x"

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-wide v2, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    invoke-static {v2, v3}, Ljava/lang/Long;->toHexString(J)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " > 0x7FFFFFFFFFFFFFFF"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_c
    :goto_8
    iput-wide v3, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoOO:Z

    if-eqz v0, :cond_e

    iget-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    const-wide/16 v2, 0x7d

    cmp-long v0, v0, v2

    if-gtz v0, :cond_d

    goto :goto_9

    :cond_d
    new-instance v0, Ljava/net/ProtocolException;

    const-string v1, "Control frame must be less than 125B."

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_e
    :goto_9
    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoOo:Z

    if-eqz v0, :cond_f

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    iget-object v1, p0, Lo0OOo0O/o00oOoO;->o00oOoo0:[B

    invoke-interface {v0, v1}, Lokio/o00oOoO;->readFully([B)V

    :cond_f
    return-void

    :cond_10
    new-instance v0, Ljava/net/ProtocolException;

    const-string v1, "Reserved flags are unsupported."

    invoke-direct {v0, v1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v0

    :catchall_0
    move-exception v2

    iget-object v3, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v3}, Lokio/o0OoO00O;->o00oOooO()Lokio/o0O00O0o;

    move-result-object v3

    sget-object v4, Ljava/util/concurrent/TimeUnit;->NANOSECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v3, v0, v1, v4}, Lokio/o0O00O0o;->o00oOoO(JLjava/util/concurrent/TimeUnit;)Lokio/o0O00O0o;

    throw v2

    :cond_11
    new-instance v0, Ljava/io/IOException;

    const-string v1, "closed"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final o00oOo0O()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_1

    const/4 v2, 0x2

    if-ne v0, v2, :cond_0

    goto :goto_0

    :cond_0
    new-instance v1, Ljava/net/ProtocolException;

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "Unknown opcode: "

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-static {v0}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_1
    :goto_0
    new-instance v2, Lokio/o00oOo00;

    invoke-direct {v2}, Lokio/o00oOo00;-><init>()V

    invoke-virtual {p0, v2}, Lo0OOo0O/o00oOoO;->o00oOooO(Lokio/o00oOo00;)V

    if-ne v0, v1, :cond_2

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    invoke-virtual {v2}, Lokio/o00oOo00;->o0O0o0o0()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Lo0OOo0O/o00oOoO$o00oOOo0;->o00oOo0O(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOo00:Lo0OOo0O/o00oOoO$o00oOOo0;

    invoke-virtual {v2}, Lokio/o00oOo00;->o0O0OooO()Lokio/o00oo00O;

    move-result-object v1

    invoke-interface {v0, v1}, Lo0OOo0O/o00oOoO$o00oOOo0;->o00oOooO(Lokio/o00oo00O;)V

    :goto_1
    return-void
.end method

.method public o00oOo0o()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOooO:Z

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOo00()V

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoOO:Z

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOOoO()V

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method

.method public final o00oOooO(Lokio/o00oOo00;)V
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOooO:Z

    if-nez v0, :cond_6

    iget-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    iget-wide v2, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    cmp-long v0, v0, v2

    if-nez v0, :cond_2

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Lo0OOo0O/o00oOoO;->o00oOo0o()V

    iget v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO:Z

    if-eqz v0, :cond_2

    iget-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-nez v0, :cond_2

    return-void

    :cond_1
    new-instance p1, Ljava/net/ProtocolException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Expected continuation opcode. Got: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget v1, p0, Lo0OOo0O/o00oOoO;->o00oOo0O:I

    invoke-static {v1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    iget-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOo0o:J

    iget-wide v2, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    sub-long/2addr v0, v2

    iget-boolean v2, p0, Lo0OOo0O/o00oOoO;->o00oOoOo:Z

    const-wide/16 v3, -0x1

    if-eqz v2, :cond_4

    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    array-length v2, v2

    int-to-long v5, v2

    invoke-static {v0, v1, v5, v6}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v0

    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    iget-object v5, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    long-to-int v0, v0

    const/4 v1, 0x0

    invoke-interface {v2, v5, v1, v0}, Lokio/o00oOoO;->read([BII)I

    move-result v0

    int-to-long v11, v0

    cmp-long v0, v11, v3

    if-eqz v0, :cond_3

    iget-object v5, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    iget-object v8, p0, Lo0OOo0O/o00oOoO;->o00oOoo0:[B

    iget-wide v9, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    move-wide v6, v11

    invoke-static/range {v5 .. v10}, Lo0OOo0O/o00oOo0O;->o00oOo00([BJ[BJ)V

    iget-object v0, p0, Lo0OOo0O/o00oOoO;->o00oOooo:[B

    long-to-int v2, v11

    invoke-virtual {p1, v0, v1, v2}, Lokio/o00oOo00;->o0O0oOo([BII)Lokio/o00oOo00;

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/io/EOFException;

    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    throw p1

    :cond_4
    iget-object v2, p0, Lo0OOo0O/o00oOoO;->o00oOOoO:Lokio/o00oOoO;

    invoke-interface {v2, p1, v0, v1}, Lokio/o0OoO00O;->o0O0o0oO(Lokio/o00oOo00;J)J

    move-result-wide v11

    cmp-long v0, v11, v3

    if-eqz v0, :cond_5

    :goto_1
    iget-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    add-long/2addr v0, v11

    iput-wide v0, p0, Lo0OOo0O/o00oOoO;->o00oOoO0:J

    goto/16 :goto_0

    :cond_5
    new-instance p1, Ljava/io/EOFException;

    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    throw p1

    :cond_6
    new-instance p1, Ljava/io/IOException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
