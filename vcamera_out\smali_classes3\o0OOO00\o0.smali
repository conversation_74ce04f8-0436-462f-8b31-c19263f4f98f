.class public final Lo0OOo00/o0;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final o00oOo00:I = 0xffff

.field public static final o00oOo0O:I = 0x2

.field public static final o00oOo0o:I = 0x4

.field public static final o00oOoO:I = 0x6

.field public static final o00oOoO0:I = 0x5

.field public static final o00oOoOO:I = 0x7

.field public static final o00oOoOo:I = 0xa

.field public static final o00oOooO:I = 0x1


# instance fields
.field public o00oOOo0:I

.field public final o00oOOoO:[I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xa

    new-array v0, v0, [I

    iput-object v0, p0, Lo0OOo00/o0;->o00oOOoO:[I

    return-void
.end method


# virtual methods
.method public o00oOOo0()V
    .locals 2

    const/4 v0, 0x0

    iput v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    iget-object v1, p0, Lo0OOo00/o0;->o00oOOoO:[I

    invoke-static {v1, v0}, Ljava/util/Arrays;->fill([II)V

    return-void
.end method

.method public o00oOOoO(I)I
    .locals 1

    iget-object v0, p0, Lo0OOo00/o0;->o00oOOoO:[I

    aget p1, v0, p1

    return p1
.end method

.method public o00oOo00(Z)Z
    .locals 3

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit8 v0, v0, 0x4

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    iget-object p1, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v0, 0x2

    aget p1, p1, v0

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    move p1, v2

    goto :goto_0

    :cond_1
    move p1, v1

    :goto_0
    if-ne p1, v2, :cond_2

    move v1, v2

    :cond_2
    return v1
.end method

.method public o00oOo0O()I
    .locals 2

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit16 v0, v0, 0x80

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v1, 0x7

    aget v0, v0, v1

    goto :goto_0

    :cond_0
    const v0, 0xffff

    :goto_0
    return v0
.end method

.method public o00oOo0o(I)I
    .locals 1

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit8 v0, v0, 0x10

    if-eqz v0, :cond_0

    iget-object p1, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v0, 0x4

    aget p1, p1, v0

    :cond_0
    return p1
.end method

.method public o00oOoO(I)I
    .locals 1

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit8 v0, v0, 0x40

    if-eqz v0, :cond_0

    iget-object p1, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v0, 0x6

    aget p1, p1, v0

    :cond_0
    return p1
.end method

.method public o00oOoO0(I)I
    .locals 1

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit8 v0, v0, 0x20

    if-eqz v0, :cond_0

    iget-object p1, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v0, 0x5

    aget p1, p1, v0

    :cond_0
    return p1
.end method

.method public o00oOoOO(I)Z
    .locals 2

    const/4 v0, 0x1

    shl-int p1, v0, p1

    iget v1, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/2addr p1, v1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public o00oOoOo(Lo0OOo00/o0;)V
    .locals 2

    const/4 v0, 0x0

    :goto_0
    const/16 v1, 0xa

    if-ge v0, v1, :cond_1

    invoke-virtual {p1, v0}, Lo0OOo00/o0;->o00oOoOO(I)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_1

    :cond_0
    iget-object v1, p1, Lo0OOo00/o0;->o00oOOoO:[I

    aget v1, v1, v0

    invoke-virtual {p0, v0, v1}, Lo0OOo00/o0;->o00oOoo0(II)Lo0OOo00/o0;

    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public o00oOoo0(II)Lo0OOo00/o0;
    .locals 3

    iget-object v0, p0, Lo0OOo00/o0;->o00oOOoO:[I

    array-length v1, v0

    if-lt p1, v1, :cond_0

    return-object p0

    :cond_0
    const/4 v1, 0x1

    shl-int/2addr v1, p1

    iget v2, p0, Lo0OOo00/o0;->o00oOOo0:I

    or-int/2addr v1, v2

    iput v1, p0, Lo0OOo00/o0;->o00oOOo0:I

    aput p2, v0, p1

    return-object p0
.end method

.method public o00oOooO()I
    .locals 2

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    and-int/lit8 v0, v0, 0x2

    if-eqz v0, :cond_0

    iget-object v0, p0, Lo0OOo00/o0;->o00oOOoO:[I

    const/4 v1, 0x1

    aget v0, v0, v1

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    :goto_0
    return v0
.end method

.method public o00oOooo()I
    .locals 1

    iget v0, p0, Lo0OOo00/o0;->o00oOOo0:I

    invoke-static {v0}, Ljava/lang/Integer;->bitCount(I)I

    move-result v0

    return v0
.end method
