.class public abstract Lo0OOo000/o00oOOo0$o00oOOoO;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lokio/o0OoO00O;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo000/o00oOOo0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x401
    name = "o00oOOoO"
.end annotation


# instance fields
.field public o00oo0O:Z

.field public final o00oo0O0:Lokio/o00oo0O;

.field public final synthetic o00oo0Oo:Lo0OOo000/o00oOOo0;


# direct methods
.method public constructor <init>(Lo0OOo000/o00oOOo0;)V
    .locals 1

    iput-object p1, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lokio/o00oo0O;

    iget-object p1, p1, Lo0OOo000/o00oOOo0;->o00oOooO:Lokio/o00oOoO;

    invoke-interface {p1}, Lokio/o0OoO00O;->o00oOooO()Lokio/o0O00O0o;

    move-result-object p1

    invoke-direct {v0, p1}, Lokio/o00oo0O;-><init>(Lokio/o0O00O0o;)V

    iput-object v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O0:Lokio/o00oo0O;

    return-void
.end method

.method public synthetic constructor <init>(Lo0OOo000/o00oOOo0;Lo0OOo000/o00oOOo0$o00oOOo0;)V
    .locals 0

    invoke-direct {p0, p1}, Lo0OOo000/o00oOOo0$o00oOOoO;-><init>(Lo0OOo000/o00oOOo0;)V

    return-void
.end method


# virtual methods
.method public final o00oOOo0(Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget v1, v0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    const/4 v2, 0x6

    if-ne v1, v2, :cond_0

    return-void

    :cond_0
    const/4 v3, 0x5

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O0:Lokio/o00oo0O;

    invoke-virtual {v0, v1}, Lo0OOo000/o00oOOo0;->o00oOoO0(Lokio/o00oo0O;)V

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iput v2, v0, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    iget-object v1, v0, Lo0OOo000/o00oOOo0;->o00oOo00:Lo0OOOooO/o0OoO00O;

    if-eqz v1, :cond_1

    xor-int/lit8 p1, p1, 0x1

    invoke-virtual {v1, p1, v0}, Lo0OOOooO/o0OoO00O;->o00oo0O0(ZLo0OOOooo/o0O00OO;)V

    :cond_1
    return-void

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "state: "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v1, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0Oo:Lo0OOo000/o00oOOo0;

    iget v1, v1, Lo0OOo000/o00oOOo0;->o00oOo0o:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o00oOooO()Lokio/o0O00O0o;
    .locals 1

    iget-object v0, p0, Lo0OOo000/o00oOOo0$o00oOOoO;->o00oo0O0:Lokio/o00oo0O;

    return-object v0
.end method
