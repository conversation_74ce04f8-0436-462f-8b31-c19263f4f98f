.class public Lo0OOo00/o00oo0OO$o00oo0OO;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo0OOo00/o00oo0OO;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "o00oo0OO"
.end annotation


# instance fields
.field public o00oOOo0:Ljava/net/Socket;

.field public o00oOOoO:Ljava/lang/String;

.field public o00oOo00:Lokio/o00oOoO;

.field public o00oOo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

.field public o00oOo0o:Lo0OOo00/o00ooO;

.field public o00oOoO0:Z

.field public o00oOooO:Lokio/o00oOo0O;


# direct methods
.method public constructor <init>(Z)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    sget-object v0, Lo0OOo00/o00oo0OO$o00oo0O0;->o00oOOo0:Lo0OOo00/o00oo0OO$o00oo0O0;

    iput-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    sget-object v0, Lo0OOo00/o00ooO;->o00oOOo0:Lo0OOo00/o00ooO;

    iput-object v0, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0o:Lo0OOo00/o00ooO;

    iput-boolean p1, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOoO0:Z

    return-void
.end method


# virtual methods
.method public o00oOOo0()Lo0OOo00/o00oo0OO;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    new-instance v0, Lo0OOo00/o00oo0OO;

    invoke-direct {v0, p0}, Lo0OOo00/o00oo0OO;-><init>(Lo0OOo00/o00oo0OO$o00oo0OO;)V

    return-object v0
.end method

.method public o00oOOoO(Lo0OOo00/o00oo0OO$o00oo0O0;)Lo0OOo00/o00oo0OO$o00oo0OO;
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0O:Lo0OOo00/o00oo0OO$o00oo0O0;

    return-object p0
.end method

.method public o00oOo00(Lo0OOo00/o00ooO;)Lo0OOo00/o00oo0OO$o00oo0OO;
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0o:Lo0OOo00/o00ooO;

    return-object p0
.end method

.method public o00oOo0O(Ljava/net/Socket;Ljava/lang/String;Lokio/o00oOoO;Lokio/o00oOo0O;)Lo0OOo00/o00oo0OO$o00oo0OO;
    .locals 0

    iput-object p1, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOOo0:Ljava/net/Socket;

    iput-object p2, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOOoO:Ljava/lang/String;

    iput-object p3, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo00:Lokio/o00oOoO;

    iput-object p4, p0, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOooO:Lokio/o00oOo0O;

    return-object p0
.end method

.method public o00oOooO(Ljava/net/Socket;)Lo0OOo00/o00oo0OO$o00oo0OO;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p1}, Ljava/net/Socket;->getRemoteSocketAddress()Ljava/net/SocketAddress;

    move-result-object v0

    check-cast v0, Ljava/net/InetSocketAddress;

    invoke-virtual {v0}, Ljava/net/InetSocketAddress;->getHostName()Ljava/lang/String;

    move-result-object v0

    invoke-static {p1}, Lokio/o0O00000;->o00oo0(Ljava/net/Socket;)Lokio/o0OoO00O;

    move-result-object v1

    new-instance v2, Lokio/o0OoOoOo;

    invoke-direct {v2, v1}, Lokio/o0OoOoOo;-><init>(Lokio/o0OoO00O;)V

    invoke-static {p1}, Lokio/o0O00000;->o00oOoOO(Ljava/net/Socket;)Lokio/o0O00O0;

    move-result-object v1

    new-instance v3, Lokio/o0O000O;

    invoke-direct {v3, v1}, Lokio/o0O000O;-><init>(Lokio/o0O00O0;)V

    invoke-virtual {p0, p1, v0, v2, v3}, Lo0OOo00/o00oo0OO$o00oo0OO;->o00oOo0O(Ljava/net/Socket;Ljava/lang/String;Lokio/o00oOoO;Lokio/o00oOo0O;)Lo0OOo00/o00oo0OO$o00oo0OO;

    move-result-object p1

    return-object p1
.end method
